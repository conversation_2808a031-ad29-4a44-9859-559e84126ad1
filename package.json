{"name": "marketplace", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "dev:emails": "concurrently \"next dev\" \"npm run emails\"", "emails": "cp .env ./node_modules/react-email/dist/preview && email dev --port 3001 --dir ./src/emails", "dev:turbo": "next dev --turbo", "build": "next build", "build:email": "email build", "start": "next start", "export:email": "email export", "lint": "next lint", "migrate": "tsx src/scripts/run-migrations.ts", "migrate:create": "node src/scripts/create-migration.mjs", "env:update": "vercel env pull --environment development .env.vercel.dev && vercel env pull --environment preview .env.vercel.prev && vercel env pull --environment production .env.vercel.prod"}, "dependencies": {"@aws-sdk/client-s3": "^3.635.0", "@aws-sdk/s3-request-presigner": "^3.635.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@ffmpeg/core": "^0.12.4", "@ffmpeg/ffmpeg": "^0.12.7", "@ffmpeg/util": "^0.12.1", "@heroicons/react": "^2.1.4", "@hookform/resolvers": "^3.10.0", "@lottiefiles/react-lottie-player": "^3.5.4", "@mailchimp/mailchimp_transactional": "^1.0.59", "@next/third-parties": "^15.0.3", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.1.8", "@react-email/components": "^0.1.1", "@svgr/webpack": "8.1.0", "@tanstack/react-query": "^5.48.0", "@tanstack/react-query-devtools": "^5.48.0", "@tanstack/react-query-next-experimental": "^5.48.0", "@tanstack/react-table": "^8.20.6", "@vercel/analytics": "^1.3.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "embla-carousel-react": "^8.5.1", "google-auth-library": "^9.11.0", "hashids": "^2.3.0", "ioredis": "5.4.1", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lucide-react": "^0.468.0", "nanoid": "^5.0.9", "next": "^15.1.2", "next-auth": "^4.24.7", "nextjs": "^0.0.3", "prettier": "^3.3.2", "prettier-eslint": "^16.3.0", "pretty-bytes": "^6.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "14.2.3", "react-hook-form": "^7.54.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.15.1", "react-markdown": "^9.0.1", "resend": "^4.5.2", "sharp": "^0.33.4", "slugify": "1.6.6", "stripe": "^17.7.0", "surrealdb": "^1.1.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "winston": "^3.13.1", "zod": "^3.24.2", "zustand": "^5.0.6"}, "devDependencies": {"@hookform/devtools": "^4.3.3", "@types/jsonwebtoken": "^9.0.6", "@types/lodash": "^4.17.7", "@types/mailchimp__mailchimp_transactional": "^1.0.10", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "concurrently": "^9.2.0", "eslint": "^8", "eslint-config-next": "^14.2.13", "postcss": "^8", "react-email": "^4.0.17", "tailwindcss": "^3.4.1", "tsx": "^4.20.3", "typescript": "^5"}}