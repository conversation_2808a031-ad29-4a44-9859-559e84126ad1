/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone',
  turbopack: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },
  // webpack(config, { buildId, dev, isServer, defaultLoaders, webpack }) {
  //   config.module.rules.push({
  //     test: /\.svg$/i,
  //     issuer: /\.[jt]sx?$/,
  //     use: ['@svgr/webpack'],
  //   })

  //   return config
  // },
  serverExternalPackages: [
    'sequelize',
    '@aws-sdk/client-s3',
    '@aws-sdk/s3-request-presigner',
  ],
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: `templace-files*.amazonaws.com`,
        port: '',
        pathname: '/files/**/*',
      },
      {
        protocol: 'https',
        hostname: `templace-images*.amazonaws.com`,
        port: '',
        pathname: '/images/**/*',
      },
      {
        protocol: 'https',
        hostname: `templace-products-images*.amazonaws.com`,
        port: '',
        pathname: '/images/**/*',
      },
      {
        protocol: 'https',
        hostname: `templace-automation*.amazonaws.com`,
        port: '',
        pathname: '/**/*',
      },
      {
        protocol: 'https',
        hostname: 'www.gravatar.com',
        port: '',
        pathname: '/avatar/**',
      },
      {
        protocol: 'https',
        hostname: '*.googleusercontent.com',
        port: '',
        pathname: '/*/**',
      },
    ],
  },
  // make sure that robots will not index preview pages
  async headers() {
    const headers = []

    // Headers for preview pages
    if (process.env.NEXT_PUBLIC_VERCEL_ENV === 'preview') {
      headers.push({
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'noindex',
          },
        ],
        source: '/:path*',
      })
    }

    return headers
  },
  experimental: {
    serverActions: {
      bodySizeLimit: '128mb',
    },
  },
}

export default nextConfig
