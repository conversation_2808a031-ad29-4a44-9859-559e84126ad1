'use client'
import React from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryStreamedHydration } from '@tanstack/react-query-next-experimental'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'

function makeQueryClient(staleTime) {
  return new QueryClient({
    defaultOptions: {
      queries: {
        staleTime,
      },
    },
  })
}

let browserQueryClient = undefined

function getQueryClient() {
  // return makeQueryClient();
  if (typeof window === 'undefined') {
    return makeQueryClient(60 * 1000) // 60s for SSR
  } else {
    if (!browserQueryClient) browserQueryClient = makeQueryClient(0)
    return browserQueryClient
  }
}

export default function ReactQueryProvider(props) {
  const queryClient = getQueryClient()
  return (
    <QueryClientProvider client={queryClient}>
      <ReactQueryStreamedHydration>
        {props.children}
        <ReactQueryDevtools />
      </ReactQueryStreamedHydration>
    </QueryClientProvider>
  )
}
