import { Button, Code, Layout, Paragraph } from '@/components/email'

type VerifyIdentityProps = {
  confirmationUrl?: string
  confirmationCode: string
}

export default function VerifyIdentity({
  confirmationUrl,
  confirmationCode,
}: VerifyIdentityProps) {
  if (confirmationUrl) {
    return (
      <Layout
        title="Verify you account"
        preview="Confirm that this is your email address to secure your account by clicking the link below:"
      >
        <Paragraph>
          Confirm that this is your email address to secure your account by
          clicking the link below:
        </Paragraph>
        <Button href={confirmationUrl}>Click to log in</Button>
        <Paragraph>Or, copy and paste this temporary login code:</Paragraph>
        <Code loginCode={confirmationCode} />
      </Layout>
    )
  }

  return (
    <Layout
      title="Verify you account"
      preview={`Copy and paste this verification code into your authentication page: ${confirmationCode}`}
    >
      <Paragraph>
        Copy and paste this verification code into your authentication page:
      </Paragraph>
      <Code loginCode={confirmationCode} />
    </Layout>
  )
}

VerifyIdentity.PreviewProps = {
  confirmationCode: '123456',
}
