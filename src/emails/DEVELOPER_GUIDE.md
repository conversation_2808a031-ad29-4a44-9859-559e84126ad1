# Developer Guide - Email System

## Overview

This project uses **React Email** with **Resend** as the primary email service provider, and **Mailchimp/Mandrill** for transactional templates. The email system is built with reusable components and follows a consistent pattern for creating and sending emails.

## Architecture

### Email Service Providers

- **Resend**: Primary service for React Email templates (modern approach)
- **Mailchimp/Mandrill**: Legacy transactional email templates
- **Mailchimp**: Mailing list management

### File Structure

```
src/emails/
├── components/           # Shared email components
├── confirm-purchase.tsx  # Purchase confirmation email
├── sale-notification.tsx # Seller notification email
├── transfer-status.tsx   # Transfer status updates
├── verify-identity.tsx   # Identity verification email
└── actions/mailing/      # Email sending logic
```

## Environment Variables

```env
RESEND_API_KEY=your_resend_api_key
MANDRIL_API_KEY=your_mandrill_api_key
MAILCHIMP_API_KEY=**********************
MAILCHIMP_AUDIENCE_ID=your_audience_id
HOST=https://your-domain.com
```

## Creating Email Templates

### 1. Basic Template Structure

```tsx
import { Layout, Paragraph, Button } from '@/components/email'

type YourEmailProps = {
  userName: string
  // ... other props
}

export default function YourEmail({ userName }: YourEmailProps) {
  return (
    <Layout
      title="Your Email Title"
      preview="Preview text that appears in email clients"
    >
      <Paragraph>Hello {userName},</Paragraph>
      <Paragraph>Your email content here...</Paragraph>
      <Button href="https://example.com">Call to Action</Button>
    </Layout>
  )
}

// Always include PreviewProps for development/testing
YourEmail.PreviewProps = {
  userName: 'John Doe',
} as YourEmailProps
```

### 2. Available Email Components

#### Layout Component
```tsx
<Layout
  title="Email Title"           // Can be string or JSX
  preview="Preview text"        // Shows in email client preview
>
  {/* Email content */}
</Layout>
```

#### Text Components
```tsx
<Paragraph>Regular paragraph text</Paragraph>
<Button href="https://example.com">Button Text</Button>
<Code loginCode="123456" />
```

#### React Email Components
```tsx
import { 
  Heading, 
  Hr, 
  Img, 
  Section, 
  Text, 
  Row, 
  Column 
} from '@react-email/components'
```

### 3. Styling

Use inline styles or style objects:

```tsx
const styles = {
  container: {
    backgroundColor: '#ffffff',
    padding: '20px',
  },
  text: {
    fontSize: '16px',
    lineHeight: '24px',
    color: '#333333',
  }
}

<Section style={styles.container}>
  <Text style={styles.text}>Styled content</Text>
</Section>
```

## Sending Emails

### Using Resend (Recommended)

```tsx
import { Resend } from 'resend'
import YourEmail from '@/emails/your-email'

const resend = new Resend(RESEND_API_KEY!)

await resend.emails.send({
  from: 'Templace <<EMAIL>>',
  to: '<EMAIL>',
  subject: 'Your Subject',
  react: YourEmail({
    userName: 'John',
    // ... other props
  }),
})
```

### Using Mandrill Templates

```tsx
import { sendTemplate } from '@/actions/mailing'

await sendTemplate({
  destEmail: '<EMAIL>',
  destName: 'John Doe',
  templateId: 'your-template-id',
  subject: 'Your Subject',
  vars: [
    { name: 'USERNAME', content: 'John' },
    { name: 'PRODUCT_NAME', content: 'Product' }
  ]
})
```

## Email Actions

### Existing Actions

1. **sendPurchaseComplete**: Sends confirmation to buyer and notification to seller
2. **sendRedeemComplete**: Sends redeem confirmation
3. **sendTransferStatus**: Sends transfer status updates
4. **saveMailing**: Adds email to Mailchimp mailing list

### Creating New Actions

```tsx
'use server'

import { RESEND_API_KEY } from '@/config'
import { Resend } from 'resend'
import YourEmail from '@/emails/your-email'

export async function sendYourEmail(emailData: any) {
  const resend = new Resend(RESEND_API_KEY!)

  try {
    const response = await resend.emails.send({
      from: 'Templace <<EMAIL>>',
      to: emailData.email,
      subject: 'Your Subject',
      react: YourEmail({
        userName: emailData.name,
        // ... other props
      }),
    })

    if (!response || !response.data) {
      return { success: false, error: 'Error sending email.' }
    }

    return { success: true }
  } catch (error) {
    return { success: false, error: String(error) }
  }
}
```

## Testing Emails

### Development Testing

1. **React Email CLI**: Use `npm run dev:emails` to preview emails (runs both Next.js and email preview)
2. **Email-only preview**: Use `npm run emails` to run just the email preview server
3. **PreviewProps**: Always define preview props for testing
4. **Resend Dashboard**: Check delivery status and logs

### Email Client Testing

Test emails across different clients:
- Gmail (Desktop/Mobile)
- Outlook (Desktop/Mobile)
- Apple Mail
- Yahoo Mail

## Best Practices

### 1. Responsive Design
```tsx
// Use flexible layouts
<Row>
  <Column style={{ width: '48%' }}>Content 1</Column>
  <Column style={{ width: '48%' }}>Content 2</Column>
</Row>
```

### 2. Accessibility
```tsx
// Use proper heading hierarchy
<Heading as="h1">Main Title</Heading>
<Heading as="h2">Subtitle</Heading>

// Alt text for images
<Img src="image.jpg" alt="Descriptive text" />
```

### 3. Performance
- Optimize images (use CDN)
- Keep CSS inline
- Minimize external dependencies

### 4. Content Guidelines
- Clear, concise copy
- Strong call-to-action
- Mobile-first approach
- Consistent branding

## Common Patterns

### Status Updates
```tsx
const statusConfig = {
  pending: { color: '#FBBF24', text: 'Pending' },
  complete: { color: '#03A868', text: 'Complete' },
  // ... other statuses
}

<Text style={{ color: statusConfig[status].color }}>
  {statusConfig[status].text}
</Text>
```

### Dynamic Content Lists
```tsx
const items = [
  { title: 'Item 1', description: 'Description 1' },
  { title: 'Item 2', description: 'Description 2' },
]

{items.map((item, index) => (
  <Section key={index}>
    <Text>{item.title}: {item.description}</Text>
  </Section>
))}
```

### Conditional Rendering
```tsx
{userName && <Paragraph>Hello {userName}!</Paragraph>}
{!userName && <Paragraph>Hello there!</Paragraph>}
```

## Error Handling

### Email Sending Errors
```tsx
try {
  const result = await sendEmail(data)
  if (!result.success) {
    logger.error(`Email failed: ${result.error}`)
    // Handle error appropriately
  }
} catch (error) {
  logger.error(`Email system error: ${error}`)
  // Fallback or retry logic
}
```

### Template Errors
- Always provide fallback content
- Validate props before rendering
- Use TypeScript for type safety

## Utilities

### Available Utilities
- `centsToDollars()`: Convert cents to dollar format
- `getPlatform()`: Extract platform from platforms array
- `buildName()`: Construct full name from first/last
- `maskEmail()`: Mask email for logging
- `objectToTemplateContent()`: Convert object to Mandrill template vars

### Date Formatting
```tsx
import { format } from 'date-fns'

// Format examples
format(date, 'PPPPp')      // "Sunday, April 12th, 2020 at 2:20 PM"
format(date, 'MMMM d, yyyy') // "April 12, 2020"
```

## Migration Guide

### From Mandrill to Resend

1. Create React Email component
2. Replace `sendTemplate` with `resend.emails.send`
3. Update props to match React component
4. Test thoroughly across email clients

### Adding New Email Types

1. Create template in `src/emails/`
2. Add sending action in `src/actions/mailing/`
3. Export from `src/actions/mailing/index.ts`
4. Update TypeScript types if needed

## Troubleshooting

### Common Issues

1. **Images not loading**: Ensure absolute URLs and proper hosting
2. **Styling issues**: Use inline styles, avoid external CSS
3. **Delivery issues**: Check spam folders, verify sender domain
4. **Template errors**: Validate all props and provide defaults

### Debugging

```tsx
// Add logging for debugging
console.log('Email data:', emailData)
console.log('Resend response:', response)
```

### Production Monitoring

- Monitor delivery rates in Resend dashboard
- Set up error alerts for failed sends
- Track email engagement metrics