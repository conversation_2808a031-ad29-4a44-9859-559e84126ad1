import { Column, Hr, Row, Section } from '@react-email/components'
import { Button, Layout, Paragraph } from '@/components/email'

type ConfirmPurchaseProps = {
  transferUrl: string
  confirmationCode: string
  email: string
  total: string
  date: string
  product: string
  platform: string
  createdBy: string
}

type ItemProps = {
  title: string
  description: string
}

function Item({ title, description }: ItemProps) {
  return (
    <Section style={{ marginBottom: 20 }}>
      <Row>
        <Paragraph style={{ margin: 0, fontSize: 12, fontWeight: 700 }}>
          {title}
        </Paragraph>
      </Row>
      <Row>
        <Paragraph style={{ margin: 0, fontSize: 14, fontWeight: 500 }}>
          {description}
        </Paragraph>
      </Row>
    </Section>
  )
}

export default function ConfirmPurchase({
  transferUrl,
  confirmationCode,
  email,
  total,
  date,
  product,
  platform,
  createdBy,
}: ConfirmPurchaseProps) {
  return (
    <Layout
      title="Thanks for your purchase"
      preview="We have initiated the transfer of your purchase. You can track the status of the transfer on the purchase page. We will notify you once the transfer is complete."
    >
      <Paragraph>
        Transfers are currently processed manually, and your template will be
        added to your Webflow account within 24 hours.
      </Paragraph>

      <Hr style={separator} />

      <Row>
        <Column style={{ width: '48%' }}>
          <Item title="Confirmation:" description={confirmationCode} />
          <Item title="Email:" description={email} />
          <Item title="Total:" description={total} />
        </Column>
        <Column style={{ width: '50%', marginLeft: '2%' }}>
          <Item title="Date:" description={date} />
          <Item title="Product:" description={product} />
          <Item title="Platform:" description={platform} />
        </Column>
      </Row>

      <Hr style={separator} />

      <Paragraph>
        If you have any questions or need assistance, please reach out to our
        support team at: <strong><EMAIL></strong>
      </Paragraph>

      <Button href={transferUrl}>Start Transfer</Button>
    </Layout>
  )
}

ConfirmPurchase.PreviewProps = {
  confirmationCode: 'CHMZ097563$SDF',
  transferUrl: '',
  email: '<EMAIL>',
  total: 'Free',
  date: 'June 29th, 2025',
  product: 'Dang',
  platform: 'webflow',
  createdBy: '',
}

const separator = {
  borderTop: '1px solid #E5E7EB',
  marginTop: '20px',
  marginBottom: '20px',
}
