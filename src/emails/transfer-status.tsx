import { Heading, Hr, Row, Section, Text } from '@react-email/components'
import { Button, Layout, Paragraph } from '@/components/email'

type TransferStatusProps = {
  userName?: string
  status: 'pending' | 'canceled' | 'initiated' | 'inProgress' | 'complete'
  template: string
}

type TitleProps = {
  status: string
  color: string
}

function Title({ status, color }: TitleProps) {
  return (
    <>
      <Heading style={h1}>Transfer Status</Heading>
      <Text style={{ ...common, color: color }}>{status}</Text>
    </>
  )
}

export default function TransferStatus({
  userName,
  status,
  template,
}: TransferStatusProps) {
  const button =
    status === 'complete'
      ? { href: 'https://tem.place/account', text: 'Go to my account' }
      : { href: 'https://tem.place/status', text: 'Track status' }

  const statusText = {
    complete: {
      title: 'Completed',
      color: '#03A868',
      content: {
        text: `We’re pleased to inform you that the transfer of your purchase (${template}) has been successfully completed! The template has been transferred to your account.`,
        whatToExpect: [
          {
            text: 'You can now access and start using your new template in your Webflow account.',
          },
          {
            text: 'If you encounter any issues or have any questions, our support team is here to help. Contact <NAME_EMAIL>',
          },
        ],
        extra:
          'Thank you for choosing Templace. We look forward to seeing the amazing things you’ll create with your new template.',
      },
    },
    pending: {
      title: 'Pending',
      color: '#FBBF24',
      content: {
        text: `We want to let you know that your purchase (${template}) transfer request is now pending. Our team is preparing your template for transfer from our account to yours.`,
        whatToExpect: [
          { text: 'Our system is initiating the transfer process.' },
          {
            text: 'You will receive another email once the transfer is in progress.',
          },
        ],
        extra: null,
      },
    },
    canceled: {
      title: 'Canceled',
      color: '#F87171',
      content: {
        text: `We regret to inform you that the transfer of your purchase (${template}) has been canceled. This could be due to several reasons, such as a technical issue or a cancellation request.`,
        whatToExpect: [
          {
            text: 'If you did not request this cancellation, please contact our support <NAME_EMAIL> for assistance..',
          },
          {
            text: 'You can initiate a new transfer request from your Templace account.',
          },
        ],
        extra:
          "We apologize for any inconvenience this may have caused. If you have any questions, please don't hesitate to reach out.",
      },
    },
    initiated: {
      title: 'Initiated',
      color: '#38BDF8',
      content: {
        text: `We want to let you know that your purchase (${template}) transfer request has been initiated. Our team is preparing your template for transfer from our account to yours.`,
        whatToExpect: [
          { text: 'Our system is initiating the transfer process.' },
          {
            text: 'You will receive another email once the transfer is in progress.',
          },
        ],
        extra: null,
      },
    },
    inProgress: {
      title: 'In Progress',
      color: '#6366F1',
      content: {
        text: `Great news! The transfer of your purchase (${template}) is now in progress. Our system is currently transferring the template from our account to yours.`,
        whatToExpect: [
          { text: 'The transfer process may take a few minutes to complete.' },
          {
            text: 'You will receive a confirmation email once the transfer is completed.',
          },
        ],
        extra:
          'If you have any questions or need assistance, please reach out to our support team at: <EMAIL>',
      },
    },
  }

  return (
    <Layout
      title={
        <Title
          status={statusText[status].title}
          color={statusText[status].color}
        />
      }
      preview="Your template transfer status has changed."
    >
      <Paragraph>{userName ? `Hi ${userName},` : 'Hello,'}</Paragraph>
      <Paragraph>{statusText[status].content.text}</Paragraph>
      <Paragraph style={{ fontWeight: 'bold' }}>What to expect next:</Paragraph>
      <Section>
        {statusText[status].content.whatToExpect.map((item, index) => (
          <Row key={index}>
            <div style={ball} />
            <Paragraph style={listItem}>{item.text}</Paragraph>
          </Row>
        ))}
      </Section>

      <Hr style={separator} />

      {statusText[status].content.extra && (
        <Paragraph style={{ fontWeight: 'bold' }}>
          {statusText[status].content.extra}
        </Paragraph>
      )}

      <Button href={button.href}>{button.text}</Button>
    </Layout>
  )
}

TransferStatus.PreviewProps = {
  userName: 'John Doe',
  status: 'inProgress',
  template: 'Bobs Webflow template',
} as TransferStatusProps

const common = {
  fontSize: '32px',
  fontWeight: 'bold',
  lineHeight: '40px',
  margin: 0,
  padding: 0,
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  textAlign: 'center' as const,
  letterSpacing: '-0.35px',
}

const h1 = {
  ...common,
  color: '#000',
}

const ball = {
  width: '6px',
  height: '6px',
  borderRadius: '3px',
  backgroundColor: '#6B7280',
  marginLeft: '2px',
  marginRight: '8px',
  marginBottom: '3px',
  display: 'inline-block',
}

const listItem = {
  margin: '0px!important',
  padding: '0px!important',
  display: 'inline',
}

const separator = {
  borderTop: '1px solid #E5E7EB',
  marginTop: '20px',
  marginBottom: '20px',
}
