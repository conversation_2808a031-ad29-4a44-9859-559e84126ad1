# Email Guide for CEO & Designer

## What is React Email?

React Email is a modern way to create beautiful, responsive emails that look great on all devices. Think of it like building a website, but specifically designed for email clients. Instead of writing complicated email code, we use components (like building blocks) to create professional emails.

## How Our Email System Works

### The Simple Version
1. **Design**: We create email templates using pre-built components
2. **Content**: We fill templates with dynamic content (names, products, etc.)
3. **Send**: We use Resend to deliver emails to customers
4. **Track**: We monitor delivery and engagement

### Our Email Service (Resend)
- **Reliable delivery**: Ensures emails reach inboxes, not spam folders
- **Analytics**: Shows open rates, click rates, and delivery status
- **Professional**: Emails come from our domain (@tem.place)
- **Fast**: Emails are delivered within seconds

## Current Email Types

### 1. Purchase Confirmation (`confirm-purchase.tsx`)
**When it's sent**: After a customer buys a template
**Purpose**: Confirm purchase and provide transfer instructions
**Key elements**:
- Purchase details (product, price, date)
- Transfer button
- Confirmation code
- Support contact

### 2. Sale Notification (`sale-notification.tsx`)
**When it's sent**: When a seller makes a sale
**Purpose**: Notify sellers about their earnings
**Key elements**:
- Congratulations message
- Sale details
- Buyer information
- Link to dashboard

### 3. Transfer Status (`transfer-status.tsx`)
**When it's sent**: When transfer status changes
**Purpose**: Keep buyers informed about their purchase
**Key elements**:
- Status indicator (color-coded)
- Next steps
- Progress information
- Action buttons

### 4. Identity Verification (`verify-identity.tsx`)
**When it's sent**: During login/registration
**Purpose**: Verify user email addresses
**Key elements**:
- Verification code
- Login link
- Security message

## Email Design System

### Brand Colors
- **Success**: #03A868 (green) - completed actions
- **Warning**: #FBBF24 (yellow) - pending actions
- **Error**: #F87171 (red) - failed actions
- **Info**: #38BDF8 (blue) - informational
- **Primary**: #6366F1 (purple) - main actions

### Typography
- **Headings**: Bold, clear hierarchy
- **Body text**: Easy to read, proper line spacing
- **Buttons**: High contrast, clear action words

### Layout Principles
- **Mobile-first**: Looks great on phones
- **Scannable**: Easy to quickly understand
- **Actionable**: Clear next steps
- **Branded**: Consistent with Templace design

## How to Edit Emails Yourself

### Prerequisites - What You Need to Install

Before you can edit emails, you'll need to install these tools on your computer:

#### Required Software
1. **Node.js** (version 16 or higher)
   - Download from: https://nodejs.org/
   - This includes NPM (Node Package Manager)
   - To check if installed: `node --version`

2. **Git** (version control)
   - Download from: https://git-scm.com/
   - To check if installed: `git --version`

3. **Code Editor** (recommended)
   - **VS Code**: https://code.visualstudio.com/ (most popular)
   - **Cursor**: https://www.cursor.com/ (AI-powered editor)
   - **Trae**: https://www.trae.ai/ (AI-powered editor)

#### Optional but Helpful
- **GitHub Desktop**: https://desktop.github.com/ (visual Git interface)
- **Yarn**: `npm install -g yarn` (alternative to NPM)

### Getting Started - Setting Up Your Environment

#### Step 1: Clone the Repository
```bash
git clone https://github.com/joaopaulots/dsm-marketplace.git
cd dsm-marketplace
```

#### Step 2: Install Dependencies
```bash
npm install
# or
yarn install
```

#### Step 3: Set Up Environment Variables
Create a `.env.local` file in the root directory and add:
```env
RESEND_API_KEY=your_resend_api_key
HOST=http://localhost:3000
```

#### Step 4: Start the Development Server with Email Preview
```bash
npm run dev:emails
# or
yarn dev:emails
```

**Why use `dev:emails`?**
This command runs both the main Next.js application (`npm run dev`) and the email preview server (`npm run emails`) at the same time. This gives you:
- **Main app**: Available at `http://localhost:3000`
- **Email preview**: Available at `http://localhost:3001`
- **Live updates**: Both update automatically when you make changes

**Alternative: Email-only preview**
If you only want to work on emails without the main app:
```bash
npm run emails
# or
yarn emails
```

### Creating a New Email Template

#### Step 1: Create the Email File
1. Navigate to `src/emails/`
2. Create a new file: `your-email-name.tsx`
3. Copy this template structure:

```tsx
import { Layout, Paragraph, Button } from '@/components/email'

type YourEmailProps = {
  userName: string
  // Add other props you need
}

export default function YourEmail({ userName }: YourEmailProps) {
  return (
    <Layout
      title="Your Email Title"
      preview="Preview text that appears in email clients"
    >
      <Paragraph>Hello {userName},</Paragraph>
      <Paragraph>Your email content goes here...</Paragraph>
      <Button href="https://tem.place">Call to Action</Button>
    </Layout>
  )
}

// This is important for testing!
YourEmail.PreviewProps = {
  userName: 'John Doe',
} as YourEmailProps
```

#### Step 2: Preview Your Email
- Open your browser to `http://localhost:3001` (email preview server)
- You'll see your new email in the preview list
- Click to see how it looks

**Note**: Make sure you're running `npm run dev:emails` or `npm run emails` for the preview to work.

### Editing Existing Emails

#### Step 1: Find the Email File
All email templates are in `src/emails/`:
- `confirm-purchase.tsx` - Purchase confirmations
- `sale-notification.tsx` - Seller notifications
- `transfer-status.tsx` - Transfer status updates
- `verify-identity.tsx` - Identity verification

#### Step 2: Make Your Changes
1. Open the email file you want to edit
2. Make your changes to:
   - **Text content**: Edit the text inside `<Paragraph>` tags
   - **Button text**: Change the text inside `<Button>` tags
   - **Colors**: Update the style objects (like `color: '#FF0000'`)
   - **Layout**: Add/remove sections

#### Step 3: Test Your Changes
1. Save the file
2. Check the preview in your browser
3. Make sure it looks good on different screen sizes

### Common Edits You Can Make

#### Changing Text
```tsx
// Before
<Paragraph>Thanks for your purchase!</Paragraph>

// After
<Paragraph>Thank you for choosing Templace!</Paragraph>
```

#### Changing Button Text and Links
```tsx
// Before
<Button href="https://tem.place/dashboard">View Dashboard</Button>

// After
<Button href="https://tem.place/purchases">View My Purchases</Button>
```

#### Adding New Content
```tsx
// Add a new paragraph
<Paragraph>
  Need help? Contact <NAME_EMAIL>
</Paragraph>

// Add a separator line
<Hr style={{ margin: '20px 0' }} />
```

#### Changing Colors
```tsx
// Find the style object (usually at the bottom of the file)
const styles = {
  button: {
    backgroundColor: '#6366F1', // Change this color
    color: '#FFFFFF'
  }
}
```

### Testing Your Changes

#### Visual Testing
1. **Preview in Browser**: Check how it looks
2. **Mobile View**: Resize browser window to test mobile
3. **Dark Mode**: Some email clients have dark themes

#### Send Test Email
1. Find the sending function in `src/actions/mailing/`
2. Add a test function (ask a developer for help with this)
3. Send to your own email to test

### Publishing Your Changes

#### Step 1: Save and Commit
```bash
git add .
git commit -m "Update email template: describe your changes"
```

#### Step 2: Push to Repository
```bash
git push origin main
```

#### Step 3: Deploy
The changes will be automatically deployed when merged to the main branch.

### Getting Help

#### If Something Breaks
1. **Check the browser console** for red error messages
2. **Restart the development server**: Stop with Ctrl+C, then run `npm run dev:emails` again
3. **Check port conflicts**: Make sure ports 3000 and 3001 are available
4. **Ask for help**: Share the error message with the development team

#### If You're Stuck
- **Slack**: #email-questions channel
- **Email**: development team
- **Documentation**: Check the DEVELOPER_GUIDE.md for more technical details

### Tips for Success

1. **Start small**: Make one change at a time
2. **Test frequently**: Check the preview after each change
3. **Keep backups**: Git saves your changes, but you can always undo
4. **Use existing patterns**: Copy from other emails that work well
5. **Ask questions**: It's better to ask than to break something!

## Useful Links & Resources

### Development Tools
- **Node.js**: https://nodejs.org/ - JavaScript runtime
- **Git**: https://git-scm.com/ - Version control system
- **VS Code**: https://code.visualstudio.com/ - Code editor
- **GitHub Desktop**: https://desktop.github.com/ - Visual Git interface

### Email Development
- **React Email**: https://react.email/ - Official React Email documentation
- **Resend**: https://resend.com/docs - Email service documentation
- **React Email Components**: https://react.email/docs/components/button - Available components

### Design Resources
- **Email Design Inspiration**: https://reallygoodemails.com/ - Email design gallery
- **Email Client Testing**: https://www.emailonacid.com/ - Test across email clients
- **Web Safe Colors**: https://websafecolors.info/ - Colors that work in all email clients

### Learning Resources
- **React Email Tutorial**: https://react.email/docs/introduction - Getting started guide
- **Email HTML Best Practices**: https://www.campaignmonitor.com/css/ - Email CSS guide
- **Git Basics**: https://git-scm.com/book/en/v2/Getting-Started-Git-Basics - Learn Git

### Project Specific
- **Repository**: https://github.com/joaopaulots/dsm-marketplace/ - Main project repository
- **Issues**: https://github.com/joaopaulots/dsm-marketplace/issues - Report problems
- **Pull Requests**: https://github.com/joaopaulots/dsm-marketplace/pulls - Submit changes

### Support
- **Templace Website**: https://tem.place/ - Main website
- **Contact**: <EMAIL> - General support
- **Documentation**: Check `DEVELOPER_GUIDE.md` for technical details
