import { HOST } from '@/config'
import { Heading, Hr, Img, Section, Text } from '@react-email/components'
import { Button, Layout, Paragraph } from '@/components/email'

interface SaleNotificationProps {
  userName?: string
  product: {
    productSold: string
    platform: string
    user: string
    purchaseDate: string
    total: string
  }
}

function Title({ isFree }: { isFree?: boolean }) {
  return (
    <>
      <Heading style={title}>Congratulations!</Heading>
      <Text style={title}>
        {isFree
          ? 'Your product has been redeemed!'
          : 'You made a sale on Templace'}
      </Text>
    </>
  )
}

export default function SaleNotification({
  userName,
  product,
}: SaleNotificationProps) {
  const list = [
    { title: 'Product Sold', description: product.productSold },
    { title: 'Platform', description: product.platform },
    { title: 'User', description: product.user },
    { title: 'Purchase Date', description: product.purchaseDate },
    { title: 'Total', description: product.total },
  ]

  return (
    <Layout
      title={<Title isFree={product.total === 'Free'} />}
      preview="Sale Notification"
    >
      <Img src={`${process.env.HOST}/email/banner-sell.png`} style={banner} />
      <Paragraph>{userName ? `Hi ${userName},` : 'Hello,'}</Paragraph>
      <Paragraph>
        Great news! We&apos;re excited to inform you that one of your products
        has been purchased on Templace.
      </Paragraph>
      <Section>
        {list.map((item, index) => (
          <Paragraph key={index} style={{ margin: 0 }}>
            {item.title}: {item.description}
          </Paragraph>
        ))}
      </Section>

      <Hr style={separator} />

      <Paragraph>
        <strong>Important!</strong> We encourage you to provide support to the
        buyer if needed, to ensure they have the best experience with your
        product.
      </Paragraph>

      <Button href={`${HOST}/dashboard`}>Check your earnings</Button>
    </Layout>
  )
}

SaleNotification.PreviewProps = {
  userName: 'John Doe',
  product: {
    productSold: 'Bobs Template',
    platform: 'Webflow',
    user: '@bigball',
    purchaseDate: 'July 30, 2024',
    total: '$129',
  },
} as SaleNotificationProps

const banner = {
  width: '100%',
  height: 'auto',
  marginTop: '48px',
}

const title = {
  color: '#000',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '24px',
  lineHeight: '30px',
  fontWeight: 'bold',
  textAlign: 'center' as const,
  margin: 0,
  padding: 0,
}

const separator = {
  borderTop: '1px solid #E5E7EB',
  marginTop: '20px',
  marginBottom: '20px',
}
