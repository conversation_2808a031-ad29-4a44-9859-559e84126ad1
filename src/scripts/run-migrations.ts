import { Surrealized } from '@/io/surreal'
import fs from 'fs/promises'
import path from 'path'

const MIGRATIONS_DIR = path.resolve(process.cwd(), 'migrations')

async function runMigrations() {
  let surreal

  try {
    console.log('Connecting to SurrealDB...')
    surreal = await Surrealized()
    console.log('Connection successful.')

    console.log('Ensuring migrations table exists...')
    await surreal.query('DEFINE TABLE migrations SCHEMAFULL;')
    await surreal.query('DEFINE FIELD name ON TABLE migrations TYPE string;')
    await surreal.query(
      'DEFINE FIELD executedAt ON TABLE migrations TYPE datetime;',
    )
    console.log('Migrations table ensured.')

    console.log('Reading migration files...')
    const migrationFiles = (await fs.readdir(MIGRATIONS_DIR)).filter(file =>
      file.endsWith('.surql'),
    )
    migrationFiles.sort()
    console.log(`Found ${migrationFiles.length} migration files.`)

    const executedMigrations = await surreal.select('migrations')
    const executedMigrationNames = executedMigrations.map((m: any) => m.name)
    console.log(
      `${executedMigrationNames.length} migrations have already been executed.`,
    )

    for (const file of migrationFiles) {
      if (executedMigrationNames.includes(file)) {
        console.log(`Skipping already executed migration: ${file}`)
        continue
      }

      console.log(`Executing migration: ${file}...`)
      const filePath = path.join(MIGRATIONS_DIR, file)
      const query = await fs.readFile(filePath, 'utf-8')

      await surreal.query(query)

      await surreal.create('migrations', {
        name: file,
        executedAt: new Date(),
      })

      console.log(`Migration ${file} executed and registered successfully.`)
    }

    console.log('All pending migrations have been executed.')
  } catch (error) {
    console.error('An error occurred during the migration process:', error)
  } finally {
    if (surreal) {
      await surreal.close()
      console.log('Connection to SurrealDB closed.')
    }
  }
}

runMigrations()
