import fs from 'fs/promises';
import path from 'path';

function getTimestamp() {
  const now = new Date();
  const year = now.getFullYear();
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const seconds = now.getSeconds().toString().padStart(2, '0');
  return `${year}${month}${day}${hours}${minutes}${seconds}`;
}

async function createMigrationFile() {
  const migrationName = process.argv[2];

  if (!migrationName) {
    console.error('Error: Please provide a name for the migration.');
    console.log('Usage: npm run migrate:create <migration-name>');
    process.exit(1);
  }

  const sanitizedName = migrationName.replace(/\s+/g, '-').toLowerCase();
  const timestamp = getTimestamp();
  const fileName = `${timestamp}-${sanitizedName}.surql`;
  const filePath = path.resolve(process.cwd(), 'migrations', fileName);

  try {
    await fs.writeFile(filePath, '-- Add your SurrealQL migration code here.\n');
    console.log(`Migration created successfully: ${fileName}`);
  } catch (error) {
    console.error('Error creating migration file:', error);
    process.exit(1);
  }
}

createMigrationFile();
