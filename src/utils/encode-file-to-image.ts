type ConvertResult = { imageFile: File; width: number; height: number }

const IMAGE_QUALITY = 0.999

async function toFile(canvas: HTMLCanvasElement, type: string): Promise<File> {
  return new Promise(resolve => {
    canvas.toBlob(
      function (blob: Blob | null) {
        if (!blob) return
        const file = new File(
          [blob],
          `${crypto.randomUUID()}.${type.split('/')[1]}`,
          {
            type,
          },
        )

        resolve(file)
      },
      type,
      IMAGE_QUALITY,
    )
  })
}

async function loadImage(file: File): Promise<HTMLImageElement> {
  return new Promise(resolve => {
    const image = new Image()
    image.onload = () => resolve(image)
    image.src = URL.createObjectURL(file)
  })
}

function get2DContext(width = 256, height = 256) {
  const canvas = Object.assign(document.createElement('canvas'), {
    width,
    height,
  })

  const context = canvas.getContext('2d')
  if (!context?.canvas) {
    throw new Error('Canvas context not accessible')
  }

  return context
}

function supportWebpExport() {
  return (
    (get2DContext(1, 1)?.canvas.toDataURL('image/webp').indexOf('image/webp') ??
      -1) > -1
  )
}

/**
 * Convert a file to an image
 * @param file - The file to convert
 * @returns The image file, width, and height
 */
export async function convertFileToImage(file: File): Promise<ConvertResult> {
  const image = await loadImage(file)

  const context = get2DContext(image.width, image.height)
  context.drawImage(image, 0, 0)

  URL.revokeObjectURL(image.src)

  const imageFile = await toFile(
    context.canvas,
    supportWebpExport() ? 'image/webp' : 'image/jpeg',
  )

  return { imageFile, width: image.width, height: image.height }
}

/**
 * Convert an array of files to an array of images
 * @param files - The files to convert
 * @returns The images
 */
export async function encodeFilesToImages(files: File[]): Promise<File[]> {
  const images = []
  for (const file of files) {
    const { imageFile } = await convertFileToImage(file)

    if (!imageFile) continue
    images.push(imageFile)
  }

  return images
}
