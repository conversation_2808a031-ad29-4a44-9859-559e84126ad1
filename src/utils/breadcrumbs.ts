interface BreadcrumbItem {
  label: string
  href?: string
}

// Route to label mapping
const ROUTE_MAP: Record<string, string> = {
  backoffice: 'Backoffice',
  logs: 'Transaction Logs',
  users: 'Users',
  products: 'Products',
  transfers: 'Transfers',
  sends: 'Sends',
  receives: 'Receives',
  purchases: 'Purchases',
  emails: 'Emails',
}

// Routes that should have clickable links
const LINKABLE_ROUTES = new Set([
  'backoffice',
  'logs',
  'users',
  'products',
  'transfers',
  'sends',
  'receives',
  'purchases',
  'emails',
])

/**
 * Automatically derives breadcrumbs from URL
 * Runs on server - no hydration issues
 */
export function getBreadcrumbsFromPath(pathname: string): BreadcrumbItem[] {
  const segments = pathname.split('/').filter(Boolean)
  const breadcrumbs: BreadcrumbItem[] = []

  // Always starts with Backoffice
  if (segments.length > 0 && segments[0] === 'backoffice') {
    breadcrumbs.push({
      label: 'Backoffice',
      href: '/backoffice',
    })

    // Process remaining segments
    let currentPath = '/backoffice'

    for (let i = 1; i < segments.length; i++) {
      const segment = segments[i]
      currentPath += `/${segment}`

      // If it's a known route, add breadcrumb
      if (ROUTE_MAP[segment]) {
        breadcrumbs.push({
          label: ROUTE_MAP[segment],
          href: LINKABLE_ROUTES.has(segment) ? currentPath : undefined,
        })
      }
      // If it's an ID (usually last segment), add without link
      else if (i === segments.length - 1 && segment.length > 10) {
        breadcrumbs.push({
          label: segment.slice(0, 8) + '...',
          href: undefined,
        })
      }
    }
  }

  return breadcrumbs
}

/**
 * Combines automatic breadcrumbs with manual overrides
 */
export function combineBreadcrumbs(
  pathname: string,
  overrides: BreadcrumbItem[] = [],
): BreadcrumbItem[] {
  const autoBreadcrumbs = getBreadcrumbsFromPath(pathname)

  // If there are no overrides, return the automatic ones
  if (overrides.length === 0) {
    return autoBreadcrumbs
  }

  // If there are overrides, replace from the second item onwards (keep "Backoffice")
  return [
    autoBreadcrumbs[0], // Always keep "Backoffice"
    ...overrides,
  ]
}
