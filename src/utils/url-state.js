import { check, set } from "@/utils/jwt";

async function getStateFromParams(searchParams, extraState = {}) {
  const currentState = await check(searchParams.get("state"));
  // console.log(currentState);

  let nextState = {};
  for (const [key, value] of Object.entries(currentState)) {
    nextState[key] = [
      ...(nextState?.[key] ? nextState[key] : []),
      ...(Array.isArray(value) ? value : [value]),
    ];
  }

  for (const [key, value] of searchParams.entries()) {
    if (key === "state") continue;

    nextState[key] = [
      ...(nextState?.[key] ? nextState[key] : []),
      ...(Array.isArray(value) ? value : [value]),
    ];
  }

  return nextState;
}

export default async function updateUrlState(url, extraState = {}) {
  const urlObject = new URL(url);
  const currentState = await getStateFromParams(
    urlObject.searchParams,
    extraState
  );
  // console.log(currentState);

  // const stateString = await set({ ...currentState });
  // console.log(stateString);

  // const stateObj = await check(stateString);
  // console.log(stateObj);
}
