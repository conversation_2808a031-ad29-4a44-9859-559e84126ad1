'use client'

import { FFmpeg } from '@ffmpeg/ffmpeg'

type ConvertResult = {
  videoFile: File
  duration: number
  width: number
  height: number
}

export async function convertFileToVideo(file: File): Promise<ConvertResult> {
  const ffmpeg = new FFmpeg()
  await ffmpeg.load({
    coreURL: '/ffmpeg-core.js',
    wasmURL: '/ffmpeg-core.wasm',
    workerURL: '/ffmpeg-core.worker.js',
  })

  const inputName = 'input' + file.name.substring(file.name.lastIndexOf('.'))
  const outputName = `output.mp4`

  const fileData = new Uint8Array(await file.arrayBuffer())

  await ffmpeg.writeFile(inputName, fileData)

  await ffmpeg.exec([
    '-i',
    inputName,
    '-c:v',
    'libx264',
    '-preset',
    'fast',
    '-crf',
    '22',
    '-c:a',
    'aac',
    '-b:a',
    '128k',
    outputName,
  ])

  const data = await ffmpeg.readFile(outputName)

  const videoFile = new File([data], outputName, { type: 'video/mp4' })
  await ffmpeg.deleteFile(inputName)
  await ffmpeg.deleteFile(outputName)

  const width = 1280
  const height = 720
  const duration = 0

  return { videoFile, duration, width, height }
}

export async function encodeFilesToVideos(files: File[]): Promise<File[]> {
  const videos = []
  for (const file of files) {
    const { videoFile } = await convertFileToVideo(file)
    if (!videoFile) continue
    videos.push(videoFile)
  }
  return videos
}
