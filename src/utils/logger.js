// import winston from "winston";
// import Transport from "winston-transport";

import { VERCEL_ENV } from '@/config'

const Reset = '\x1b[0m'
const Bright = '\x1b[1m'
const Dim = '\x1b[2m'
const Underscore = '\x1b[4m'
const Blink = '\x1b[5m'
const Reverse = '\x1b[7m'
const Hidden = '\x1b[8m'

const FgBlack = '\x1b[30m'
const FgRed = '\x1b[31m'
const FgGreen = '\x1b[32m'
const FgYellow = '\x1b[33m'
const FgBlue = '\x1b[34m'
const FgMagenta = '\x1b[35m'
const FgCyan = '\x1b[36m'
const FgWhite = '\x1b[37m'

const BgBlack = '\x1b[40m'
const BgRed = '\x1b[41m'
const BgGreen = '\x1b[42m'
const BgYellow = '\x1b[43m'
const BgBlue = '\x1b[44m'
const BgMagenta = '\x1b[45m'
const BgCyan = '\x1b[46m'
const BgWhite = '\x1b[47m'

const Clear = '\x1b[0m'

const Colors = {
  silly: `${Dim}${FgGreen}`,
  trace: `${Dim}${FgCyan}`,
  info: `${FgCyan}`,
  error: `${BgRed}${FgWhite}`,
  warn: `${FgYellow}`,
  verbose: `${FgWhite}`,
}

// class SimpleConsoleTransport extends Transport {
//   constructor() {
//     super();
//     this.isProd = VERCEL_ENV !== "development";
//   }
//   log = (info, callback) => {
//     const { level, message, stack } = info;
//     console.log(
//       `${!this.isProd ? Colors[level] : ""}${String(level).toUpperCase()}\t${message}${!this.isProd ? Clear : ""}`,
//       stack ? `\n${stack}` : ""
//     );
//     if (callback) {
//       callback();
//     }
//   };
// }

// const logger = winston.createLogger({
//   level: VERCEL_ENV !== "development" ? "info" : "silly",
//   transports: [new SimpleConsoleTransport()],
// });

const isPreview = VERCEL_ENV === 'preview'
const isProd = isPreview || VERCEL_ENV !== 'development'

function log(level, ...params) {
  console.log(
    `${!isProd ? Colors[level] : ''}${String(level).toUpperCase()}\t`,
    ...params,
    `${!isProd ? Clear : ''}`,
  )
}

const logger = {
  silly(...params) {
    return log('silly', ...params)
  },
  trace(...params) {
    return log('trace', ...params)
  },
  info(...params) {
    return log('info', ...params)
  },
  warn(...params) {
    return log('warn', ...params)
  },
  error(...params) {
    return log('error', ...params)
  },
  alert(...params) {
    return log('alert', ...params)
  },
  fatal(...params) {
    return log('fatal', ...params)
  },
}

export default logger
