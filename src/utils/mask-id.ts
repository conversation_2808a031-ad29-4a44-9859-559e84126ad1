import { PUBLIC_VERCEL_ENV } from '@/config'
import { StringRecordId, RecordId } from 'surrealdb'

export default function maskId(
  id: RecordId | StringRecordId | string,
  tensor = 4,
) {
  if (PUBLIC_VERCEL_ENV !== 'production') {
    return id
  }

  const rx = new RegExp(`^(.{${tensor}})(.*)(.{${tensor}})(.*)$`)
  if (typeof id === 'object') {
    return `${(id as RecordId)?.tb}:${(id as RecordId)?.id?.toString().replace(rx, '$1****$3$4')}`
  }

  return id?.replace(rx, '$1****$3$4')
}
