// Utility functions for URL cleaning and validation
export const cleanUrl = (value: string): string => {
  if (!value) return value

  let cleanedValue = value

  // Remove @ if present at the beginning
  if (cleanedValue.startsWith('@')) {
    cleanedValue = cleanedValue.slice(1)
  }

  // Remove http:// or https:// if present
  if (
    cleanedValue.startsWith('https://') ||
    cleanedValue.startsWith('http://')
  ) {
    cleanedValue = cleanedValue.replace(/^https?:\/\//, '')
  }

  // Remove www. if present
  if (cleanedValue.startsWith('www.')) {
    cleanedValue = cleanedValue.replace(/^www\./, '')
  }

  // Remove trailing slash if present
  cleanedValue = cleanedValue.replace(/\/$/, '')

  return cleanedValue
}
