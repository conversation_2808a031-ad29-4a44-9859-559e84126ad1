export default function centsToDollars(
  cents: number | bigint | undefined,
  code: string = 'USD',
  includeCents = false,
) {
  const priceString = Number(cents ?? 0) / 100
  const price = Intl.NumberFormat('en-us', {
    style: 'currency',
    currency: code,
    minimumFractionDigits: includeCents ? 2 : 0,
    maximumFractionDigits: includeCents ? 2 : 0,
  })

  return price.format(priceString)
}
