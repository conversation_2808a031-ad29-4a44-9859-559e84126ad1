import {
  Platforms,
  PlatformsSchema,
  ProductPlatformsKeys,
  ProductPlatformsValues,
} from '@/actions/product'

export function getPlatform(
  platforms?: Platforms,
): ProductPlatformsKeys | undefined {
  const platformStrings = Object.keys(platforms ?? {}) as ProductPlatformsKeys[]

  const platformString = platformStrings.find(
    (platform: ProductPlatformsKeys) => {
      return Object.prototype.hasOwnProperty.call(
        PlatformsSchema.shape,
        platform,
      )
    },
  )

  return platformString
}

export function getPlatformValue(
  platforms?: Platforms,
): ProductPlatformsValues {
  const platformKey = getPlatform(platforms)
  if (!platformKey || !platforms) return undefined
  return platforms?.[platformKey as keyof Platforms] ?? undefined
}
