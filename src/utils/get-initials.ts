export function getInitials(firstName?: string, lastName?: string): string {
  let initials = ''
  if (firstName && lastName) {
    initials = `${firstName.slice(0, 1).toUpperCase()}${lastName.slice(0, 1).toUpperCase()}`
  } else if (firstName && !lastName) {
    const names = firstName.split(' ')
    if (names.length > 1) {
      initials = `${names[0].slice(0, 1).toUpperCase()}${names[1].slice(0, 1).toUpperCase()}`
    } else {
      initials = firstName.slice(0, 2).toUpperCase()
    }
  } else if (!firstName && lastName) {
    initials = lastName.slice(0, 2).toUpperCase()
  }

  return initials.padEnd(2, 'UU')
}
