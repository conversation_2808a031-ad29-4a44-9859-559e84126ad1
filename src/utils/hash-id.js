import Hashids from 'hashids'

import { HASHID_SECRET } from '@/config'

const hi = new Hashids(HASHID_SECRET)

function stringToHex(str) {
  return Array.from(str)
    .map(char => char.charCodeAt(0).toString(16).padStart(2, '0'))
    .join('')
}

function hexToString(hex) {
  return hex
    .match(/.{1,2}/g)
    .map(byte => String.fromCharCode(parseInt(byte, 16)))
    .join('')
}

export function encodeId(surrealId) {
  const original = surrealId.slice(0, 6).padStart(6, '0')
  const hex = stringToHex(original)
  const value = hi.encodeHex(hex)
  return value
}

export function decodeId(hashId) {
  const decoded = hi.decodeHex(hashId)
  const hexBack = hexToString(decoded)
  return hexBack
}
