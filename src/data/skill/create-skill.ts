'use server'

import { Surrealized, jsonify, surql } from '@/io/surreal'
import logger from '@/utils/logger'
import maskData from '@/utils/mask-data'

import { type Skill, type SkillInput } from '@/actions/skill'

export async function createSkill(
  data: SkillInput,
): Promise<Skill | undefined> {
  const surreal = await Surrealized()

  try {
    logger.trace(`createSkill(skillData: ${maskData(data)})`)

    const response = await surreal.query<[[Skill]]>(
      surql`
        INSERT INTO Skill {
          created: time::now(),
          updated: time::now(),
          enabled: true,
          name: $name,
          slug: $slug,
          category: $category,
          description: $description,
          icon: $icon,
          color: $color,
          tags: $tags
        };
      `,
      {
        name: data.name,
        slug: data.slug,
        category: data.category,
        description: data.description,
        icon: data.icon,
        color: data.color,
        tags: data.tags ?? [],
      },
    )

    const skill = response?.shift()?.shift()
    return skill ? jsonify<Skill>(skill) : undefined
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
