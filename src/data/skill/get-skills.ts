'use server'

import { Surrealized, jsonify, surql } from '@/io/surreal'
import logger from '@/utils/logger'

import { type Skill, type SkillCategory } from '@/actions/skill'

export async function getSkills(
  category?: SkillCategory,
  enabled?: boolean,
): Promise<Skill[]> {
  const surreal = await Surrealized()

  try {
    logger.trace(`getSkills(category: ${category}, enabled: ${enabled})`)

    let query: any
    const params: Record<string, any> = {}

    // Build query based on filters
    if (category && enabled !== undefined) {
      query = surql`SELECT * FROM Skill WHERE category = $category AND enabled = $enabled ORDER BY name ASC`
      params.category = category
      params.enabled = enabled
    } else if (category) {
      query = surql`SELECT * FROM Skill WHERE category = $category ORDER BY name ASC`
      params.category = category
    } else if (enabled !== undefined) {
      query = surql`SELECT * FROM Skill WHERE enabled = $enabled ORDER BY name ASC`
      params.enabled = enabled
    } else {
      query = surql`SELECT * FROM Skill ORDER BY name ASC`
    }

    const response = await surreal.query<[Skill[]]>(query, params)
    const skills = response?.shift() ?? []

    return skills.map(skill => jsonify<Skill>(skill))
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}

export async function getSkillBySlug(slug: string): Promise<Skill | undefined> {
  const surreal = await Surrealized()

  try {
    logger.trace(`getSkillBySlug(slug: ${slug})`)

    const response = await surreal.query<[[Skill]]>(
      surql`SELECT * FROM Skill WHERE slug = $slug LIMIT 1`,
      { slug },
    )

    const skill = response?.shift()?.shift()
    return skill ? jsonify<Skill>(skill) : undefined
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
