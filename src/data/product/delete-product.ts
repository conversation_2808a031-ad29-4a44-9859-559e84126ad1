'use server'

import { Surrealized, surql } from '@/io/surreal'
import { logAndThrow } from '@/utils'
import { idToRecord } from '@/utils/idToRecord'

import logger from '@/utils/logger'

export async function deleteProduct(productId: string): Promise<string> {
  try {
    const surreal = await Surrealized()
    logger.trace(`deleteProduct(productId:<${productId}>)`)

    await surreal.query(
      surql`
        DELETE type::thing($productId);
      `,
      {
        productId: idToRecord('Product', productId),
      },
    )

    return productId
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
