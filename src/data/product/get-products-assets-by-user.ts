'use server'

import { Product } from '@/actions/product'
import { Surrealized, surql, jsonify } from '@/io/surreal'
import { logAndThrow } from '@/utils'

import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

export async function getProductsAssetsByUser(
  userId: string,
  limit: number = 0,
  offset: number = 0,
): Promise<Product[] | undefined> {
  const surreal = await Surrealized()

  try {
    logger.trace(`getProductsAssetsByUser(userId:<${maskId(userId)}>)`)

    const response = await surreal.query<[Product[]]>(
      surql`
        SELECT * FROM Product
          WHERE owner = type::thing($userId)
            AND platforms.bundle == NONE
            AND platforms.subscription == NONE
            AND status == 'ProductStatus/PUBLISHED'
          ORDER BY created DESC
          LIMIT $limit
          START $offset
          FETCH
            owner,
            imageSet,
            imageSet.images,
            fileSet,
            fileSet.files,
            price,
            price.paymentMethod,
            platforms.bundle.assets,
            platforms.subscription.assets
        ;
      `,
      {
        userId,
        limit,
        offset,
      },
    )

    const output = response?.shift()
    return output ? jsonify<Product[]>(output) : undefined
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
