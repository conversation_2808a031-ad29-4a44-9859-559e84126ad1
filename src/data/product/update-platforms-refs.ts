'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'

import logger from '@/utils/logger'
import { type Product } from '@/actions/product'
import { idToRecord } from '@/utils/idToRecord'
import { logAndThrow } from '@/utils'

export async function updatePlatformsRefs(
  productId: Product['id'],
): Promise<Product | undefined> {
  const surreal = await Surrealized()

  try {
    logger.trace(`updatePlatforms(productId: ${productId})`)

    const response = await surreal.query<[[Product]]>(
      surql`
        UPDATE type::thing($productId) SET
            platforms.bundle.assets = (
              IF platforms.bundle.assets IS NOT NONE
                THEN
                  array::map(platforms.bundle.assets, |$i| type::thing($i))
                ELSE
                  NONE
              END
            ),

            platforms.subscription.assets = (
              IF platforms.subscription.assets IS NOT NONE
                THEN
                  array::map(platforms.subscription.assets, |$i| type::thing($i))
                ELSE
                  NONE
              END
            )
        ;
      `,
      {
        productId: idToRecord('Product', String(productId)),
      },
    )

    const product = response?.pop()?.pop()
    return product ? jsonify<Product>(product) : undefined
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
