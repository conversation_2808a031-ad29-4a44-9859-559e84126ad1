'use server'

import { Surrealized, surql } from '@/io/surreal'
import { logAndThrow } from '@/utils'
import { idToRecord } from '@/utils/idToRecord'

import logger from '@/utils/logger'

export async function disableProduct(productId: string): Promise<string> {
  try {
    const surreal = await Surrealized()
    logger.trace(`disableProduct(productId:<${productId}>)`)

    await surreal.query(
      surql`
        UPDATE type::thing($productId) SET enabled = false;
      `,
      {
        productId: idToRecord('Product', productId),
      },
    )

    return productId
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
