'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'
import { logAndThrow } from '@/utils'

import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

export async function countFramerRemixUrl(remixUrl: string): Promise<number> {
  const surreal = await Surrealized()

  try {
    logger.trace(`countFramerRemixUrl(remixUrl:<${maskId(remixUrl)}>)`)

    const response = await surreal.query<[{ total: number }]>(
      surql`
        SELECT count() as total FROM Product WHERE platforms.framer.remixUrl = $remixUrl;
      `,
      {
        remixUrl,
      },
    )

    const total = response?.shift()?.total ?? 0
    return total ? jsonify<number>(total) : 0
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
