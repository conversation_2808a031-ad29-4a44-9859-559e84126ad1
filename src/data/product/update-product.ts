'use server'

import { Surrealized, jsonify, surql } from '@/io/surreal'

import logger from '@/utils/logger'
import maskData from '@/utils/mask-data'

import { type Product } from '@/actions/product'
import { logAndThrow } from '@/utils'
import { idToRecord } from '@/utils/idToRecord'

export async function updateProduct(
  data: Partial<Product>,
): Promise<Product | undefined> {
  const surreal = await Surrealized()

  try {
    logger.trace(`updateProduct(productData: ${maskData(data)})`)

    const response = await surreal.query<[[Product]]>(
      surql`
        UPDATE type::thing($productId) MERGE {
            updated: time::now(),
            enabled: (IF $enabled IS NONE THEN enabled ELSE $enabled END),

            name: (IF $name IS NONE THEN name ELSE $name END),
            slug: (IF $slug IS NONE THEN slug ELSE $slug END),

            owner: (IF $owner IS NONE THEN owner ELSE type::thing($owner) END),
            price: (IF $price IS NONE THEN price ELSE type::thing($price) END),

            status: (IF $status IS NONE THEN status ELSE $status END),
            externalId: (IF $externalId IS NONE THEN externalId ELSE $externalId END),
            description: (IF $description IS NONE THEN description ELSE $description END),
            featured: (IF $featured IS NONE THEN featured ELSE $featured END),
            features: (IF $features IS NONE THEN features ELSE $features END),
            includes: (IF $includes IS NONE THEN includes ELSE $includes END),
            liveUrl: (IF $liveUrl IS NONE THEN liveUrl ELSE $liveUrl END),
            fileSet: (IF $fileSet IS NONE THEN fileSet ELSE type::thing($fileSet) END),
            flair: (IF $flair IS NONE THEN flair ELSE $flair END)
        };

        UPDATE type::thing($productId) SET
            platforms=($platforms OR platforms OR {})
        ;

        SELECT * FROM type::thing($productId) FETCH price;
      `,
      {
        productId: idToRecord('Product', String(data.id)),
        name: data.name,
        enabled: data?.enabled,
        slug: data.slug,
        owner: data.owner,
        price: idToRecord('PaymentPrice', String(data.price?.id)),
        externalId: data.externalId,
        description: data.description,
        featured: data.featured,
        status: data.status,
        features: data.features,
        platforms: data.platforms,
        includes: data.includes,
        liveUrl: data.liveUrl,
        fileSet: data.fileSet,
        flair: data.flair,
      },
    )

    const product = response?.pop()?.pop()
    return product ? jsonify<Product>(product) : undefined
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
