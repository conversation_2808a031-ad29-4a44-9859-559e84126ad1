'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'

import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'
import maskData from '@/utils/mask-data'

import { type Product } from '@/actions/product'

export async function insertProduct(
  userId: string,
  productData: any,
): Promise<Product | undefined> {
  try {
    const surreal = await Surrealized()

    logger.trace(
      `insertProduct(userId:<${maskId(userId)}>, productData: ${maskData(productData)})`,
    )

    const response = await surreal.query<[[Product]]>(
      surql`
      INSERT INTO Product {
        created: time::now(),
        updated: time::now(),
        enabled: $enabled,

        name: $name,
        slug: $slug,

        owner: type::thing($owner),
        price: (IF $price THEN type::thing($price) ELSE NONE END),
        fileSet: (IF $fileSet THEN type::thing($fileSet) ELSE NONE END),
        status: (IF $status THEN $status ELSE 'ProductStatus/INITIAL' END),
        platforms: $platforms,

        externalId: $externalId,
        featured: $featured,
        features: $features,
        includes: $includes,
        liveUrl: $liveUrl,
        flair: $flair
      };
      `,
      {
        enabled: productData.enabled ?? true,
        name: productData.name,
        slug: productData.slug,
        children: productData.children,
        owner: userId,
        price: productData.price,
        externalId: productData.externalId,
        featured: productData.featured ?? false,
        features: productData.features ?? [],
        platforms: productData.platforms ?? {},
        includes: productData.includes ?? [],
        liveUrl: productData.liveUrl,
        flair: productData.flair ?? [],
        status: productData.status,
      },
    )

    const product = response?.shift()?.shift()
    return product ? jsonify<Product>(product) : undefined
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
