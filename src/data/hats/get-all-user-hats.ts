'use server'

import { UserHat } from '@/actions/user'
import { Surrealized, jsonify, surql } from '@/io/surreal'
import { logAndThrow } from '@/utils'
import logger from '@/utils/logger'

export async function getAllUserHats(): Promise<UserHat[] | undefined> {
  const surreal = await Surrealized()

  try {
    logger.trace(`getAllHats()`)

    const response = await surreal.query<[UserHat[]]>(
      surql`
        SELECT * FROM UserHat
      ;`,
    )

    const output = response?.shift()
    return output ? jsonify<UserHat[]>(output) : undefined
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
