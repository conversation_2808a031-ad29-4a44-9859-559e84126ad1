'use server'

import { User } from '@/actions/user'
import { Surrealized, jsonify, surql } from '@/io/surreal'
import { logAndThrow } from '@/utils'
import { idToRecord } from '@/utils/idToRecord'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

export async function updateRelationStatus(
  userId: string,
  newStatus: string,
): Promise<User | undefined> {
  const surreal = await Surrealized()

  try {
    logger.trace(
      `updateRelationStatus(userId:<${maskId(userId)}>, newStatus:<${newStatus}>)`,
    )

    const response = await surreal.query<[User, User]>(
      surql`
        UPDATE User SET relationStatus = $newStatus WHERE id = type::thing($userId);
        SELECT * FROM type::thing($userId);
      `,
      {
        userId: idToRecord('User', userId),
        newStatus,
      },
    )

    const output = response?.pop()
    return output ? jsonify<User>(output) : undefined
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
