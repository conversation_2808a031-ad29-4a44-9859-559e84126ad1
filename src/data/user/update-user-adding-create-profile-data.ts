'use server'

import { User } from '@/actions/user'
import { jsonify, Surrealized } from '@/io/surreal'
import { logAndThrow } from '@/utils'
import { idToRecord } from '@/utils/idToRecord'
import { surql } from '@/utils/surql'
import { getFullAuthSession } from '@/io/next-auth-config'
import { saveSessionV2 } from '@/actions/auth/save-session'

export interface OnboardingUserData {
  userId: string
  name?: string
  handler?: string
  headline?: string
  avatar?: string
}

function splitFullName(fullName: string = '') {
  const parts = fullName.trim().split(' ')
  const firstName = parts[0] || ''
  const lastName = parts.slice(1).join(' ') || ''
  return { firstName, lastName }
}

export async function updateUserAddingCreateProfileData(
  data: OnboardingUserData,
): Promise<User | undefined> {
  const { userId, name = '', handler = '', headline = '', avatar = '' } = data

  if (!userId) throw new Error('userId is required')

  const { firstName, lastName } = splitFullName(name)

  const surreal = await Surrealized()

  try {
    const query = surql`
        UPDATE User SET
          name = $name,
          handler = $handler,
          avatar = $avatar,
          profile = {
            firstName: $firstName,
            lastName: $lastName,
            name: $name,
            headline: $headline,
            permissions: {
              canAddContent: false
            }
          } 

        WHERE id = type::thing($userId);
        SELECT * FROM type::thing($userId);
      `
    const response = await surreal.query<[User, User]>(query, {
      userId: idToRecord('User', userId),
      name,
      handler,
      avatar,
      firstName,
      lastName,
      headline,
    })

    const output = response?.pop()
    const user = output ? jsonify<User>(output) : undefined

    const session = await getFullAuthSession()

    if (session?.providerId && session?.provider && user) {
      await saveSessionV2({
        email: user.email,
        provider: session.provider,
        providerId: session.providerId,
        credentials: {}, // ou recupere se necessário
        meta: {
          ...user,
          providerId: session.providerId,
        },
      })
    }

    return user
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
