'use server'

import { User, UserHat } from '@/actions/user'
import { Surrealized, jsonify, surql } from '@/io/surreal'
import { getIdFromRecord, logAndThrow } from '@/utils'
import { idToRecord } from '@/utils/idToRecord'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

export async function updateHats(
  userId: string,
  newHats: UserHat['id'][],
): Promise<User | undefined> {
  const surreal = await Surrealized()

  try {
    logger.trace(`updateHats(userId:<${maskId(userId)}>, newHats:<${newHats}>)`)

    const response = await surreal.query<[User, User]>(
      surql`
        UPDATE User SET hats = $newHats WHERE id = type::thing($userId);
        SELECT * FROM type::thing($userId);
      `,
      {
        userId: idToRecord('User', userId),
        newHats: newHats.map(id => idToRecord('UserHat', getIdFromRecord(id))),
      },
    )

    const output = response?.pop()
    return output ? jsonify<User>(output) : undefined
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
