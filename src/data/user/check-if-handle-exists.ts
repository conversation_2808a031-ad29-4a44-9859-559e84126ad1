'use server'

import { User } from '@/actions/user'
import { jsonify, Surrealized } from '@/io/surreal'
import { logAndThrow } from '@/utils'
import { surql } from '@/utils/surql'

export interface Props {
  handler: string
}

export async function checkIfHandleExists({
  handler,
}: Props): Promise<User | undefined> {
  if (!handler) throw new Error('handler is required')

  const surreal = await Surrealized()

  try {
    const query = surql`
        SELECT * FROM User WHERE string::lowercase(handler) = string::lowercase($handler);
      `
    const response = await surreal.query<[User, User]>(query, {
      handler,
    })

    const output = response?.pop()
    const user =
      output && Array.isArray(output) && output.length > 0
        ? jsonify<User>(output[0])
        : undefined

    return user
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
