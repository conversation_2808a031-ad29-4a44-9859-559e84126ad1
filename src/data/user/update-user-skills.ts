'use server'

import { Surrealized, jsonify, surql } from '@/io/surreal'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'
import { idToRecord } from '@/utils/idToRecord'

import { type User } from '@/actions/user'

export async function updateUserSkills(
  userId: string,
  skillIds: string[],
): Promise<User | undefined> {
  const surreal = await Surrealized()

  try {
    logger.trace(
      `updateUserSkills(userId: ${maskId(userId)}, skillIds: ${skillIds})`,
    )

    const response = await surreal.query<[[User]]>(
      surql`
        UPDATE type::thing($userId) SET
          profile.skills = $skillIds
        ;
        SELECT * FROM type::thing($userId) FETCH profile;
      `,
      {
        userId: idToRecord('User', userId),
        skillIds,
      },
    )

    const user = response?.shift()?.shift()
    return user ? jsonify<User>(user) : undefined
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}

export async function addUserSkill(
  userId: string,
  skillId: string,
): Promise<User | undefined> {
  const surreal = await Surrealized()

  try {
    logger.trace(`addUserSkill(userId: ${maskId(userId)}, skillId: ${skillId})`)

    const response = await surreal.query<[[User]]>(
      surql`
        UPDATE type::thing($userId) SET
          profile.skills = array::union(profile.skills OR [], [$skillId])
        ;
        SELECT * FROM type::thing($userId) FETCH profile;
      `,
      {
        userId: idToRecord('User', userId),
        skillId,
      },
    )

    const user = response?.shift()?.shift()
    return user ? jsonify<User>(user) : undefined
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}

export async function removeUserSkill(
  userId: string,
  skillId: string,
): Promise<User | undefined> {
  const surreal = await Surrealized()

  try {
    logger.trace(
      `removeUserSkill(userId: ${maskId(userId)}, skillId: ${skillId})`,
    )

    const response = await surreal.query<[[User]]>(
      surql`
        UPDATE type::thing($userId) SET
          profile.skills = array::remove(profile.skills OR [], $skillId)
        ;
        SELECT * FROM type::thing($userId) FETCH profile;
      `,
      {
        userId: idToRecord('User', userId),
        skillId,
      },
    )

    const user = response?.shift()?.shift()
    return user ? jsonify<User>(user) : undefined
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
