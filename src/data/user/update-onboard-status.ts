'use server'

import { User, UserOnboard } from '@/actions/user'
import { Surrealized, jsonify, surql } from '@/io/surreal'
import { logAndThrow } from '@/utils'
import { idToRecord } from '@/utils/idToRecord'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

export async function updateOnboardStatus(
  userId: string,
  newStatus: UserOnboard | string,
): Promise<User | undefined> {
  const surreal = await Surrealized()

  try {
    logger.trace(
      `updateOnboardStatus(userId:<${maskId(userId)}>, newStatus:<${newStatus}>)`,
    )

    // Validate status
    const validStatuses = Object.values(UserOnboard)

    if (!validStatuses.includes(newStatus as UserOnboard)) {
      throw new Error('Invalid onboard status')
    }

    const response = await surreal.query<[User, User]>(
      surql`
        UPDATE User SET onboardStatus = $newStatus WHERE id = type::thing($userId);
        SELECT * FROM type::thing($userId);
      `,
      {
        userId: idToRecord('User', userId),
        newStatus,
      },
    )

    const output = response?.pop()
    return output ? jsonify<User>(output) : undefined
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
