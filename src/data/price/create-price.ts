'use server'

import { Surrealized, jsonify, surql } from '@/io/surreal'
import logger from '@/utils/logger'

import {
  CurrencyCode,
  type PaymentPrice,
  type PaymentPriceInput,
} from '@/actions/price'
import { idToRecord } from '@/utils/idToRecord'
import maskData from '@/utils/mask-data'
import maskId from '@/utils/mask-id'

export async function createPrice(
  userId: string,
  data: PaymentPriceInput,
): Promise<PaymentPrice | undefined> {
  const surreal = await Surrealized()

  try {
    logger.trace(
      `createPaymentPrice(userId: ${maskId(userId)}, paymentPriceData: ${maskData(data)})`,
    )

    const response = await surreal.query<[[PaymentPrice]]>(
      surql`
        INSERT INTO PaymentPrice {
          owner: type::thing($owner),
          paymentMethod: type::thing($paymentMethod),
          amount: $amount,
          currencyCode: $currencyCode,
          externalId: $externalId,
        };
      `,
      {
        owner: idToRecord('User', userId),
        paymentMethod: idToRecord('PaymentMethod', data.paymentMethod),
        amount: Number(data.amount),
        currencyCode: CurrencyCode.USD,
        externalId: data.externalId,
      },
    )

    const paymentPrice = response?.shift()?.shift()
    return paymentPrice ? jsonify<PaymentPrice>(paymentPrice) : undefined
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
