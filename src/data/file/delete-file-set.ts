'use server'

import { Surrealized, surql } from '@/io/surreal'
import { logAndThrow } from '@/utils'
import { idToRecord } from '@/utils/idToRecord'

import logger from '@/utils/logger'

export async function deleteFileSet(fileSetId: string): Promise<string> {
  try {
    const surreal = await Surrealized()
    logger.trace(`deleteFileSet(fileSetId:<${fileSetId}>)`)

    await surreal.query(
      surql`
        DELETE type::thing($fileSetId);
      `,
      {
        fileSetId: idToRecord('FileSet', fileSetId),
      },
    )

    return fileSetId
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
