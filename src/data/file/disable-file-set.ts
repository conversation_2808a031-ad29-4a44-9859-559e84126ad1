'use server'

import { FileSet } from '@/actions/file'
import { Surrealized, jsonify, surql } from '@/io/surreal'
import { logAndThrow } from '@/utils'
import { idToRecord } from '@/utils/idToRecord'

import logger from '@/utils/logger'

export async function disableFileSet(
  fileSetId: string,
): Promise<FileSet | undefined> {
  try {
    const surreal = await Surrealized()
    logger.trace(`disableFileSet(fileSetId:<${fileSetId}>)`)

    const result = await surreal.query<[FileSet]>(
      surql`
        UPDATE type::thing($fileSetId) SET enabled = false;
      `,
      {
        fileSetId: idToRecord('FileSet', fileSetId),
      },
    )

    const output = result.shift() ?? undefined
    return output ? jsonify<FileSet>(output) : undefined
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
