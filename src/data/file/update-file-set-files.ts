'use server'

import { type File, type FileSet } from '@/actions/file'
import { Surrealized, surql, jsonify } from '@/io/surreal'
import { logAndThrow } from '@/utils'

import logger from '@/utils/logger'

export async function updateFileSetFiles(
  fileSetId: string,
  newFiles: string[] | File['id'][],
): Promise<FileSet | undefined> {
  const surreal = await Surrealized()

  try {
    logger.trace(`updateFileSetFiles(fileSetId:<${fileSetId}>)`)

    const response = await surreal.query<[FileSet]>(
      surql`
        UPDATE type::thing($fileSetId) SET files = array::map($newFiles, |$v| type::thing($v));
      `,
      {
        fileSetId,
        newFiles,
      },
    )

    const files = response?.shift()
    return files ? jsonify<FileSet>(files) : undefined
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
