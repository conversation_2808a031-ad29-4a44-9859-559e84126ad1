'use server'

import { Surrealized, surql } from '@/io/surreal'
import { logAndThrow } from '@/utils'
import { idToRecord } from '@/utils/idToRecord'

import logger from '@/utils/logger'

export async function disableFile(fileId: string): Promise<string> {
  try {
    const surreal = await Surrealized()
    logger.trace(`disableFile(fileId:<${fileId}>)`)

    await surreal.query(
      surql`
        UPDATE type::thing($fileId) SET enabled = false;
      `,
      {
        fileId: idToRecord('File', fileId),
      },
    )

    return fileId
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
