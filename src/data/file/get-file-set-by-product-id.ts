'use server'

import { FileSet } from '@/actions/file'
import { Surrealized, surql, jsonify } from '@/io/surreal'
import { logAndThrow } from '@/utils'
import { idToRecord } from '@/utils/idToRecord'

import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

export async function getFileSetByProductId(
  productId: string,
): Promise<FileSet | undefined> {
  const surreal = await Surrealized()

  try {
    logger.trace(`getFileSetByProductId(productId:<${maskId(productId)}>)`)

    const response = await surreal.query<[FileSet[]]>(
      surql`
        SELECT * FROM FileSet
          WHERE parent = type::thing($productId)
          FETCH
            parent,
            files
        ;
      `,
      {
        productId: idToRecord('Product', productId),
      },
    )

    const output = response?.shift()?.shift()
    return output ? jsonify<FileSet>(output) : undefined
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
