const questions = [
  {
    id: crypto.randomUUID(),
    question: 'What is Webflow, and why should I use it for my website?',
    answer: `Webflow is a web design tool that allows you to design, build, and launch responsive websites visually, without writing code. It combines a visual editor with powerful CMS functionality, enabling designers to create professional websites faster and more efficiently. You should use Webflow if you want full control over your design, the ability to create custom animations, and a robust hosting solution.`,
  },
  {
    id: crypto.randomUUID(),
    question: "How does Webflow's CMS benefit content management?",
    answer: `Webflow's CMS (Content Management System) provides an intuitive interface for managing your site's content. It allows you to create custom content types, add fields, and design templates without needing a developer. The CMS is perfect for blogs, portfolios, and any content-driven website, offering flexibility and ease of use for both designers and content editors.`,
  },
  {
    id: crypto.randomUUID(),
    question: 'What is Webflow, and why should I use it for my website?',
    answer: `Framer is a design and prototyping tool that focuses on creating interactive and high-fidelity prototypes. It allows designers to build interactive components with code and provides advanced animation capabilities. Unlike Webflow, which is more focused on building and hosting complete websites, Framer excels in the early stages of design, where interaction and animation are key. Framer is ideal for creating detailed prototypes that mimic the final user experience.`,
  },
  {
    id: crypto.randomUUID(),
    question: 'Can I integrate Webflow and Framer in my design workflow?',
    answer: `Yes, you can integrate Webflow and Framer in your design workflow to leverage the strengths of both tools. Use Framer to create detailed, interactive prototypes and test user interactions. Once you finalize the design, move to Webflow to build and deploy the actual website. This approach allows you to experiment with interactions and animations in Framer before translating your designs into a fully functional website in Webflow.`,
  },
]

export async function getFaqQuestions() {
  return questions
}
