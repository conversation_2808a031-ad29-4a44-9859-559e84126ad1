'use server'

import { RecordId } from 'surrealdb'

import { idToRecord } from '@/utils/idToRecord'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

import { getSessionUser } from '@/actions/auth'
import { getUserById } from '@/actions/user'

export async function getSessionUserOrAdmin(
  adminsActions?: string,
): Promise<null | RecordId<string>> {
  const sessionUser = await getSessionUser()
  const user = await getUserById(sessionUser?.id?.toString())

  if (user?.hats?.find(hat => hat.slug === 'admin')) {
    logger.info(
      `Session user is admin, ${maskId(user?.id.toString())}, acting: ${adminsActions ?? 'unknown'}`,
    )
    return null
  }

  return idToRecord('User', String(user?.id))
}
