'use server'

import logger from '@/utils/logger'
import maskEmail from '@/utils/mask-email'
import { saveUser } from '@/actions/user/save-user'
import {
  getUserByEmail,
  UserInSession,
} from '@/actions/user'
import { enrichUserHats } from '@/actions/user/enrich-user-hats'
import { getFullAuthSession } from '@/io/next-auth-config'
import { getSession } from './getSession'
import { saveSession } from './save-session'
import { sendEmailCode } from './send-email-code'

export interface SessionData {
  email: string
  provider: string
  providerId: string
  credentials: Record<string, any>
  meta?: Record<string, any>
}

export async function signIn({ user, account, profile }: any) {
  if (account.provider === 'google') {
    if (!profile.email_verified) {
      logger.info(`User profile not verified: <${maskEmail(profile.email)}>`)
      return false
    }

    try {
      const existingUser = await getUserByEmail(profile?.email || user?.email)

      const savedUser = await saveUser({
        email: user.email,
        name: existingUser?.name || user?.name,
        handler:
          existingUser?.handler ||
          user.email.replace('@', '_').replace('.', '_'),
        avatar: existingUser?.avatar || profile?.picture || '',
        profile: {
          ...existingUser?.profile,
          firstName: profile?.given_name || user?.name || '',
          lastName: profile?.family_name || user?.name || '',
        },
      })

      if (!savedUser) return false

      await saveSession({
        email: savedUser.email,
        provider: account.provider,
        providerId: account.providerAccountId,
        credentials: account,
        meta: {
          ...savedUser,
          providerId: account.providerAccountId,
        },
      })

      return true
    } catch (err: unknown) {
      logger.error(`${(err as Error).toString()}`)
      return false
    }
  }

  if (account.provider === 'credentials') {
    const existingUser = await getUserByEmail(profile?.email || user?.email)

    if (!existingUser) {
      return false
    }

    await saveSession({
      email: existingUser.email,
      provider: account.provider,
      providerId: account.providerAccountId,
      credentials: account,
      meta: {
        ...existingUser,
        providerId: account.providerAccountId,
      },
    })

    return true
  }

  return false
}

/**
 *
 */
export async function session({ session, token }: any) {
  logger.trace(`session(session:${session.user.email}, token:${token.sub})`)

  const savedSession = await getSession(token.sub)
  if (savedSession?.meta && savedSession?.email) {
    logger.info(`Session for user: <${maskEmail(savedSession?.email)}> email`)
    const { meta } = savedSession

    const enrichedUser = await enrichUserHats(meta)

    session.providerId = token.sub
    session.user = {
      ...enrichedUser,
      hats: enrichedUser.hats || [],
    }

    return session
  }

  logger.info(
    `Session for user <${maskEmail(token.email)}> not found , signing out`,
  )
  return null
}

/**
 *
 */
export async function redirect({ baseUrl }: any) {
  return `${baseUrl}/post-login-redirect`
}

/**
 *
 */
export async function jwt({ token, user, trigger }: any) {
  if (trigger === 'update' || user) {
    try {
      const savedSession = await getSession(token.sub)
      if (savedSession?.meta && savedSession?.email) {
        const { meta } = savedSession
        const enrichedUser = await enrichUserHats(meta)

        token.user = {
          ...enrichedUser,
          hats: enrichedUser.hats || [],
        }
      } else if (user) {
        token.user = user
      }
    } catch (error) {
      logger.error('Error enriching JWT token:', error)
      if (user) {
        token.user = user
      }
    }
  }

  return token
}

/**
 *
 */
export async function getSessionUser() {
  const session = await getFullAuthSession()
  return session?.user as UserInSession
}

export { sendEmailCode }
