'use server'

import { getSessionUser } from '@/actions/auth'
import { type User } from '@/actions/user'
import { getUserById } from '@/actions/user/getUserById'
import { getIdFromRecord } from '@/utils'

export async function getSessionAdmin(): Promise<User> {
  const sessionUser = await getSessionUser()
  const admin = await getUserById(getIdFromRecord(sessionUser.id))

  if (admin?.hats && admin?.hats?.find(hat => hat.slug === 'admin')) {
    return admin
  }

  throw new Error('Unauthorized')
}
