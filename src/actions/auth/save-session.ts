'use server'

import redis from '@/io/ioredis'
import createSessionKey from '@/utils/create-session-key'
import logger from '@/utils/logger'

import { type SessionData } from '@/actions/auth'

export async function saveSession(data: SessionData) {
  try {
    const key = createSessionKey(data?.providerId)
    await redis.hset(key, {
      email: data?.email,
      provider: data?.provider,
      providerId: data?.providerId,
      ...(data?.credentials
        ? { credentials: JSON.stringify(data.credentials || '') }
        : {}),
      ...(data?.meta ? { meta: JSON.stringify(data.meta || '') } : {}),
    })
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}

export async function saveSessionV2(data: SessionData) {
  try {
    const key = createSessionKey(data?.providerId)
    // Garantir que meta tenha todos os campos essenciais e datas como string
    const meta = {
      ...data?.meta,
      updatedAt: new Date().toISOString(),
      // Forçar datas para string ISO se existirem
      created: data?.meta?.created
        ? new Date(data.meta.created).toISOString()
        : undefined,
      updated: data?.meta?.updated
        ? new Date(data.meta.updated).toISOString()
        : undefined,
    }
    const sessionObj = {
      email: data?.email,
      provider: data?.provider,
      providerId: data?.providerId,
      credentials: data?.credentials
        ? JSON.stringify(data.credentials || '')
        : '{}',
      meta: JSON.stringify(meta),
    }
    logger.info('Salvando sessão V2:', sessionObj)
    await redis.hset(key, sessionObj)
  } catch (err: unknown) {
    logger.error(`V2: ${(err as Error).toString()}`)
    throw err
  }
}
