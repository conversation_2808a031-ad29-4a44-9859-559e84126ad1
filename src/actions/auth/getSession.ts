'use server'

import redis from '@/io/ioredis'
import createSession<PERSON>ey from '@/utils/create-session-key'
import logger from '@/utils/logger'

/**
 *
 */
export async function getSession(providerId: string) {
  try {
    const key = createSessionKey(providerId)
    const user = await redis.hgetall(key)
    user.credentials = JSON.parse(user?.credentials ?? '{}')
    user.meta = JSON.parse(user?.meta ?? '{}')
    return user
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
