'use server'

import { RESEND_API_KEY } from '@/config'
import { Resend } from 'resend'
import VerifyIdentity from '@/emails/verify-identity'
import { getUserByEmail, OnboardStatus } from '../user'
import { saveUser } from '../user/save-user'

interface SendEmailCodeParams {
  email: string
}

export async function sendEmailCode({ email }: SendEmailCodeParams) {
  const resend = new Resend(RESEND_API_KEY!)
  // Generate a random 5-digit code
  const code = Math.floor(10000 + Math.random() * 90000).toString()

  try {
    const response = await resend.emails.send({
      from: 'Templace <<EMAIL>>',
      to: email,
      subject: 'Your verification code',
      text: `Your verification code is: ${code}`,
      react: VerifyIdentity({ confirmationCode: code }),
    })

    if (!response || !response.data) {
      return { success: false, error: 'Error on send email.' }
    }

    const user = await getUserByEmail(email)

    const userObj = {
      email,
      name: user?.name || '',
      handler: user?.handler || email.replace('@', '_').replace('.', '_'),
      avatar: user?.avatar || '',
      onBoardStatus: user?.onboardStatus || OnboardStatus.NONE,
      profile: user?.profile ?? {},
      newUserToken: code,
    }

    const savedUser = await saveUser(userObj)

    if (!savedUser) {
      return { success: false, error: 'Error on save new user.' }
    }

    return { success: true }
  } catch (error) {
    return { success: false, error: String(error) }
  }
}
