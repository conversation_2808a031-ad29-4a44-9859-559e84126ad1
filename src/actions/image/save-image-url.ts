'use server'

import { Surrealized, jsonify, surql } from '@/io/surreal'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

import { type Image } from '@/actions/image'

export async function saveImageUrl(
  image: string,
  url: string,
): Promise<Image | undefined> {
  const surreal = await Surrealized()

  try {
    logger.trace(
      `saveImageUrl(image: ${maskId(image)}, url: ${maskId(url, 9)})`,
    )

    const [images] = await surreal.query<[Image[]]>(
      surql`
        UPDATE type::thing($image)
          SET
            url = $url,
            updated = time::now()
        ;
      `,
      {
        image,
        url,
      },
    )

    const output = images?.shift() ?? undefined
    return output ? jsonify<Image>(output) : undefined
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
