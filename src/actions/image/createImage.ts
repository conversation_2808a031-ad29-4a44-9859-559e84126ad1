'use server'

import { Surrealized, jsonify, surql } from '@/io/surreal'
import logger from '@/utils/logger'

import { type Image } from '@/actions/image'
import maskId from '@/utils/mask-id'

export async function createImage(
  ownerId: string,
  name: string,
  url?: string,
  tags: string[] = [],
): Promise<Image> {
  const surreal = await Surrealized()

  try {
    logger.trace(`createImage(ownerId: ${maskId(ownerId)}, name: ${name})`)

    const response = await surreal.query<[[Image]]>(
      surql`
        INSERT INTO Image {
          owner: type::thing($ownerId),
          name: $name,
          url: $url,
          tags: $tags
        } ON DUPLICATE KEY UPDATE
          updated = time::now(),
          name = $name,
          tags = $tags
        ;`,
      {
        ownerId,
        name,
        url,
        tags,
      },
    )

    const image = response?.shift()?.shift() ?? null
    return jsonify(image) as Image
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
