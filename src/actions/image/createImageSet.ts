'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'
import logger from '@/utils/logger'
import { type ImageSet, type Image } from '.'

export async function createImageSet(
  parentId: string,
  images: Image[],
): Promise<ImageSet> {
  const surreal = await Surrealized()

  try {
    logger.trace(`Creating new image`)

    const response = await surreal.query<[[], [ImageSet]]>(
      surql`
        LET $mapped_images = array::map($images, |$i|type::thing($i));
        INSERT INTO ImageSet {
          parent: type::thing($parentId),
          images: $mapped_images
        };
      `,
      {
        parentId,
        images: images.map(image => image.id.toString()),
      },
    )

    const imageSet = response?.pop()?.shift() ?? null
    return jsonify(imageSet) as ImageSet
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
