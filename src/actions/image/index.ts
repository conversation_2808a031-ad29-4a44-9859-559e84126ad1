import { RecordId, StringRecordId } from 'surrealdb'

import { Product } from '@/actions/product'
import { User } from '@/actions/user'

export type Image = {
  id: RecordId | StringRecordId | string
  name: string
  owner: User
  updated?: Date | string
  url?: string
}

export type ImageSet = {
  id: RecordId | StringRecordId | string
  updated?: Date | string
  parent?: Product
  images: Image[]
}

export * from './createImage'
