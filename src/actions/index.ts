import { RecordId, StringRecordId } from 'surrealdb'

export type BaseModel = {
  id: RecordId | StringRecordId | string
  created?: Date | string
  updated?: Date | string
  enabled?: boolean
}

export type SidebarTotals = {
  webflow?: number
  framer?: number
  bundle?: number
  subscription?: number
  template?: number
  component?: number
  app?: number
  featured?: number
}

export * from './get-sidebar-totals'
export * from './get-trending-templates'
export * from './get-top-creators'
export * from './get-recent-creators'
export * from './get-recent-products'
