import { z } from 'zod'

import { FileSchema } from '@/actions/file'

export type ProductPlatformsKeys = keyof typeof PlatformsSchema.shape
export type ProductPlatformsValues<T extends ProductPlatformsKeys = any> =
  T extends keyof typeof PlatformsSchema.shape
    ? NonNullable<z.infer<typeof PlatformsSchema>[T]>
    : never

export const PlatformKindSchema = z.enum([
  'template',
  'component',
  'asset',
  'font',
])

export const ProductSchema = z.any()

export const PlatformsSchema = z.object({
  webflow: z
    .object({
      kind: PlatformKindSchema.optional(),
      distributionId: z.string().optional(),
      distributionStatus: z.string().optional(),
      auto: z.boolean().optional(),
    })
    .optional(),
  framer: z
    .object({
      kind: PlatformKindSchema.optional(),
      remixUrl: z.string().url().optional(),
      embedPreview: z.boolean().optional(),
    })
    .optional(),
  figma: z
    .object({
      downloadLink: z.string().url().optional(),
      file: FileSchema,
    })
    .optional(),
  vzero: z
    .object({
      downloadLink: z.string().url().optional(),
      github: z.string().url().optional(),
      file: FileSchema,
      embedPreview: z.boolean().optional(),
    })
    .optional(),
  loveable: z
    .object({
      downloadLink: z.string().url().optional(),
      repo: z.string().url().optional(),
      github: z.string().url().optional(),
      file: FileSchema,
      embedPreview: z.boolean().optional(),
    })
    .optional(),
  bolt: z
    .object({
      downloadLink: z.string().url().optional(),
      repo: z.string().url().optional(),
      github: z.string().url().optional(),
      file: FileSchema,
      embedPreview: z.boolean().optional(),
    })
    .optional(),
  spline: z
    .object({
      // downloadLink: z.string().url().optional(),
      file: FileSchema,
    })
    .optional(),
  shopify: z
    .object({
      downloadLink: z.string().url().optional(),
      repo: z.string().url().optional(),
      github: z.string().url().optional(),
      file: FileSchema,
    })
    .optional(),

  // multiple products
  subscription: z
    .object({
      assets: z.array(ProductSchema),
      startDate: z.string().datetime(),
      endDate: z.string().datetime(),
    })
    .optional(),
  bundle: z
    .object({
      assets: z.array(ProductSchema),
    })
    .optional(),
})

export const PlatformKindEnum = Object.keys(PlatformKindSchema.enum).reduce(
  (acc, item) => {
    const uppercased = item.toUpperCase() as Uppercase<
      z.infer<typeof PlatformKindSchema>
    >
    acc[uppercased] = item.toLowerCase() as z.infer<typeof PlatformKindSchema>
    return acc
  },
  {} as Record<
    Uppercase<z.infer<typeof PlatformKindSchema>>,
    z.infer<typeof PlatformKindSchema>
  >,
)

export const PlatformsEnum = Object.freeze(
  Object.keys(PlatformsSchema.shape).reduce(
    (acc, item: string) => {
      const uppercased = item.toUpperCase() as Uppercase<ProductPlatformsKeys>
      const lowercased = item.toLowerCase() as Lowercase<ProductPlatformsKeys>
      acc[uppercased] = lowercased
      return acc
    },
    {} as Record<Uppercase<ProductPlatformsKeys>, ProductPlatformsKeys>,
  ),
)

export type Platforms = z.infer<typeof PlatformsSchema>

export const enum ProductStatus {
  INITIAL = 'ProductStatus/INITIAL',
  PROGRESS = 'ProductStatus/PROGRESS',
  WAITING = 'ProductStatus/WAITING',
  PENDING = 'ProductStatus/PENDING',
  DRAFT = 'ProductStatus/DRAFT',
  HOLDING = 'ProductStatus/HOLDING',
  PUBLISHED = 'ProductStatus/PUBLISHED',
  UNKNOWN = 'ProductStatus/UNKNOWN',
}

export const ProductStatusSchema = z.enum([
  ProductStatus.INITIAL,
  ProductStatus.PROGRESS,
  ProductStatus.WAITING,
  ProductStatus.PENDING,
  ProductStatus.DRAFT,
  ProductStatus.HOLDING,
  ProductStatus.PUBLISHED,
  ProductStatus.UNKNOWN,
])
