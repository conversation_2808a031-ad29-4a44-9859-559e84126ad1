'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'

import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'
import maskData from '@/utils/mask-data'

import { FileSet } from '@/actions/file'
import { Product } from '@/actions/product'

export async function updateProductFileSet(
  givenProduct: Product,
  fileSet: FileSet,
): Promise<Product> {
  const surreal = await Surrealized()

  try {
    logger.trace(
      `updateProductImageSet(product:<${maskId(givenProduct.id)}>, imageSet: ${maskData(fileSet.id)})`,
    )

    const response = await surreal.query<[[Product]]>(
      surql`
        UPDATE type::thing($product) SET
          updated = time::now(),
          fileSet = type::thing($fileSet)
        ;
      `,
      {
        product: givenProduct.id.toString(),
        fileSet: fileSet.id.toString(),
      },
    )

    const product = response?.shift()?.shift() ?? null
    return jsonify(product) as Product
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
