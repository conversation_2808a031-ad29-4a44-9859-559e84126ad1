'use server'

import { Surrealized, jsonify, surql } from '@/io/surreal'

import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

import { Product } from '@/actions/product'

export async function getUserBundleProducts(userId: string) {
  const surreal = await Surrealized()

  try {
    logger.info(`getUserBundleProducts(userId: ${maskId(userId)})>`)

    const response = await surreal.query<[[any]]>(
      surql`
        SELECT * FROM Product WHERE owner = type::thing($userId) AND (
          type::is::record(children, 'ProductBundle')
        ) FETCH
            children,
            children.assets,
            children.assets.children,
            children.assets.imageSet,
            children.assets.imageSet.images,
            owner,
            imageSet,
            imageSet.images,
            price,
            price.paymentMethod
        ;
      `,
      { userId: String(`User:${userId?.toString()?.split(':')?.pop()}`) },
    )

    const products = response?.shift() ?? []
    return jsonify(products) as Product[]
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
