'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'

import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'
import maskData from '@/utils/mask-data'

import { ImageSet } from '@/actions/image'
import { Product } from '@/actions/product'

export async function updateProductImageSet(
  givenProduct: Product,
  imageSet: ImageSet,
): Promise<Product> {
  const surreal = await Surrealized()

  try {
    logger.trace(
      `updateProductImageSet(product:<${maskId(givenProduct.id)}>, imageSet: ${maskData(imageSet.id)})`,
    )

    const response = await surreal.query<[[Product]]>(
      surql`
        UPDATE type::thing($product) SET
          updated = time::now(),
          imageSet = type::thing($imageSet)
        ;
      `,
      {
        product: givenProduct.id,
        imageSet: imageSet.id,
      },
    )

    const product = response?.shift()?.shift() ?? null
    return jsonify(product) as Product
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
