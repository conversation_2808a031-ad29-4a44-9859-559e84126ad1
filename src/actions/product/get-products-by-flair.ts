'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'

import { Product } from '@/actions/product'
import logger from '@/utils/logger'
import { logAndThrow } from '@/utils'

/**
 *
 */
export async function getProductsByFlair(
  rawFlair: string,
  limit: number = 0,
  offset: number = 0,
): Promise<Product[] | undefined> {
  logger.trace(`getProductsByFlair(rawFlair ${rawFlair})`)

  try {
    const surreal = await Surrealized()

    const flair = rawFlair.split('|')

    const response = await surreal.query<[unknown, Product[]]>(
      surql`
        LET $flairFromAssets = |$f, $p| array::fold(
            array::union(
                $p.bundle.assets.*.flair OR [],
                $p.subscription.assets.*.flair OR []
            ),
            $f,
            |$a, $i| array::union($a OR [], $i OR [])
        );

        SELECT *, $flairFromAssets(flair, platforms) as flair
            FROM Product
              WHERE featured = true
                AND (
                    IF (array::find($flair, 'subscription') OR array::find($flair, 'bundle')) THEN {
                        array::intersect($flair, $flairFromAssets(flair, platforms)).len() == $flair.len()
                    } ELSE {
                        array::intersect(flair, $flair).len() == $flair.len()
                    } END
                )
                AND status = 'ProductStatus/PUBLISHED'
              ORDER BY created
              LIMIT $limit
              START $offset
              FETCH
                owner,
                imageSet,
                imageSet.images,
                fileSet,
                fileSet.files,
                price,
                price.paymentMethod,
                platforms.bundle.assets,
                platforms.subscription.assets
            ;
        `,
      {
        flair,
        limit,
        offset,
      },
    )

    const values = response?.[1]
    const output = values ? jsonify<Product[]>(values) : undefined
    return output
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
