'use server'

import { getSessionUser } from '@/actions/auth'
import { deleteFileSet } from '@/data/file/delete-file-set'
import { getFileSetByProductId } from '@/data/file/get-file-set-by-product-id'
import { getIdFromRecord, logAndThrow, logger, maskId } from '@/utils'
import { getProductById } from '@/actions/product'
import { deleteProduct } from '@/data/product/delete-product'

async function deleteProductFileSet(
  productId: string,
): Promise<string | undefined> {
  const fileSet = await getFileSetByProductId(productId)
  if (fileSet) {
    await deleteFileSet(getIdFromRecord(String(fileSet?.id)) as string)
  }

  return fileSet?.id?.toString()
}

export async function deleteProductCascade(productId: string): Promise<string> {
  try {
    const user = await getSessionUser()
    const userId = user?.id?.toString()

    const product = await getProductById(productId)

    if (!product) {
      throw new Error('Product not found')
    }

    if (product?.owner?.id !== userId) {
      throw new Error('Unauthorized')
    }

    logger.info(
      `Deleting Product <${productId}>, productId <${maskId(userId)}>, userId`,
    )

    await deleteProductFileSet(productId)

    await deleteProduct(productId)

    logger.info(`Product <${productId}> deleted.`)

    return productId
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
