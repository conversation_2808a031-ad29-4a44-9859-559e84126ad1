'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'

import logger from '@/utils/logger'

/**
 * Checks if a product with the given slug exists in the database.
 *
 * @param slug - The slug to check for existence
 * @returns A boolean indicating whether a product with the given slug exists
 */
export async function checkProductSlug(slug: string): Promise<boolean> {
  const surreal = await Surrealized()

  try {
    const [[result]] = await surreal.query<[[boolean]]>(
      surql`
        SELECT count() > 0 AS exists FROM Product WHERE slug = $slug;
      `,
      { slug },
    )

    return jsonify(Bo<PERSON>an(result))
  } catch (err) {
    logger.error(`${(err as Error).toString()}`)
    return false
  }
}
