'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'
import { logger, logAndThrow } from '@/utils'
import { Product } from '@/actions/product'

/**
 *
 */
export async function getProductsByPlatform(
  platform: string,
  limit: number = 0,
  offset: number = 0,
): Promise<Product[] | undefined> {
  logger.trace(`getProductsByPlatform(rawFlair ${platform})`)

  try {
    const surreal = await Surrealized()

    const response = await surreal.query<[unknown, Product[]]>(
      surql`
        SELECT *
            FROM Product
              WHERE featured = true
                AND $platform IN platforms
                AND status = 'ProductStatus/PUBLISHED'
              ORDER BY created
              LIMIT $limit
              START $offset
              FETCH
                owner,
                imageSet,
                imageSet.images,
                fileSet,
                fileSet.files,
                price,
                price.paymentMethod,
                platforms.bundle.assets,
                platforms.subscription.assets
            ;
        `,
      {
        platform,
        limit,
        offset,
      },
    )

    const values = response?.[1]
    const output = values ? jsonify<Product[]>(values) : undefined
    return output
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
