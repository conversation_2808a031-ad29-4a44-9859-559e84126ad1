'use server'

import { Surrealized, jsonify, surql } from '@/io/surreal'
import { getSessionAdmin } from '@/actions/auth/get-session-admin'
import { idToRecord } from '@/utils/idToRecord'
import { logAndThrow, logger, maskId } from '@/utils'
import { type Product, ProductStatus } from '@/actions/product'

async function getProductByIdIncludingDisabled(
  productId: string,
): Promise<Product | undefined> {
  const surreal = await Surrealized()

  try {
    const response = await surreal.query<[[Product]]>(
      surql`
        SELECT * FROM Product
          WHERE id=type::thing($productId)
          FETCH
            owner,
            fileSet,
            fileSet.files,
            price,
            price.paymentMethod,
            platforms.bundle.assets,
            platforms.subscription.assets
      ;`,
      {
        productId: idToRecord('Product', productId),
      },
    )

    const product = response?.shift()?.shift()
    return product ? jsonify<Product>(product) : undefined
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}

export async function toggleProductEnabled(
  productId: string,
): Promise<boolean> {
  try {
    // Check if user is admin
    const admin = await getSessionAdmin()

    logger.info(
      `Toggling product enabled status <${productId}>, adminId <${maskId(admin.id.toString())}>`,
    )

    // Get current product (including disabled ones)
    const product = await getProductByIdIncludingDisabled(productId)
    if (!product) {
      throw new Error('Product not found')
    }

    // Toggle enabled status
    const newEnabledStatus = !product.enabled

    // Update product directly via query to avoid affecting price
    const surreal = await Surrealized()

    // If disabling product, also unpublish it (set as DRAFT)
    const newStatus = !newEnabledStatus ? ProductStatus.DRAFT : product.status

    if (!newEnabledStatus) {
      logger.info(
        `Product <${productId}> being disabled and unpublished (status -> DRAFT)`,
      )
    }

    const response = await surreal.query<[[Product]]>(
      surql`
        UPDATE type::thing($productId) MERGE {
          updated: time::now(),
          enabled: $enabled,
          status: $status
        };
        
        SELECT * FROM type::thing($productId) FETCH price;
      `,
      {
        productId: idToRecord('Product', productId),
        enabled: newEnabledStatus,
        status: newStatus,
      },
    )

    const updatedProductRaw = response?.pop()?.pop()
    if (!updatedProductRaw) {
      throw new Error('Failed to update product')
    }

    const updatedProduct = jsonify<Product>(updatedProductRaw)

    logger.info(
      `Product <${productId}> enabled status changed to ${newEnabledStatus}${
        !newEnabledStatus ? ' and unpublished' : ''
      }`,
    )

    return newEnabledStatus
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
