'use server'

import { Surrealized, jsonify, surql } from '@/io/surreal'

import logger from '@/utils/logger'

import { ProductStatus, type Product } from '@/actions/product'

export async function getProductBySlug(
  slug: string,
  status?: ProductStatus,
): Promise<Product | undefined> {
  const surreal = await Surrealized()

  try {
    logger.trace(`getProductBySlug(slug: ${slug})`)

    const response = await surreal.query<[[Product]]>(
      surql`
        SELECT * FROM Product
          WHERE slug = type::string($slug)
            AND (IF $status IS NOT NONE THEN status = type::string($status) ELSE true END)
            AND enabled = true
          FETCH
            owner,
            fileSet,
            fileSet.files,
            price,
            price.paymentMethod,
            platforms.*.assets,
            platforms.*.assets.price,
            platforms.*.assets.fileSet,
            platforms.*.assets.fileSet.files
      ;`,
      {
        slug,
        status,
      },
    )

    const product = response?.shift()?.shift()
    return product ? jsonify<Product>(product) : undefined
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
