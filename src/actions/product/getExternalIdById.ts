'use server'

import { Surrealized, jsonify, surql } from '@/io/surreal'

import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'
import { idToRecord } from '@/utils/idToRecord'

import { Product } from '@/actions/product'

/**
 * @function getExternalIdById
 * @description Get the external ID of a product by ID
 * @param {string} productId - The ID of the product
 * @returns {Promise<Product['externalId'] | undefined>} The external ID of the product, or undefined if not found
 * @example
 * const externalId = await getExternalIdById('some-id')
 * // externalId is 'some-external-id'
 */
export async function getExternalIdById(
  productId: string,
): Promise<Product['externalId'] | null> {
  const surreal = await Surrealized()

  try {
    logger.trace(`getExternalIdById(productId: ${maskId(productId)})`)

    const response = await surreal.query<[[Product['externalId']]]>(
      surql`SELECT VALUE externalId FROM type::thing($productId);`,
      {
        productId: idToRecord('Product', productId),
      },
    )

    const productExternalId = response?.shift()?.shift() ?? null
    const output = productExternalId
      ? jsonify<Product['externalId']>(productExternalId)
      : null
    logger.trace(`getExternalIdById =>`, JSON.stringify(output, null, 2))

    return output
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
