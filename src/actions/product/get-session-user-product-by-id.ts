'use server'

import { Surrealized, jsonify, surql } from '@/io/surreal'

import { idToRecord } from '@/utils/idToRecord'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

import { type Product as BaseProduct } from '@/actions/product'
import { getSessionUser } from '@/actions/auth'
import { FileSet } from '@/actions/file'
import { ImageSet } from '@/actions/image'
import { PaymentMethod } from '@/actions/payment'
import { PaymentPrice } from '@/actions/price'
import { User } from '@/actions/user'

type Product = Omit<
  BaseProduct,
  'price' | 'owner' | 'imageSet' | 'fileSet' | 'children'
> & {
  price: Omit<PaymentPrice, 'paymentMethod'> & {
    paymentMethod: PaymentMethod
  }
  owner: User
  imageSet: ImageSet
  fileSet: FileSet
}

export async function getSessionUserProductById(
  productId: string = '',
): Promise<Product | undefined> {
  const surreal = await Surrealized()

  try {
    const user = await getSessionUser()
    logger.info(`getSessionUserProductById(productId: ${maskId(productId)})>`)

    const response = await surreal.query<[[Product]]>(
      surql`
        SELECT * FROM type::thing($product)
          WHERE owner = type::thing($userId)
          FETCH
            owner,
            imageSet,
            imageSet.images,
            fileSet,
            fileSet.files,
            price,
            price.paymentMethod,
            platforms.bundle.assets,
            platforms.subscription.assets,
            platforms.vzero.file
        ;
      `,
      {
        userId: idToRecord('User', String(user.id)),
        product: idToRecord('Product', String(productId)),
      },
    )

    const product = response?.shift()?.shift()
    return product ? jsonify<Product>(product) : undefined
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
