import { RecordId, StringRecordId } from 'surrealdb'

import { FileSet } from '@/actions/file'
import { ImageSet } from '@/actions/image'
import { PaymentPrice } from '@/actions/price'
import { User } from '@/actions/user'
import { ProductStatus } from '@/actions/product/enum'
import { Platforms } from '@/actions/product/enum'

export { getProductByUserAndType } from './getProductByUserAndType'
// export { createProductAsSubscription } from './createProductAsSubscription'
export { bootstrapProduct } from './bootstrapProduct'
export { checkProductSlug } from './checkProductSlug'
export { createProductFileSet } from './createProductFileSet'
export { deleteProductCascade } from './deleteProductCascade'
export { getSessionUserProductById } from './get-session-user-product-by-id'
export { getExternalIdById } from './getExternalIdById'
export { getProductById } from './getProductById'
export { getProductByIdOrSlug } from './getProductByIdOrSlug'
export { getProductBySlug } from './getProductBySlug'
export { getUserBundleProducts } from './getUserBundleProducts'
export { getUserProductsByKind } from './getUserProductsByKind'
export { getUserSubscriptionProducts } from './getUserSubscriptionProducts'

export { getProductsByFlair } from './get-products-by-flair'
export { getUserProducts } from './get-user-products'
export { getAllProducts, type ProductsResponse } from './get-all-products'
export { toggleProductEnabled } from './toggle-product-enabled'
export * from './saveProduct'
export * from './enum'

export {
  ProductStatus,
  PlatformKindSchema,
  ProductSchema,
} from '@/actions/product/enum'

export const enum ProductChildrenKind {
  WEBFLOW = 'ProductWebflow',
  FRAMER = 'ProductFramer',
  BUNDLE = 'ProductBundle',
  SUBSCRIPTION = 'ProductSubscription',
}

export type ProductFramer = {
  id: RecordId | StringRecordId | string
  previewUrl: string
  description?: string
  remixUrl: string
  kind: any
}

export const enum ProductBundleKind {
  HAT = 'ProductBundle/HAT',
  MULTIPLE = 'ProductBundle/MULTIPLE',
  USER = 'ProductBundle/USER',
  OTHER = 'ProductBundle/OTHER',
}

export type ProductBundle = {
  id: RecordId | StringRecordId | string
  liveUrl?: string
  description?: string
  featured?: boolean
  kind?: ProductBundleKind
  assets: Product[]
}

export const enum ProductSubscriptionKind {
  WEEKLY = 'ProductSubscription/WEEKLY',
  DAILY = 'ProductSubscription/DAILY',
  MONTLHY = 'ProductSubscription/MONTLHY',
  QUARTERLY = 'ProductSubscription/QUARTERLY',
  YEARLY = 'ProductSubscription/YEARLY',
  OTHER = 'ProductSubscription/OTHER',
}

export type ProductSubscription = {
  id: RecordId | StringRecordId | string
  liveUrl?: string
  description?: string
  featured?: boolean
  kind?: ProductSubscriptionKind
  assets: Product[]
}

export type Product = {
  created?: string
  updated?: string
  enabled?: boolean
  name: string
  id: string
  slug: string
  externalId: string
  liveUrl?: string
  description?: string
  featured: boolean
  features?: string[]
  includes?: string[]
  flair?: string[]
  imageSet?: ImageSet
  fileSet?: FileSet
  owner: User
  price: PaymentPrice
  platforms: Platforms
  status: ProductStatus
}
