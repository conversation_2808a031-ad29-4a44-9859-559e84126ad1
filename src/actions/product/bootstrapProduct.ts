import { nanoid } from 'nanoid'

import { User } from '@/actions/user'
import { ProductStatus } from '@/actions/product'
import { insertProduct } from '@/data/product/insert-product'
import { logAndThrow, logger, slugMe } from '@/utils'

export async function bootstrapProduct(userId: User['id'], flair: string) {
  try {
    logger.trace(`bootstrapProduct(userId:${userId}, flair:${flair})`)

    const name = `Untitled-${nanoid(8)}`

    const newProduct = await insertProduct(userId.toString(), {
      name,
      slug: slugMe(name),
      flair: [flair],
      owner: userId.toString(),
      status: ProductStatus.DRAFT,
    })

    return newProduct
  } catch (error) {
    logAndThrow(error, 'bootstrapProduct')
  }
}
