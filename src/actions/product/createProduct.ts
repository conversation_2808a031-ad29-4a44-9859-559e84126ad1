'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'

import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'
import maskData from '@/utils/mask-data'

import { Product } from '.'

export async function createProduct(
  userId: string,
  productData: any,
): Promise<Product> {
  const surreal = await Surrealized()

  try {
    logger.trace(
      `createProduct(userId:<${maskId(userId)}>, productData: ${maskData(productData)})`,
    )

    const response = await surreal.query<[[]]>(
      surql`
        INSERT INTO Product {
          name: $name,
          slug: $slug,
          children: type::thing($children),
          externalId: $externalId,
          imageSet: $imageSet,
          owner: type::thing($owner),
          price: type::thing($price)
        } ON DUPLICATE KEY UPDATE
          updated=time::now(),
          name=$name,
          children=type::thing($children),
          imageSet=$imageSet,
          owner=type::thing($owner),
          price=type::thing($price)
        ;
      `,
      {
        name: productData.name,
        slug: productData.slug,
        children: productData.children,
        externalId: productData.externalId,
        owner: String(`User:${userId?.toString()?.split(':')?.pop()}`),
        price: productData.price,
      },
    )

    const product = response?.shift()?.shift()
    return jsonify(product)

    // const stripeProductUpdated = await updateProductMetadata(stripeProduct.id, {
    //   userId,
    //   productId: product.id,
    //   priceId: product.price.id,
    // });

    // logger.trace(
    //   `stripeProductUpdated:, <${maskId(stripeProductUpdated.id)}>, stripeProductUpdated.id`
    // );

    // return product;
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
