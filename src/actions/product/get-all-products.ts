'use server'

import { Surrealized, jsonify, surql } from '@/io/surreal'

import logger from '@/utils/logger'
import { logAndThrow } from '@/utils'

import { Product } from '@/actions/product'

export type ProductsResponse = {
  products: Product[]
  total: number
  currentPage: number
  totalPages: number
}

export async function getAllProducts(
  limit: number = 50,
  offset: number = 0,
  sortBy: string = 'created',
  sortOrder: 'asc' | 'desc' = 'desc',
): Promise<ProductsResponse> {
  const surreal = await Surrealized()

  try {
    logger.trace(
      `getAllProducts(limit: ${limit}, offset: ${offset}, sortBy: ${sortBy}, sortOrder: ${sortOrder})`,
    )

    // Map frontend column names to database field names
    const sortFieldMap: Record<string, string> = {
      id: 'id',
      name: 'name',
      owner: 'owner.handler',
      price: 'price.amount',
      status: 'status',
      enabled: 'enabled',
      created: 'created',
    }

    const sortField = sortFieldMap[sortBy] || 'created'
    const orderDirection = sortOrder.toUpperCase()

    // Get total count
    const countResponse = await surreal.query<[{ count: number }[]]>(
      surql`
        SELECT count() as count
        FROM Product
        GROUP ALL
      ;`,
    )

    // Build dynamic query string for sorting
    const queryString = `
      SELECT * FROM Product
        ORDER BY ${sortField} ${orderDirection}
        LIMIT $limit
        START $offset
        FETCH
          owner,
          imageSet,
          imageSet.images,
          fileSet,
          fileSet.files,
          price,
          price.paymentMethod,
          platforms.bundle.assets,
          platforms.subscription.assets
    ;`

    const response = await surreal.query<[Product[]]>(queryString, {
      limit,
      offset,
    })

    const total = countResponse?.[0]?.[0]?.count || 0
    const products = response?.shift() ?? []
    const currentPage = Math.floor(offset / limit) + 1
    const totalPages = Math.ceil(total / limit)

    return {
      products: jsonify<Product[]>(products),
      total,
      currentPage,
      totalPages,
    }
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
