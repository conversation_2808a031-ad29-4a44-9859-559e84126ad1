'use server'

import { nanoid } from 'nanoid'

import { getSessionUser } from '@/actions/auth'
import { FileSet } from '@/actions/file'
import { createPaymentPrice, PaymentPrice } from '@/actions/price'
import {
  checkProductSlug,
  createProductFileSet,
  getProductById,
  Product,
} from '@/actions/product'
import {
  createStripeProduct,
  updateProductMetadata,
  updateStripeProduct,
} from '@/actions/stripe'
import { HOST } from '@/config'
import { updateFileSetFiles } from '@/data/file/update-file-set-files'
import { updatePlatformsRefs } from '@/data/product/update-platforms-refs'
import { updateProduct } from '@/data/product/update-product'
import {
  DatabaseSavePlatformsSchema,
  FormSaveBundleData,
  FormSaveTemplateData,
} from '@/schemas'
import { arrayStringTrim, logAndThrow, logger, maskId, slugMe } from '@/utils'
import dollarsToCents from '@/utils/dollars-to-cents'
import { getPlatform } from '@/utils/get-platform'
import { getUserById } from '../user'

type SaveProductData = FormSaveTemplateData & FormSaveBundleData & {}
type SaveProductResponse = {
  externalId: string | undefined
  price: PaymentPrice | undefined
}

function getPriceAmountInCents(price?: FormSaveTemplateData['price']) {
  return price !== undefined ? dollarsToCents(Math.abs(price ?? 0)) : 0
}

async function saveStripeProductAndUpdatePrice(
  userId: string,
  productData: SaveProductData,
  product: Product,
): Promise<SaveProductResponse> {
  const priceAmountInCents = getPriceAmountInCents(
    productData.price ?? product?.price?.amount,
  )

  const description = productData?.description || productData?.name || undefined
  const metadata = {
    userId,
    productId: product.id,
    name: productData.name,
    url: `${HOST}/product/${productData.slug}`,
  }

  let nextStripeProduct
  if (product.externalId === undefined) {
    nextStripeProduct = await createStripeProduct({
      name: productData.name,
      description,
      metadata,
    })
  } else {
    nextStripeProduct = await updateStripeProduct(product.externalId, {
      description,
      metadata,
    })
  }

  if (!nextStripeProduct.id) {
    throw new Error('Failed to create or update Stripe product')
  }

  if (priceAmountInCents !== product?.price?.amount) {
    const { stripePrice, price } = await createPaymentPrice(
      priceAmountInCents,
      userId,
      product.id,
      nextStripeProduct.id,
    )

    if (stripePrice?.id) {
      nextStripeProduct = await updateStripeProduct(nextStripeProduct.id, {
        default_price: stripePrice.id,
      })
    }

    return { externalId: nextStripeProduct.id, price }
  }

  return { externalId: nextStripeProduct.id, price: product.price }
}

async function updateFileSet(
  userId: string,
  productData: FormSaveTemplateData,
  product: Product,
) {
  const imagesAndVideos = new Set(
    productData?.imagesAndVideos?.map(item => item.id.toString()) ?? [],
  )
  if (imagesAndVideos.size !== (productData?.imagesAndVideos?.length ?? 0)) {
    throw new Error('Invalid images and videos')
  }

  const allFiles = [
    ...(productData?.imagesAndVideos ?? []),
    ...(productData?.platforms?.vzero?.file
      ? [productData?.platforms?.vzero?.file]
      : []),
    ...(productData?.platforms?.figma?.file
      ? [productData?.platforms?.figma?.file]
      : []),
    ...(productData?.figmaFile ? [productData?.figmaFile] : []),
  ].map(item => item.id.toString())

  ///

  if (!product?.fileSet && allFiles.length > 0) {
    const fileSet = await createProductFileSet(product, allFiles)
    return fileSet?.id?.toString()
  } else if (product?.fileSet) {
    const productFileSet = product?.fileSet as FileSet
    const productFileSetIds = productFileSet?.files.map(item =>
      item.id.toString(),
    )

    if (JSON.stringify(productFileSetIds) === JSON.stringify(allFiles)) {
      return undefined
    }

    const fileSet = await updateFileSetFiles(
      productFileSet.id.toString(),
      allFiles,
    )

    return fileSet?.id?.toString()
  }
}

export async function saveProduct(
  rawProductData: FormSaveTemplateData,
  productId: string,
) {
  try {
    const sessionUser = await getSessionUser()
    const user = await getUserById(sessionUser?.id.toString())
    if (!user) {
      throw new Error('User not found or disabled')
    }

    const userId = user?.id?.toString()
    if (
      !user.hats?.find(item => item.slug === 'admin' || item.slug === 'creator')
    ) {
      throw new Error('Not wearing the right hat')
    }

    const product = await getProductById(productId)
    if (!product) {
      throw new Error('Product not found')
    } else if (product?.owner?.id !== userId) {
      throw new Error('Unauthorized')
    }

    const productData = {
      ...rawProductData,
      featured: true,
      slug: rawProductData?.slug ?? slugMe(rawProductData?.name ?? nanoid(8)),
    }

    if (productData.slug !== product?.slug) {
      const isSlugUsed = await checkProductSlug(productData.slug!)
      if (isSlugUsed) {
        throw new Error('Slug already in use')
      }
    }

    logger.info(
      `Updating Product <${productId}>, productId <${maskId(userId)}>, userId`,
    )

    const { externalId, price } = await saveStripeProductAndUpdatePrice(
      userId,
      productData,
      product,
    )

    const fileSet = await updateFileSet(userId, productData, product)

    const productPlatform = getPlatform(productData.platforms)
    const flair: string[] = [
      ...new Set([
        ...(product?.flair ?? []),
        ...(productData?.flair ?? []),
        ...(productPlatform ? [productPlatform] : []),
      ]).values(),
    ]

    const platforms = productData.platform
      ? {
          [productData.platform]:
            productData?.platforms?.[productData.platform] ?? {},
        }
      : {}

    // all set
    const updatedProduct = await updateProduct({
      ...productData,
      externalId,
      includes: arrayStringTrim(productData.includes),
      features: arrayStringTrim(productData.features),
      platforms: DatabaseSavePlatformsSchema.parse(platforms),
      flair,
      price,
      fileSet,
      id: productId,
    } as Partial<Product>)
    if (!updatedProduct) {
      throw new Error('Product update failed')
    }

    if (product?.id?.toString()) {
      await updatePlatformsRefs(product?.id?.toString())
    }

    if (updatedProduct.price && externalId) {
      await updateProductMetadata(externalId, {
        userId,
        productId: updatedProduct.id.toString(),
        priceId: updatedProduct.price.id.toString(),
      })
    }

    logger.info(`Product Updated <${updatedProduct.id}>, productId`)

    return updatedProduct
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
