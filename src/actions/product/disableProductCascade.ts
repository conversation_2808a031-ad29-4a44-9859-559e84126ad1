'use server'

import { getSessionUser } from '@/actions/auth'
import { getProductById } from '@/actions/product'
import { disableFile } from '@/data/file/disable-file'
import { disableFileSet } from '@/data/file/disable-file-set'
import { getFileSetByProductId } from '@/data/file/get-file-set-by-product-id'
import { disableProduct } from '@/data/product/disable-product'
import { getIdFromRecord, logAndThrow, logger, maskId } from '@/utils'

async function disableProductFileSet(
  productId: string,
): Promise<string | undefined> {
  const fileSet = await getFileSetByProductId(productId)
  if (fileSet) {
    await disableFileSet(getIdFromRecord(String(fileSet?.id)) as string)

    fileSet.files.forEach(async file => {
      await disableFile(file.id)
    })
  }

  return fileSet?.id?.toString()
}

export async function disableProductCascade(
  productId: string,
): Promise<string> {
  try {
    const user = await getSessionUser()
    const userId = user?.id?.toString()

    const product = await getProductById(productId)

    if (!product) {
      throw new Error('Product not found')
    }

    if (product?.owner?.id !== userId) {
      throw new Error('Unauthorized')
    }

    logger.info(
      `Deleting Product <${productId}>, productId <${maskId(userId)}>, userId`,
    )

    await disableProductFileSet(productId)

    await disableProduct(productId)

    logger.info(`Product <${productId}> deleted.`)

    return productId
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
