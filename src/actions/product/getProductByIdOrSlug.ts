'use server'

import logger from '@/utils/logger'

import {
  getProductBySlug,
  getProductById,
  ProductStatus,
  type Product,
} from '@/actions/product'

export async function getProductByIdOrSlug(
  idOrSlug: string,
  status?: ProductStatus,
): Promise<Product | undefined> {
  try {
    const product =
      (await getProductBySlug(idOrSlug, status)) ??
      (await getProductById(idOrSlug, status))
    return product ?? undefined
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
