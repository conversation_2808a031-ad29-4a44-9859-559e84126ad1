'use server'

import { Surrealized, jsonify, surql } from '@/io/surreal'

import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'
import { idToRecord } from '@/utils/idToRecord'

import { Product, ProductStatus } from '@/actions/product'

export async function getUserProducts(
  userId: string,
  rawFlair: string = '',
  status: ProductStatus | undefined,
  limit: number = 999,
  offset: number = 0,
): Promise<Product[]> {
  const surreal = await Surrealized()

  try {
    logger.info(`getUserProducts(userId: ${maskId(userId)}, status: ${status})`)

    const singleFlair = rawFlair.split('|').shift()

    const response = await surreal.query<[[Product]]>(
      surql`
        SELECT * FROM Product
          WHERE owner = type::thing($userId)
            AND enabled = true
            AND (IF $singleFlair != '' THEN array::find(flair, $singleFlair) ELSE true END)
            AND (IF $status IS NOT NONE THEN status = $status ELSE true END)
          ORDER BY created DESC
          LIMIT $limit
          START $offset
          FETCH
            owner,
            imageSet,
            imageSet.images,
            fileSet,
            fileSet.files,
            price,
            price.paymentMethod,
            platforms.bundle.assets,
            platforms.subscription.assets
        ;
      `,
      {
        userId: idToRecord('User', userId),
        singleFlair,
        limit,
        offset,
        status,
      },
    )

    const products = response?.shift() ?? []
    return jsonify<Product[]>(products)
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
