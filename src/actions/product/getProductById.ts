'use server'

import { Surrealized, jsonify, surql } from '@/io/surreal'

import { idToRecord } from '@/utils/idToRecord'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

import { ProductStatus, type Product } from '@/actions/product'

/**
 * @function getProductById
 * @description Get a product by ID.
 * @param {string} productId - The ID of the product.
 * @returns {Promise<Product>} The product.
 */
export async function getProductById(
  productId: string,
  status?: ProductStatus,
): Promise<Product | undefined> {
  const surreal = await Surrealized()

  try {
    logger.trace(`getProductById(productId: ${maskId(productId)})`)

    const response = await surreal.query<[[Product]]>(
      surql`
        SELECT * FROM Product
          WHERE id=type::thing($productId)
            AND (IF $status IS NOT NONE THEN status = type::string($status) ELSE true END)
            AND enabled = true
          FETCH
            owner,
            fileSet,
            fileSet.files,
            price,
            price.paymentMethod,
            platforms.bundle.assets,
            platforms.subscription.assets
      ;`,
      {
        productId: idToRecord('Product', productId),
        status,
      },
    )

    const product = response?.shift()?.shift()
    return product ? jsonify<Product>(product) : undefined
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
