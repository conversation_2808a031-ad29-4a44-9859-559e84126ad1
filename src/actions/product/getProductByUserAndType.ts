'use server'

import { Surrealized, surql } from '@/io/surreal'

import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

export async function getProductByUserAndType(userId: string, kind: any) {
  const surreal = await Surrealized()

  try {
    logger.info(`getProductByUserAndType: <${maskId(userId)}>, kind: ${kind}`)

    const response = await surreal.query<[[any]]>(
      surql`
        SELECT * FROM Product WHERE owner=$id
          FETCH children, owner, imageSet, imageSet.images, price, price.paymentMethod
      ;`,
      { owner: String(`User:${userId?.toString()?.split(':')?.pop()}`) },
    )

    return response
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    return []
  }
}
