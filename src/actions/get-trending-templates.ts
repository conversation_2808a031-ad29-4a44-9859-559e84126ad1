'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'

import { Product } from '@/actions/product'
import logger from '@/utils/logger'

/**
 *
 */
export async function getTrendingTemplates(
  limit: number = 0,
): Promise<Product[] | undefined> {
  if (limit <= 0) {
    return [] as Product[]
  }

  logger.trace(`getTrendingTemplates(limit: ${limit})`)

  const surreal = await Surrealized()

  try {
    const response = await surreal.query<[Product[]]>(
      surql`
        SELECT
          *,
          (IF owner.relationStatus = 'UserRelation/ELITE_CREATOR' {2}
            ELSE IF owner.relationStatus = 'UserRelation/TOP_CONTRIBUTOR' {1}
            ELSE {0}
          ) as relationLevel
        FROM Product
        WHERE enabled = true
          AND featured = true
          AND owner.enabled = true
          AND (flair CONTAINS 'template')
        ORDER BY updated, relationLevel DESC
        LIMIT $limit
        FETCH
          owner,
          price,
          imageSet,
          imageSet.images,
          fileSet,
          fileSet.files
        ;
      `,
      {
        limit,
      },
    )

    const totals = response?.pop()
    const output = totals ? jsonify<Product[]>(totals) : undefined
    return output
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
