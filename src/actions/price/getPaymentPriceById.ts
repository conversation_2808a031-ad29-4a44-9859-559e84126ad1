'use server'

import { Surrealized, surql } from '@/io/surreal'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

import { PaymentPrice } from '@/actions/price'

export async function getPaymentPriceById(paymentPriceId: string) {
  const surreal = await Surrealized()

  try {
    logger.info(`PaymentPrice: <${maskId(paymentPriceId)}>`)

    const response = await surreal.query<[[PaymentPrice]]>(
      surql`
        SELECT * FROM PaymentPrice WHERE id=type::thing($id);
      `,
      {
        id: String(
          `PaymentPrice:${paymentPriceId?.toString()?.split(':')?.pop()}`,
        ),
      },
    )

    return response
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    return null
  }
}
