import { z } from 'zod'

import { PaymentPrice, PaymentPriceInput, CurrencyCode } from './types'

export type { PaymentPrice, PaymentPriceInput }
export { CurrencyCode }

export { getPaymentPriceById } from './getPaymentPriceById'
export { createPaymentPrice } from './createPaymentPrice'

export const PriceSchema = z.union([
  z
    .string()
    .superRefine((data, ctx) => {
      const maybeNumber = Number(data.replace(/,|\./g, ''))
      if (typeof data === 'string' && Number.isNaN(maybeNumber)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Price should only contain numbers',
        })
      }
    })
    .transform((val: string) => {
      return typeof val === 'string' ? Number(val.replace(/,|\./g, '')) : val
    }),
  z.number(),
])
