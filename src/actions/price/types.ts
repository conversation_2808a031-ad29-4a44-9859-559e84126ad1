import { RecordId, StringRecordId } from 'surrealdb'
import { PaymentMethod } from '@/actions/payment'
import { User } from '@/actions/user'

export interface PaymentPriceInput {
  amount: number
  paymentMethod: string
  externalId: string // unique stripe id
}

export type PaymentPrice = {
  id: RecordId | StringRecordId | string
  created?: Date | string
  updated?: Date | string
  enabled?: boolean
  currencyCode: string
  amount: number
  paymentMethod: PaymentMethod
  externalId?: string
  owner: User
}

export const enum CurrencyCode {
  USD = 'USD',
}
