'use server'

import { nanoid } from 'nanoid'

import { createStripePrice } from '@/actions/stripe/createStripePrice'
import { getUserPaymentMethod } from '@/actions/user/getUserPaymentMethod'
import { createPrice } from '@/data/price/create-price'
import { logAndThrow } from '@/utils'

export async function createPaymentPrice(
  amount: number,
  userId: string,
  productId: string,
  productExternalId?: string,
) {
  try {
    const paymentMethod = await getUserPaymentMethod(userId)

    let stripePrice = null
    let externalId = undefined
    if (amount === 0) {
      externalId = `free_${nanoid(11)}`
    } else {
      stripePrice = await createStripePrice({
        unit_amount: amount,
        nickname: `dsm.product::${productId}`,
        product: productExternalId,
      })

      externalId = stripePrice.id
    }

    const price = await createPrice(userId, {
      paymentMethod: paymentMethod.id.toString(),
      amount,
      externalId,
    })

    return { paymentMethod, stripePrice, price }
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
