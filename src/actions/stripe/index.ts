import Stripe from 'stripe'

import { STRIPE_SECRET_KEY } from '@/config'

export type StripeProductMetadata = Stripe.Emptyable<{
  productId?: string
  userId?: string
  priceId?: string
}>

export const stripe = new Stripe(STRIPE_SECRET_KEY!)

export { createStripeProduct } from './createStripeProduct'
export { getProduct } from './getProduct'
export { updateProductMetadata } from './update-product-metadata'
export { createCheckoutSession } from './createCheckoutSession'
export { getEventFromCache } from './getEventFromCache'
export { getInvoice } from './get-invoice'
export { updateStripeProduct } from './update-stripe-product'
