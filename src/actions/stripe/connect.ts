'use server'

import { getFullAuthSession } from '@/io/next-auth-config'
import { Surrealized } from '@/io/surreal'
import { logAndThrow } from '@/utils'
import Stripe from 'stripe'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!)

export async function createStripeConnectAccount() {
  try {
    const session = await getFullAuthSession()

    if (!session?.user?.id) {
      throw new Error('Unauthorized')
    }

    const account = await stripe.accounts.create({
      type: 'express',
    })

    const accountLink = await stripe.accountLinks.create({
      account: account.id,
      refresh_url: `${process.env.NEXT_PUBLIC_BASE_URL}/user/settings`,
      return_url: `${process.env.NEXT_PUBLIC_BASE_URL}/settings/stripe/callback?account_id=${account.id}`,
      type: 'account_onboarding',
    })

    return { url: accountLink.url }
  } catch (err) {
    logAndThrow(err)
    return { error: 'Error creating Stripe connect link' }
  }
}

export async function saveStripeAccountId(accountId: string) {
  try {
    const session = await getFullAuthSession()

    if (!session?.user?.id) {
      throw new Error('Unauthorized')
    }

    const surreal = await Surrealized()

    // First, get the current user to check their existing Stripe ID
    const [currentUser] = await surreal.query<
      [{ profile?: { stripeConnectId?: string } }?]
    >(`SELECT profile FROM ${session.user.id}`)

    // If the user already has the same Stripe account connected, do nothing.
    if (currentUser?.profile?.stripeConnectId === accountId) {
      return { success: true, message: 'Account already connected.' }
    }

    const query = `UPDATE ${session.user.id} MERGE { profile: { stripeConnectId: '${accountId}' } };`
    await surreal.query(query)

    return { success: true }
  } catch (err) {
    console.error('Error saving Stripe account:', err)
    logAndThrow(err)
    return { error: 'Error saving Stripe account' }
  }
}
