import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'
import { getEventFromCache } from '.'

export async function getLogDataFromSession(sessionId: string) {
  logger.trace(
    `Getting log data from session <${maskId(sessionId)}>, sessionId`,
  )

  const checkoutInitEvent = await getEventFromCache([
    'stripe.dsm.checkout.init',
    sessionId,
  ])

  const checkoutCompletedEvent = await getEventFromCache([
    'stripe.checkout.session.completed',
    sessionId,
  ])

  const {
    customer,
    invoice,
    payment_intent: paymentIntent,
    metadata,
  } = checkoutCompletedEvent?.data?.object ?? {}

  const invoiceCreatedEvent = await getEventFromCache([
    'stripe.invoice.created',
    invoice,
  ])

  const { invoice_pdf: invoicePdf } = invoiceCreatedEvent?.data?.object ?? {}

  const invoiceFinalizedEvent = await getEventFromCache([
    'stripe.invoice.finalized',
    invoice,
  ])

  const { hosted_invoice_url: invoiceUrl } = invoiceFinalizedEvent?.data?.object ?? {}

  return {
    customer,
    invoice,
    invoicePdf,
    invoiceUrl,
    paymentIntent,
    metadata: {
      ...checkoutInitEvent?.data,
      ...metadata,
    },
  }
}
