import Stripe from 'stripe'

import { stripe } from '.'

type StripeProductCreateParams = Stripe.ProductCreateParams & {
  amount: number
  currency: string
}

export async function createStripeSubscription(
  stripeCustomerId: string,
  stripePriceId: string,
  metadata: Record<string, any>,
) {
  const product = await stripe.subscriptions.create({
    customer: stripeCustomerId,
    items: [{ price: stripePriceId }],
    metadata,
  })

  return product
}
