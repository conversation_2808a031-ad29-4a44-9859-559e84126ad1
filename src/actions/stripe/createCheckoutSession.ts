import { HOST } from '@/config'

import { stripe } from '@/actions/stripe'

export async function createCheckoutSession(
  priceId: string,
  mode: 'payment' | 'subscription',
  metadata: Record<string, string>,
  sellerStripeConnectId?: string,
  applicationFeeAmount?: number,
) {
  const sessionParams: any = {
    payment_method_types: ['card'],
    line_items: [
      {
        price: priceId,
        quantity: 1,
      },
    ],
    currency: 'usd',
    mode,
    metadata,
    success_url: `${HOST}/api/payment/stripe/complete?session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: `${HOST}/api/payment/stripe/cancel?session_id={CHECKOUT_SESSION_ID}`,
  }

  // unique payment
  if (mode === 'payment') {
    sessionParams.invoice_creation = {
      enabled: true,
    }
  }

  if (sellerStripeConnectId && applicationFeeAmount) {
    sessionParams.payment_intent_data = {
      application_fee_amount: applicationFeeAmount,
      transfer_data: {
        destination: sellerStripeConnectId,
      },
    }
  }

  const checkout = await stripe.checkout.sessions.create(sessionParams)
  return checkout
}
