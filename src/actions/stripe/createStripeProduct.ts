import Stripe from 'stripe'

import { stripe } from '@/actions/stripe'
import { CurrencyCode } from '@/actions/price'

type StripeProductCreateParamsWithPriceData = Omit<
  Stripe.ProductCreateParams,
  'default_price_data'
> & {
  // amount?: number
  // interval?: 'day' | 'week' | 'month' | 'year' | undefined
  default_price_data?: Omit<
    Stripe.ProductCreateParams.DefaultPriceData,
    'currency'
  >
}

export async function createStripeProduct(
  options: StripeProductCreateParamsWithPriceData,
) {
  const { default_price_data, name, description, metadata } = options

  const product = (await stripe.products.create({
    ...options,
    name,
    default_price_data: default_price_data
      ? {
          ...default_price_data,
          currency: CurrencyCode.USD,
        }
      : undefined,
    description,
    metadata,
    expand: ['default_price'],
  })) as Stripe.Response<Stripe.Product & { default_price: Stripe.Price }>

  return product
}
