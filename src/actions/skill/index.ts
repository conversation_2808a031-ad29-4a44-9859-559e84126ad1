import { z } from 'zod'
import { RecordId, StringRecordId } from 'surrealdb'

export const SkillSchema = z.object({
  id: z.string(),
  created: z.union([z.date(), z.string()]),
  updated: z.union([z.date(), z.string()]),
  enabled: z.boolean().optional(),
  name: z.string(),
  slug: z.string(),
  category: z.enum([
    'development',
    'design',
    'platforms-tools',
    'product-types',
    'business-monetization',
  ]),
  description: z.string().optional(),
  icon: z.string().optional(),
  color: z.string().optional(),
  tags: z.array(z.string()).optional(),
})

export type Skill = z.infer<typeof SkillSchema>

export type SkillInput = {
  name: string
  slug: string
  category:
    | 'development'
    | 'design'
    | 'platforms-tools'
    | 'product-types'
    | 'business-monetization'
  description?: string
  icon?: string
  color?: string
  tags?: string[]
}

export const SkillCategory = {
  DEVELOPMENT: 'development',
  DESIGN: 'design',
  PLATFORMS_TOOLS: 'platforms-tools',
  PRODUCT_TYPES: 'product-types',
  BUSINESS_MONETIZATION: 'business-monetization',
} as const

export type SkillCategory = (typeof SkillCategory)[keyof typeof SkillCategory]
