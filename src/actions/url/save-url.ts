'use server'

import { Surrealized, surql } from '@/io/surreal'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'
import { HOST } from '@/config'

import { type ShortUrl } from '@/actions/url'

export async function saveUrl(url: string): Promise<string> {
  const surreal = await Surrealized()

  try {
    logger.info(`Saving URL: <${maskId(url)}>, url`)

    const [shortUrls] = await surreal.query<[ShortUrl[]]>(
      surql`INSERT INTO ShortUrl { url: $url }
        ON DUPLICATE KEY UPDATE
          updated = time::now()
      ;`,
      {
        url,
      },
    )

    const shortUrl = shortUrls?.shift()
    if (!shortUrl) throw new Error('Failed to save URL')

    return `${HOST}/go/${shortUrl.id.id as string}`
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
