'use server'

import { Surrealized, surql } from '@/io/surreal'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

import { type ShortUrl } from '@/actions/url'

type OnlyUrl = Omit<ShortUrl, 'id' | 'updatedAt'>

export async function getUrl(id: string): Promise<string> {
  const surreal = await Surrealized()

  try {
    logger.info(`Saving URL: <${maskId(id)}>, id`)

    const [{ url }] = await surreal.query<[OnlyUrl]>(
      surql`
        UPDATE ONLY type::thing($id)
          SET
            updated = time::now(),
            usage = (SELECT url, usage FROM ONLY type::thing($id)).usage + 1
          RETURN url;`,
      {
        id: String(`ShortUrl:${id}`),
      },
    )

    return url
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
