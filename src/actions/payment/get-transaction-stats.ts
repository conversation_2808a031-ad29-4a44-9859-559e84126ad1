'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'
import logger from '@/utils/logger'
import { startOfDay, endOfDay } from 'date-fns'

export interface TransactionStats {
  totalTransactions: number
  todayVolume: number
  todayTransactions: number
  currency: string
}

export async function getTransactionStats(): Promise<TransactionStats> {
  const surreal = await Surrealized()

  try {
    logger.info(`getTransactionStats()`)

    const today = new Date()
    const startOfToday = startOfDay(today).toISOString()
    const endOfToday = endOfDay(today).toISOString()

    // Get total transactions count
    const totalCountResponse = await surreal.query<[{ count: number }[]]>(
      surql`
        SELECT count() as count
        FROM PaymentLog
        WHERE complete = true
          AND enabled = true
        GROUP ALL
      ;`,
    )

    // Get today's transactions and volume
    const todayStatsResponse = await surreal.query<
      [
        {
          count: number
          totalAmount: number
          currency: string
        }[],
      ]
    >(
      surql`
        SELECT 
          count() as count,
          math::sum(product.price.amount) as totalAmount,
          array::first(product.price.currency) as currency
        FROM PaymentLog
        WHERE complete = true
          AND enabled = true
          AND created >= type::datetime($startOfToday)
          AND created <= type::datetime($endOfToday)
        GROUP ALL
        FETCH product.price
      ;`,
      { startOfToday, endOfToday },
    )

    const totalCount = totalCountResponse?.[0]?.[0]?.count || 0
    const todayStats = todayStatsResponse?.[0]?.[0] || {
      count: 0,
      totalAmount: 0,
      currency: 'USD',
    }

    return {
      totalTransactions: totalCount,
      todayVolume: todayStats.totalAmount || 0,
      todayTransactions: todayStats.count || 0,
      currency: todayStats.currency || 'USD',
    }
  } catch (err) {
    logger.error(`getTransactionStats error: ${err?.toString()}`)
    return {
      totalTransactions: 0,
      todayVolume: 0,
      todayTransactions: 0,
      currency: 'USD',
    }
  }
}
