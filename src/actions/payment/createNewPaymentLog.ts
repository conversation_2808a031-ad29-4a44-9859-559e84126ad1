'use server'

import { jsonify, surql, Surrealized } from '@/io/surreal'
import merge from 'lodash/merge'

import { getIdFromRecord } from '@/utils'
import { getPlatform, getPlatformValue } from '@/utils/get-platform'
import { encodeId } from '@/utils/hash-id'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'
import { getPaymentLogById, PaymentLog } from '@/actions/payment'
import { getProductById, PlatformsEnum, ProductStatus } from '@/actions/product'
import { createTransferSend } from '@/actions/transfer'

export async function startPaymentLog(
  sellerId: string,
  buyerId: string,
  productId: string,
  metadata: any,
) {
  const surreal = await Surrealized()

  try {
    logger.info(
      `startPaymentLog(sellerId: ${maskId(sellerId)}, buyerId: ${maskId(buyerId)}, productId: ${maskId(productId)}, metadata: ${JSON.stringify(metadata)}`,
    )

    const response = await surreal.query<[[PaymentLog]]>(
      surql`INSERT INTO PaymentLog {
        seller: type::thing($sellerId),
        buyer: type::thing($buyerId),
        product: type::thing($productId),
        metadata: $metadata
      }`,
      { sellerId, buyerId, productId, metadata },
    )

    const paymentLog = response?.shift()?.shift() ?? null
    return jsonify(paymentLog) as PaymentLog
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}

async function completePaymentLog(
  paymentId: string,
  hashId: string,
  metadata: any,
) {
  const surreal = await Surrealized()

  try {
    logger.info(
      `completePaymentLog(paymentId: ${maskId(paymentId)}, hashId: ${maskId(hashId)}, metadata: ${JSON.stringify(metadata)}`,
    )

    const response = await surreal.query<[[PaymentLog, PaymentLog]]>(
      surql`
        UPDATE type::thing($paymentId)
          SET
            hashId = $hashId,
            complete = true,
            metadata = $metadata;
        SELECT * FROM type::thing($paymentId)
          FETCH
            seller, buyer, product, product.price, product.children
      ;`,
      { paymentId, hashId, metadata },
    )

    const paymentLog = response?.pop()?.shift() ?? null
    return jsonify(paymentLog) as PaymentLog
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}

export async function continuePaymentLog(paymentLogId: string, metadata: any) {
  try {
    logger.info(
      `continuePaymentLog(paymentLogId: ${maskId(paymentLogId)}, metadata: ${JSON.stringify(metadata)}`,
    )
    const paymentLog = await getPaymentLogById(paymentLogId)

    const product = await getProductById(
      paymentLog.product.id,
      ProductStatus.PUBLISHED,
    )

    // creating product transfer
    const transfers = []
    if (
      getPlatform(product?.platforms) === PlatformsEnum.BUNDLE ||
      getPlatform(product?.platforms) === PlatformsEnum.SUBSCRIPTION
    ) {
      const platform = getPlatformValue(product?.platforms)
      for await (const childProduct of platform.assets) {
        const transfer = await createTransferSend(
          true,
          paymentLog.seller.id as string,
          paymentLog.buyer.id as string,
          childProduct.id,
          paymentLog.id.toString(),
        )
        transfers.push(transfer)
      }
    } else {
      const transfer = await createTransferSend(
        true,
        paymentLog.seller.id as string,
        paymentLog.buyer.id as string,
        paymentLog.product.id as string,
        paymentLog.id.toString(),
      )

      transfers.push(transfer)
    }

    const hashId = encodeId(getIdFromRecord(paymentLog.id))

    const mergedMetadata = merge({}, paymentLog.metadata, metadata)

    const updated = await completePaymentLog(paymentLog.id as string, hashId, {
      ...mergedMetadata,
      transfers,
    })

    return updated
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    return null
  }
}
