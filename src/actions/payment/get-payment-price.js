"use server";

import logger from "@/utils/logger";
import maskId from "@/utils/mask-id";

/** @type {import(".").getPaymentPrice} */
export async function getPaymentPrice(priceId, userId) {
  try {
    logger.info(
      `Getting payment price, <${maskId(priceId)}>, priceId, <${maskId(userId)}>, userId`
    );

    // const paymentPrice = await prisma.paymentPrice.findFirst({
    //   where: { enabled: true, id: priceId, owner: { id: userId } },
    //   include: { method: true },
    // });

    // return paymentPrice;
  } catch (err) {
    logger.error(`${err.toString()}`);
    return null;
  }
}
