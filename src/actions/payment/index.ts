import { RecordId, StringRecordId } from 'surrealdb'

import { User } from '@/actions/user'
import { Product } from '@/actions/product'
import { TransferSend } from '@/actions/transfer'

export type PaymentLog = {
  id: RecordId | StringRecordId | string
  hashId?: string
  seller: User
  buyer: User
  product: Product
  metadata?: Record<string, any>
  created: Date | string
}

export type PaymentMethod = {
  id: RecordId | StringRecordId | string
  created: Date | string
  enabled?: boolean
  updated?: Date | string
  name: string
  slug: string
  options?: object
}

export type Purchase = PaymentLog & {
  transfers: TransferSend[]
}

export { getPaymentMethods } from './get-payment-methods'

export { getPaymentPrice } from './get-payment-price'
export { getPaymentLogBySeller } from './get-payment-log-by-seller'
export { getPaymentLogById } from './get-payment-log-by-id'
export { checkIfUserBoughtAlready } from './check-if-user-bought-already'
export * from './createNewPaymentLog'
