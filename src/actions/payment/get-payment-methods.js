"use server";

import logger from "@/utils/logger";

/** @type {import(".").getPaymentMethods} */
export async function getPaymentMethods() {
  try {
    logger.info(`Getting payment methods, all enabled`);

    // const paymentMethods = await prisma.paymentMethod.findMany({
    //   where: { enabled: true },
    // });

    // return paymentMethods;
  } catch (err) {
    logger.error(`${err.toString()}`);
    return null;
  }
}
