'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'
import logger from '@/utils/logger'
import { PaymentLog } from '@/actions/payment'

export interface TransactionLogEntry extends PaymentLog {
  type: 'purchase' | 'sale'
  buyerName: string
  sellerName: string
  buyerEmail: string
  sellerEmail: string
  productName: string
  amount: number
  currency: string
  paymentStatus: 'complete' | 'pending' | 'failed'
}

export interface TransactionLogsResponse {
  logs: TransactionLogEntry[]
  total: number
}

export async function getAllTransactionLogs(
  limit: number = 50,
  offset: number = 0,
  sortBy: string = 'created',
  sortOrder: 'asc' | 'desc' = 'desc',
): Promise<TransactionLogsResponse> {
  const surreal = await Surrealized()

  try {
    logger.info(
      `getAllTransactionLogs(limit:${limit}, offset:${offset}, sortBy:${sortBy}, sortOrder:${sortOrder})`,
    )

    // Map frontend column names to database field names
    const sortFieldMap: Record<string, string> = {
      date: 'created',
      id: 'id',
      buyer: 'buyer.name',
      seller: 'seller.name',
      product: 'product.name',
      amount: 'product.price.amount',
      status: 'complete',
      created: 'created',
    }

    const sortField = sortFieldMap[sortBy] || 'created'
    const orderDirection = sortOrder.toUpperCase()

    // Get total count
    const countResponse = await surreal.query<[{ count: number }[]]>(
      surql`
        SELECT count() as count
        FROM PaymentLog
        WHERE enabled = true
        GROUP ALL
      ;`,
    )

    // Build dynamic query string for sorting
    const queryString = `
      SELECT 
        *,
        id,
        buyer.name as buyerName,
        seller.name as sellerName,
        buyer.email as buyerEmail,
        seller.email as sellerEmail,
        product.name as productName,
        product.price.amount as amount,
        product.price.currency as currency,
        (IF complete = true THEN 'complete' ELSE 'pending' END) as paymentStatus,
        'purchase' as type
      FROM PaymentLog
      WHERE enabled = true
      ORDER BY ${sortField} ${orderDirection}
      LIMIT $limit
      START $offset
      FETCH
        buyer,
        seller,
        product,
        product.price
    ;`

    // Get paginated results with sorting
    const response = await surreal.query<[TransactionLogEntry[]]>(queryString, {
      limit,
      offset,
    })

    const total = countResponse?.[0]?.[0]?.count || 0
    const values = response?.shift()
    const logs = values ? jsonify<TransactionLogEntry[]>(values) : []

    return { logs, total }
  } catch (err) {
    logger.error(`getAllTransactionLogs error: ${err?.toString()}`)
    throw err
  }
}
