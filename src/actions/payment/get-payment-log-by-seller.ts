'use server'
import { Surrealized, surql, jsonify } from '@/io/surreal'

import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

import { PaymentLog } from '@/actions/payment'

export async function getPaymentLogBySeller(sellerId: string) {
  const surreal = await Surrealized()

  try {
    logger.info(`getPaymentLogBySeller(sellerId: ${maskId(sellerId)})`)

    const response = await surreal.query<[PaymentLog[]]>(
      surql`
        SELECT * FROM PaymentLog
            WHERE seller = type::thing($sellerId)
            FETCH product, product.price, product.imageSet, product.imageSet.images, buyer
        ;
      `,
      {
        sellerId,
      },
    )

    const output = response?.shift() ?? []
    return jsonify<PaymentLog[]>(output)
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
