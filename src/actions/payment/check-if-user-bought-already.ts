'use server'

import { Surrealized, surql } from '@/io/surreal'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'
import { idToRecord } from '@/utils/idToRecord'

import { PaymentLog } from '@/actions/payment'

/**
 * @function checkIfUserBoughtAlready
 * @description Check if a user has bought a product already.
 * @param {string} userId - The ID of the user.
 * @param {string} productId - The ID of the product.
 * @returns {Promise<boolean>} Whether the user has bought the product already.
 */
export async function checkIfUserBoughtAlready(
  userId: string,
  productId: string,
): Promise<boolean> {
  const surreal = await Surrealized()

  try {
    logger.trace(
      `checkIfUserBoughtAlready(userId: ${maskId(userId)}, productId: ${maskId(productId)})`,
    )

    const response = await surreal.query<[[PaymentLog]]>(
      surql`
        SELECT id FROM PaymentLog
          WHERE buyer = type::thing($userId)
            AND product = type::thing($productId)
            AND complete = true
      ;`,
      {
        userId: idToRecord('User', userId),
        productId: idToRecord('Product', productId),
      },
    )

    const paymentLog = response?.shift()?.shift() ?? null
    return Boolean(paymentLog)
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    return false
  }
}
