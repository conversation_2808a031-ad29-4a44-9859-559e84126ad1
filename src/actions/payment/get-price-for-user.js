"use server";

import logger from "@/utils/logger";
import maskId from "@/utils/mask-id";
// import prisma from "@/data/prisma";

/** @type {import(".").getPricesForUser} */
export async function getPricesForUser(userId) {
  try {
    logger.info(`Getting payment prices for user, ${maskId(userId)}, userId`);

    // const paymentMethods = await prisma.paymentMethod.findMany({
    //   where: { enabled: true },
    // });

    // return paymentMethods;
  } catch (err) {
    logger.error(`${err.toString()}`);
    return null;
  }
}
