'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'
import { PaymentLog } from '@/actions/payment'
import { TransferSend } from '@/actions/transfer'

type Purchase = PaymentLog & {
  transfers: TransferSend[]
}

export async function getPaymentLogById(paymentLogId: string) {
  const surreal = await Surrealized()

  try {
    logger.info(`getPaymentLogById(${maskId(paymentLogId)})`)

    const response = await surreal.query<[[Purchase]]>(
      surql`
        SELECT *, id, buyer, (
          SELECT * FROM TransferSend
            WHERE { paymentLog: id, toUser: buyer }
            ORDER BY created DESC
          ) AS transfers FROM type::thing($paymentLogId)
          FETCH
            buyer,
            seller,
            product,
            product.fileSet,
            product.fileSet.files,
            product.price,
            product.children;
      `,
      { paymentLogId },
    )

    return jsonify(response?.shift()?.shift() ?? null) as Purchase
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
