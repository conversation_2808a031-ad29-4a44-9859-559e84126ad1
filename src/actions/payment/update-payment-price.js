// "use server";

// import { prisma } from '@/io/prisma';
// import logger from "@/utils/logger";
// import maskId from "@/utils/mask-id";

// /** @type {import(".").updatePaymentPrice} */
// export async function updatePaymentPrice(priceId, userId, data) {
//   try {
//     logger.info(`Updating PaymentPrice, priceId: <${maskId(priceId)}>`);

//     const { method, ...price } = data.price;

//     const paymentPrice = await prisma.paymentPrice.update({
//       where: {
//         id: priceId,
//         owner: { id: userId },
//         enabled: true,
//       },
//       data: {
//         ...price,
//         ...(method
//           ? {
//               method: {
//                 connect: {
//                   id: method.id,
//                 },
//               },
//             }
//           : {}),
//       },
//     });

//     return paymentPrice;
//   } catch (err) {
//     logger.error(`${err.toString()}`);
//     return null;
//   }
// }
