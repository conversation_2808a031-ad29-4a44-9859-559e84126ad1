import { z } from 'zod'

export const FileSchema = z.object({
  id: z.string(),
  created: z.union([z.date(), z.string()]),
  updated: z.union([z.date(), z.string()]),
  // owner: z.instanceof(User),
  owner: z.any(),
  name: z.string().optional(),
  tags: z.array(z.string()).optional(),
  flair: z.array(z.string()).optional(),
  mimeType: z.string().optional(),
  url: z.string().optional(),
  metadata: z.record(z.string(), z.any()).optional(),
})

export type File = z.infer<typeof FileSchema>

export const FileSetSchema = z.object({
  id: z.string(),
  created: z.union([z.date(), z.string()]),
  updated: z.union([z.date(), z.string()]),
  files: z.array(FileSchema),
  metadata: z.record(z.string(), z.any()).optional(),
  // parent: z.instanceof(Product).optional(),
  parent: z.any(),
})

export type FileSet = z.infer<typeof FileSetSchema>

export * from './create-file'
export * from './save-file-url'
export * from './upload-file'
