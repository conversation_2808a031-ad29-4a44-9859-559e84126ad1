'use server'

import crypto from 'crypto'
import { File } from 'buffer'

import { S3Client } from '@aws-sdk/client-s3'
import { PutObjectCommand } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'

import {
  AWS_KEY,
  AWS_REGION,
  AWS_SECRET,
  FILE_SECRET,
  S3_FILES_BUCKET,
} from '@/config'
import { getSessionUser } from '@/actions/auth'
import { getUserById } from '@/actions/user'

import { createFile, saveFileUrl } from '@/actions/file'
import { getIdFromRecord } from '@/utils'

type UploadMetadata = {
  name?: string
  size?: number
  type?: string
  lastModified?: number
}

export type FileFormData = FormData & {
  file: File
  meta: string
}

// function validateImageMeta(file: File, meta: UploadMetadata) {
//   if (meta.size === 0 || file.size === 0) {
//     throw new Error('No image size found')
//   }

//   if (file.size !== meta.size) {
//     throw new Error('Image size does not match')
//   }

//   if (file.type !== meta.type) {
//     throw new Error('Image type does not match')
//   }

//   if (file.lastModified - meta.lastModified > 2000) {
//     throw new Error('Image timestamp is out of sync')
//   }

//   if (file.name !== meta.name) {
//     throw new Error('Image name does not match')
//   }

//   return true
// }

function createTokenString(prefix: string) {
  const preToken = `createFileToken-${prefix}-${FILE_SECRET}`
  const hash = crypto.createHash('md5').update(preToken).digest('hex')
  return hash
}

function getIndexOnThree(meta: UploadMetadata) {
  const indexSize = (meta?.size ?? 0) % 3
  const indexLast = Math.floor((meta?.lastModified ?? 0) / 100000) % 3
  const indexName = parseInt(meta?.name ?? '', 16) % 3
  const index = (indexSize + indexLast + indexName) % 3
  return index
}

function convertEntryToBlob(entry: FormDataEntryValue) {
  if (entry instanceof File) {
    return entry
  } else {
    throw new Error('Invalid file type')
  }
}

export async function getUploadToken(meta: UploadMetadata) {
  const index = getIndexOnThree(meta)
  const metaAttributes: (keyof UploadMetadata)[] = ['name', 'size', 'type']
  const md5 = createTokenString(String(meta[metaAttributes[index]]))
  return md5
}

export async function getUploadUrl(
  folder: string,
  name: string,
  contentType: string,
  token: string,
  meta: UploadMetadata,
) {
  const localToken = await getUploadToken(meta)
  if (localToken !== token) {
    throw new Error('Invalid token')
  }

  // Verificar se usuário está autenticado
  const sessionUser = await getSessionUser()
  const user = await getUserById(sessionUser?.id?.toString())
  if (!user) {
    throw new Error('User not found or disabled')
  }

  const s3Client = new S3Client({
    region: AWS_REGION,
    credentials: {
      accessKeyId: AWS_KEY!,
      secretAccessKey: AWS_SECRET!,
    },
  })

  const fileName = `${String(getIdFromRecord(user.id))}/${crypto.randomUUID()}.${name.split('.')[1]}`
  const s3Key = `files/${folder}/${fileName}`

  const command = new PutObjectCommand({
    Bucket: S3_FILES_BUCKET,
    Key: s3Key,
    ContentType: contentType,
    // Metadata: {
    //   'x-amz-meta-origin': 'http://localhost:3000', // Substitua pelo seu domínio
    // },
  })

  const signedUrl = await getSignedUrl(s3Client, command, {
    expiresIn: 3600,
    // signableHeaders: new Set([
    //   'Content-Type',
    //   'Origin',
    //   'Access-Control-Request-Method',
    //   'Access-Control-Request-Headers',
    // ]),
  })

  const publicUrl = `https://${S3_FILES_BUCKET}.s3.amazonaws.com/${s3Key}`

  const fileCreated = await createFile(
    user?.id?.toString(),
    fileName,
    [],
    contentType,
    publicUrl,
    { ...meta, signedUrl: signedUrl },
  )

  if (!fileCreated) {
    throw new Error('Failed creating File')
  }

  return { url: signedUrl, fileName, publicUrl, fileCreated }
}
