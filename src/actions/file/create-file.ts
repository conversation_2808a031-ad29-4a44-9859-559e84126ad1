'use server'

import { Surrealized, jsonify, surql } from '@/io/surreal'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

import { type File as FileRecord } from '@/actions/file'

export async function createFile(
  ownerId: string,
  name: string,
  tags: string[] = [],
  mimeType?: string,
  url?: string,
  metadata: Record<string, any> = {},
): Promise<FileRecord | undefined> {
  const surreal = await Surrealized()

  try {
    logger.trace(`createFile(ownerId: ${maskId(ownerId)}, name: ${name})`)

    const response = await surreal.query<[[FileRecord]]>(
      surql`
        INSERT INTO File {
          owner: type::thing($ownerId),
          created: time::now(),
          updated: time::now(),
          name: $name,
          tags: $tags,
          flair: array::union(flair OR [], $flair),
          mimeType: $mimeType,
          metadata: $metadata,
          url: $url
        };
      `,
      {
        ownerId,
        name,
        url,
        tags,
        mimeType,
        metadata,
        flair: metadata?.flair ?? [],
      },
    )

    const output = response?.shift()?.shift() ?? undefined
    return output ? jsonify<FileRecord>(output) : undefined
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
