'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'
import { logger, maskId } from '@/utils'
import { type FileSet, type File as FileModel } from '.'
import { idToRecord } from '@/utils/idToRecord'

export async function createFileSet(
  parentId: string,
  files: string[],
): Promise<FileSet | undefined> {
  const surreal = await Surrealized()

  try {
    logger.trace(
      `createFileSet(parentId: ${maskId(parentId)}, images: ${files.length})`,
    )

    const response = await surreal.query<[[FileSet]]>(
      surql`
        INSERT INTO FileSet {
          parent: type::thing($parentId),
          files: array::union(files OR [], array::map($files, |$i|type::thing($i)))
        };
      `,
      {
        parentId: idToRecord('Product', parentId),
        files,
      },
    )

    const fileSet = response?.pop()?.shift() ?? undefined
    return fileSet ? jsonify<FileSet>(fileSet) : undefined
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
