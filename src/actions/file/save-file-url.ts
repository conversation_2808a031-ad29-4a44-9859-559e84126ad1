'use server'

import { Surrealized, jsonify, surql } from '@/io/surreal'
import { logger, maskId } from '@/utils'
import { idToRecord } from '@/utils/idToRecord'
import { type File } from '@/actions/file'

/**
 * Save the URL of an image
 */
export async function saveFileUrl(
  file: string,
  url: string,
): Promise<File | undefined> {
  const surreal = await Surrealized()

  try {
    logger.trace(`saveFileUrl(file: ${maskId(file)}, url: ${maskId(url, 9)})`)

    const response = await surreal.query<[File[]]>(
      surql`
        UPDATE type::thing($file)
          SET
            url = $url,
            updated = time::now()
        ;
        SELECT * FROM type::thing($file);
      `,
      {
        file: idToRecord('File', file),
        url,
      },
    )

    const output = response?.pop()?.pop() ?? undefined
    return output ? jsonify<File>(output) : undefined
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
