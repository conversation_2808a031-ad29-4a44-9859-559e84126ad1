import { User as UserType, UserInSession } from '@/actions/user'
import {
  Home,
  Compass,
  LucideIcon,
  User,
  Settings,
  ShoppingCart,
  BadgeDollarSign,
} from 'lucide-react'

export type MenuItem = {
  label?: string
  icon?: LucideIcon
  url?: string
  count?: number
  subItems?: MenuItem[]
}

export function getMenuItems(user?: UserInSession | UserType): MenuItem[] {
  return [
    {
      label: 'Home',
      icon: Home,
      url: '/',
    },
    {
      label: 'Explore',
      icon: Compass,
      url: '/explore',
    },
    ...(user
      ? [
          {
            label: 'My Profile',
            icon: User,
            url: `/user/${user.handler}`,
          },
          ...(user.hats?.find(hat => hat.slug === 'creator')
            ? [
                {
                  label: 'Sales',
                  icon: BadgeDollarSign,
                  url: '/dashboard/sales',
                },
              ]
            : []),
          {
            label: 'Purchases',
            icon: ShoppingCart,
            url: '/dashboard/purchases',
          },
          {
            label: 'Settings',
            icon: Settings,
            url: '/user/settings',
          },
          // {
          //   label: 'Subscriptions',
          //   icon: UserPlus,
          //   url: '/subscriptions',
          // },
        ]
      : []),
  ]
}
