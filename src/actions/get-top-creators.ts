'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'

import { TopCreator } from '@/actions/user'
import logger from '@/utils/logger'

/**
 *
 */
export async function getTopCreators(
  limit: number = 0,
): Promise<TopCreator[] | undefined> {
  if (limit <= 0) {
    return [] as TopCreator[]
  }

  const surreal = await Surrealized()

  try {
    const response = await surreal.query<[TopCreator[]]>(
      surql`
        RETURN array::map(
            (
                SELECT count() as contributions, owner FROM Product
                    WHERE
                        enabled = true AND
                        owner.enabled = true AND
                        owner.relationStatus != 'UserRelation/FLAT'  AND
                        owner.hats ANYINSIDE (
                            SELECT VALUE id FROM UserHat WHERE slug != 'admin'
                        )
                    GROUP BY owner
                    ORDER BY contributions DESC
                    LIMIT $limit
                    FETCH owner
            ),
            |$v| object::from_entries(array::append(object::entries($v.owner), ['contributions', $v.contributions]))
        );
      `,
      {
        limit,
      },
    )

    const totals = response?.pop() ?? null
    const output = totals ? jsonify<TopCreator[]>(totals) : undefined
    logger.trace(`getTopCreators::${output?.length ?? 0}`)
    return output
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
