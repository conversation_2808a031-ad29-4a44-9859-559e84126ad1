'use server'
import { surql } from 'surrealdb'

import { jsonify, Surrealized } from '@/io/surreal'
import logger from '@/utils/logger'

import { type TransferReceive } from '.'

export async function getReceives() {
  try {
    const surreal = await Surrealized()

    const [receives] = await surreal.query<[TransferReceive[]]>(
      surql`
        SELECT id, operation, manual, status, incomingId, product
          FROM TransferReceive
          FETCH
            product.id,
            product.name,
            product.slug
      ;`,
    )

    const output = jsonify<TransferReceive[]>(receives)
    return output
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    return null
  }
}
