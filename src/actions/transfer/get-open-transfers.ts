'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'
import logger from '@/utils/logger'
import { TransferSend, TransferSendStatus } from '@/actions/transfer'
// import maskId from '@/utils/mask-id'

export async function getOpenTransfers() {
  const surreal = await Surrealized()

  try {
    logger.trace(`getOpenTransfers()`)

    const response = await surreal.query<[[TransferSend]]>(
      surql`
        SELECT * FROM TransferSend
          WHERE status != $transferComplete AND manual = true
          ORDER BY created, updated DESC
        FETCH
          toUser,
          fromUser,
          product,
          product.children
        ;`,
      { transferComplete: String(TransferSendStatus.COMPLETE) },
    )

    const transfers = response?.shift() ?? null
    return jsonify(transfers) as TransferSend[]
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
