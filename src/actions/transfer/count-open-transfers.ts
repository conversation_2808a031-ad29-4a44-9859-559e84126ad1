'use server'

import { TransferSendStatus } from '@/actions/transfer'
import { Surrealized, jsonify, surql } from '@/io/surreal'
import logger from '@/utils/logger'

type TransferCount = {
  count: number
}

export async function countOpenTransfers() {
  const surreal = await Surrealized()

  try {
    logger.trace(`countOpenTransfers()`)

    const response = await surreal.query<[[number]]>(
      surql`
        SELECT VALUE count() FROM TransferSend
          WHERE status != $transferComplete
          GROUP ALL
        ;`,
      { transferComplete: String(TransferSendStatus.COMPLETE) },
    )

    const transfers = response?.shift()?.shift() as TransferCount | undefined
    return transfers ? jsonify<number>(transfers.count) : 0
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
