'use server'

import { Surrealized, surql } from '@/io/surreal'
import logger from '@/utils/logger'
import { VERCEL_ENV } from '@/config'

import { TransferReceiveStatus, type TransferReceive } from '@/actions/transfer'

export async function getNextReceive() {
  try {
    const surreal = await Surrealized()

    const [receive, ...rest] = await surreal.query(
      surql`SELECT *, $env FROM TransferReceive WHERE status = $status;`,
      {
        status: String(TransferReceiveStatus.PENDING),
        env: String(VERCEL_ENV),
      },
    )

    return receive as TransferReceive[]
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    return null
  }
}
