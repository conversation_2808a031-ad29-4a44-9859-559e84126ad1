"use server";

import logger from "@/utils/logger";
// import { sendTransferStatus } from "@/actions/mailing";

export async function startTransferForUser(transferId: string, userId: string) {
  try {
    return {};
    // const transfer = await prisma.transfer.update({
    //   where: {
    //     id: transferId,
    //     to: {
    //       id: userId,
    //     },
    //   },
    //   data: {
    //     updated: new Date(),
    //     status: TransferStatus.PENDING,
    //   },
    //   include: {
    //     items: {
    //       select: {
    //         name: true,
    //       },
    //     },
    //     to: {
    //       select: {
    //         email: true,
    //         name: true,
    //         handler: true,
    //       },
    //     },
    //   },
    // });
    // await sendTransferStatus({
    //   transfer,
    //   subject: 'Transfer Status: "Pending"',
    //   templateId: "transfer-status-pending",
    // });
    // return transfer;
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`);
    return [];
  }
}
