'use server'
import { type RecordId } from 'surrealdb'

import { Surrealized, surql } from '@/io/surreal'
import logger from '@/utils/logger'
import { VERCEL_ENV } from '@/config'

import { type TransferReceive } from '.'

interface OpenTransferReceive extends TransferReceive {
  logs?: any[]
}

export async function getReceiveById(id: RecordId) {
  try {
    const surreal = await Surrealized()

    const [receives] = await surreal.query<[OpenTransferReceive[]]>(
      surql`SELECT * FROM type::thing($id);`,
      {
        id: String(`TransferReceive:${id}`),
        env: String(VERCEL_ENV),
      },
    )

    const receive = receives?.shift() as OpenTransferReceive
    const logUrl = receive?.stake?.find(item => item.endsWith('log'))
    const response = logUrl ? await fetch(logUrl) : undefined
    const payload = await response?.text()
    const logs = payload
      ?.split('\n')
      .map(item => {
        if (item.trim()) {
          return { ...JSON.parse(item.trim()) }
        }
        return null
      })
      .filter(item => !!item?.message)

    if (receive) {
      receive.logs = logs
    }

    return receive
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    return null
  }
}
