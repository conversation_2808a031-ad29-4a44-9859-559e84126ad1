'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'

import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'
import { idToRecord } from '@/utils/idToRecord'
import { TransferSend } from '@/actions/transfer'

export async function createTransferSend(
  manual: boolean,
  from: string,
  to: string,
  product: string,
  paymentLogId: string,
): Promise<TransferSend> {
  const surreal = await Surrealized()

  try {
    logger.info(
      `createTransferSend(from: ${maskId(from)}, to: ${maskId(to)}, product: ${maskId(product)}, paymentLogId: ${maskId(paymentLogId)})`,
    )

    const response = await surreal.query<[[TransferSend]]>(
      surql`INSERT INTO TransferSend {
        manual: $manual,
        updated: time::now(),
        fromUser: type::thing($fromUser),
        toUser: type::thing($toUser),
        product: type::thing($product),
        paymentLog: type::thing($paymentLogId)
      } ON DUPLICATE KEY UPDATE
        manual=$manual,
        updated=time::now(),
        fromUser=type::thing($fromUser),
        toUser=type::thing($toUser),
        product=type::thing($product),
        paymentLog=type::thing($paymentLogId)
      ;`,
      {
        manual: manual,
        fromUser: idToRecord('User', from),
        toUser: idToRecord('User', to),
        product: idToRecord('Product', product),
        paymentLogId: idToRecord('PaymentLog', paymentLogId),
      },
    )

    const transferSend = response?.shift()?.shift() ?? null
    return jsonify(transferSend) as TransferSend
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
