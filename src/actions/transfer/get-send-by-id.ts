'use server'
import { type RecordId } from 'surrealdb'

import { VERCEL_ENV } from '@/config'
import { Surrealized, jsonify, surql } from '@/io/surreal'
import logger from '@/utils/logger'

import { type TransferSend } from '.'

type TransferSendWithLogs = TransferSend & {
  logs?: any[]
}

export async function getSendById(
  id: RecordId,
): Promise<TransferSendWithLogs | undefined> {
  try {
    const surreal = await Surrealized()

    const response = await surreal.query<[[TransferSend]]>(
      surql`SELECT *, env FROM type::thing($id);`,
      {
        id: String(`TransferSend:${id}`),
        env: String(VERCEL_ENV),
      },
    )

    const receive = (response?.pop()?.pop() ?? undefined) as
      | TransferSendWithLogs
      | undefined

    const logUrl = receive?.stake?.find(item => item.endsWith('.log'))

    const logPayload = logUrl ? await (await fetch(logUrl))?.text() : ''
    const logs = logPayload
      ?.split('\n')
      .map(item => {
        if (item.trim()) {
          const parsed = JSON.parse(item.trim())
          return { ...parsed }
        }
        return undefined
      })
      .map(item =>
        item?.message && Array.isArray(item?.message)
          ? { ...item, ...item?.message?.pop() }
          : item,
      )
      .filter(item => Boolean(item?.message))

    if (receive) {
      receive.logs = logs
    }

    return receive ? jsonify<TransferSendWithLogs>(receive) : undefined
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    return undefined
  }
}
