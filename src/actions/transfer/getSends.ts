'use server'
import { surql } from 'surrealdb'

import { jsonify, Surrealized } from '@/io/surreal'
import logger from '@/utils/logger'

import { type TransferSend } from '@/actions/transfer'

export async function getSends(): Promise<TransferSend[] | undefined> {
  try {
    const surreal = await Surrealized()

    const response = await surreal.query<[TransferSend[]]>(
      surql`
        SELECT *
          FROM TransferSend
          WHERE manual = false
          ORDER BY updated, created, status DESC
          FETCH
            fromUser,
            toUser,
            paymentLog,
            product
        ;
      `,
    )

    const output = response?.pop()
    return output ? jsonify<TransferSend[]>(output) : undefined
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    return undefined
  }
}
