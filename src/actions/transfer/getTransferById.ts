'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

import { type TransferSend } from '@/actions/transfer'

export async function getTransferById(
  transferId: string,
): Promise<TransferSend> {
  const surreal = await Surrealized()

  try {
    logger.info(`getTransferById(transferId: ${maskId(transferId)})`)

    const response = await surreal.query<[[TransferSend]]>(
      surql`SELECT * FROM type::thing($transferId);`,
      {
        transferId: String(
          `TransferSend:${transferId.toString().split(':').pop()}`,
        ),
      },
    )

    const transfer = response?.shift()?.shift() ?? null
    return jsonify(transfer) as TransferSend
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
