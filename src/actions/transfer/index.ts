import { RecordId, StringRecordId } from 'surrealdb'
import { type User } from '@/actions/user'
import { type Product } from '@/actions/product'
import { type PaymentLog } from '@/actions/payment'

export const enum TransferSendStatus {
  INITIAL = 'TransferSend/INITIAL',
  PROGRESS = 'TransferSend/PROGRESS',
  PENDING = 'TransferSend/PENDING',
  COMPLETE = 'TransferSend/COMPLETE',
  MULTIPLE = 'TransferSend/MULTIPLE',
  UNKNOWN = 'TransferSend/UNKNOWN',
  CANCELED = 'TransferSend/CANCELED',
}

export type TransferSend = {
  id: RecordId | StringRecordId | string
  created: Date | string
  updated: Date | string
  manual: boolean
  fromUser?: User
  toUser?: User
  product?: Product
  paymentLog?: PaymentLog
  verifyFor?: string
  stake?: string[] | null
  status: TransferSendStatus
}

export const enum TransferReceiveStatus {
  INITIAL = 'TransferReceive/INITIAL',
  PROGRESS = 'TransferReceive/PROGRESS',
  PENDING = 'TransferReceive/PENDING',
  COMPLETE = 'TransferReceive/COMPLETE',
  UNKNOWN = 'TransferReceive/UNKNOWN',
  CANCELED = 'TransferReceive/CANCELED',
}

export interface TransferReceive {
  id: RecordId | StringRecordId | string
  created?: Date | string
  updated?: Date | string
  renameTo?: string
  manual?: boolean
  product?: Product
  incomingId?: string
  status?: TransferReceiveStatus
  stake?: string[]
}

export * from './get-open-transfers'
export * from './start-transfer-for-user'
export * from './get-next-receive'
export * from './set-transfer-receive-status'
export * from './getSends'

export { getCompleteTransfers } from './getCompleteTransfers'
export { setTransferStatus } from './setTransferStatus'
export { getTransferById } from './getTransferById'
export { createTransferSend } from './createTransferSend'
