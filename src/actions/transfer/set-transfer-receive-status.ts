'use server'

import { Surrealized, surql } from '@/io/surreal'
import logger from '@/utils/logger'
import { VERCEL_ENV } from '@/config'

import { TransferReceiveStatus, type TransferReceive } from '@/actions/transfer'

export async function setTransferReceiveStatus(
  id: string,
  status: TransferReceiveStatus,
  operation: string,
  manual: boolean = false,
) {
  try {
    const surreal = await Surrealized()

    const [receive] = await surreal.query<[TransferReceive[]]>(
      surql`UPDATE type::thing($id) SET status = $status, operation = $operation, manual = $manual;`,
      {
        id: String(`TransferReceive:${id}`),
        status: String(status),
        operation: String(operation),
        manual: Boolean(manual),
      },
    )

    return { ...receive?.shift(), env: VERCEL_ENV } as TransferReceive
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    return null
  }
}
