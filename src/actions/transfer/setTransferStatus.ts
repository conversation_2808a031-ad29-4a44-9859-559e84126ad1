'use server'

import { getSessionUserOrAdmin } from '@/actions/auth/getSessionUserOrAdmin'
import { getStatusForTransferSend, sendTransferStatus } from '@/actions/mailing'
import { TransferSend, TransferSendStatus } from '@/actions/transfer'
import { Surrealized, jsonify, surql } from '@/io/surreal'
import { idToRecord } from '@/utils/idToRecord'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

export async function setTransferStatus(
  transferId: string,
  status: TransferSendStatus,
  manual = false,
) {
  const surreal = await Surrealized()

  try {
    const sessionUserOrAdmin = await getSessionUserOrAdmin('setTransferStatus')

    logger.info(
      `setTransferStatus(transferId: ${maskId(transferId)}, status: ${status}, manual: ${manual})`,
    )

    const response = await surreal.query<[[TransferSend], [TransferSend]]>(
      surql`
          UPDATE type::thing($transferId)
            SET manual = $manual, status = $status, updated = time::now()
            WHERE (
              not(type::is::null($toUser)) AND toUser = type::thing($toUser)
              OR
              type::is::null($toUser) AND id = type::thing($transferId)
            )
          ;
          SELECT * FROM type::thing($transferId)
            FETCH
              fromUser,
              toUser,
              product
          ;
      `,
      {
        transferId: idToRecord('TransferSend', transferId),
        status: String(status),
        manual: Boolean(manual),
        toUser: sessionUserOrAdmin,
      },
    )

    const transfer = response?.pop()?.shift() ?? null

    if (getStatusForTransferSend(status) && transfer) {
      await sendTransferStatus({
        transfer,
        ...getStatusForTransferSend(status),
      })
    }

    return jsonify(transfer)
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
