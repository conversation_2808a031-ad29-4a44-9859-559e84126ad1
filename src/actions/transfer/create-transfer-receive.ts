'use server'

import { Surrealized, jsonify, surql } from '@/io/surreal'

import { getSessionUser } from '@/actions/auth'
import { idToRecord } from '@/utils/idToRecord'
import logger from '@/utils/logger'

import { TransferReceive } from '@/actions/transfer'
import { getProductById, ProductStatus } from '@/actions/product'

function generateRenameTo(email: string, productId: string): string {
  return `${email}:::Product:${productId}`
}

export async function createTransferReceive(productId: string, manual = false) {
  try {
    const surreal = await Surrealized()

    const user = await getSessionUser()
    if (!user?.email) {
      throw new Error('User not found')
    }

    const product = await getProductById(productId)
    if (product?.status !== ProductStatus.DRAFT) {
      const response = await surreal.query<[[TransferReceive]]>(
        surql`SELECT * FROM TransferReceive WHERE product = type::thing($product);`,
        {
          product: idToRecord('Product', productId),
        },
      )

      const transfer = response?.shift()?.shift()
      if (transfer) {
        return jsonify<TransferReceive>(transfer)
      }
    }

    const renameTo = generateRenameTo(user.email, productId)

    logger.info(
      `createTransferReceive(productId:${productId}, renameTo:${renameTo})`,
    )

    const response = await surreal.query<[[TransferReceive]]>(
      surql`INSERT INTO TransferReceive {
        manual: $manual,
        incomingId: rand::uuid::v4(),
        created: time::now(),
        updated: time::now(),
        product: type::thing($product),
        renameTo: $renameTo
      } ON DUPLICATE KEY UPDATE
        updated = time::now(),
        product = type::thing($product)
      ;`,
      {
        manual: manual,
        product: idToRecord('Product', productId),
        renameTo: renameTo,
      },
    )

    const transfer = response?.shift()?.shift()
    return transfer ? jsonify<TransferReceive>(transfer) : undefined
  } catch (err: unknown) {
    // console.log('err', err)
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
