'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'
import logger from '@/utils/logger'

import { type User } from '@/actions/user'

export async function getUserByEmail(email: string): Promise<User | null> {
  const surreal = await Surrealized()

  try {
    const response = await surreal.query<[[User]]>(
      surql`
        SELECT * FROM User WHERE email = $email FETCH hats, paymentMethod;
      `,
      { email },
    )

    const user = response?.shift()?.shift() ?? null
    return user ? jsonify<User>(user) : null
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    return null
  }
}
