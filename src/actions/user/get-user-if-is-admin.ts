'use server'

import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

import { getSessionUser } from '@/actions/auth'
import { getUserById, User } from '@/actions/user'

export async function getUserIfIsAdmin(): Promise<User | undefined> {
  const sessionUser = await getSessionUser()
  const user = await getUserById(sessionUser?.id?.toString())

  if (user?.hats?.find(hat => hat.slug === 'admin')) {
    logger.info(`Session user is admin, ${maskId(user?.id.toString())}`)
    return user
  }

  return undefined
}
