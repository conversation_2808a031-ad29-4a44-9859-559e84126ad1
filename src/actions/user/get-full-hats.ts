'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'
import logger from '@/utils/logger'

import { type UserHat } from '@/actions/user'

/**
 * @function getFullHats
 * @description Get complete hat objects from a list of hat IDs
 * @param {string[]} hatIds - Array of hat IDs
 * @returns {Promise<UserHat[]>} Array of complete hat objects
 */
export async function getFullHats(hatIds: string[]): Promise<UserHat[]> {
  if (!hatIds || hatIds.length === 0) {
    return []
  }

  const surreal = await Surrealized()

  try {
    const response = await surreal.query<[UserHat[]]>(
      surql`
        SELECT * FROM UserHat WHERE id INSIDE $hatIds;
      `,
      { hatIds },
    )

    const hats = response?.shift()
    return hats ? jsonify<UserHat[]>(hats) : []
  } catch (err: unknown) {
    logger.error(`getFullHats error: ${(err as Error).toString()}`)
    return []
  }
}
