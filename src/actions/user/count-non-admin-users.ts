'use server'
import { surql } from 'surrealdb'

import { jsonify, Surrealized } from '@/io/surreal'
import logger from '@/utils/logger'

import { type User } from '.'

export async function countNonAdminUsers(): Promise<number | undefined> {
  try {
    const surreal = await Surrealized()

    const [users] = await surreal.query<[User[]]>(
      surql`
        SELECT VALUE count()
          FROM User
          WHERE 'admin' NOT IN hats.slug
          FETCH
            hats,
            paymentMethod
      ;`,
    )

    return users?.length ?? 0
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    return undefined
  }
}
