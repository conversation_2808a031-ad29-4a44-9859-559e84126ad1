// "use server";

// import { prisma } from '@/io/prisma';
// import logger from "@/utils/logger";

// /** @type {import(".").setUserPaymentMethod} */
// export async function setUserPaymentMethod(userId, paymentMethodId) {
//   try {
//     const paymentMethod = await prisma.paymentMethod.findUnique({
//       where: { id: paymentMethodId, enabled: true },
//     });

//     const user = await prisma.user.update({
//       where: { id: userId },
//       data: {
//         paymentMethod: {
//           connect: {
//             id: paymentMethod.id,
//           },
//         },
//       },
//     });

//     return user;
//   } catch (err) {
//     logger.error(`${err.toString()}`);
//     return null;
//   }
// }
