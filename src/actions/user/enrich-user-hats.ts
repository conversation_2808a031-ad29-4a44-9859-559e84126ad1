'use server'

import { type User, type UserHat } from '@/actions/user'
import { getFullHats } from './get-full-hats'

/**
 * @function enrichUserHats
 * @description Enrich user hats by converting IDs to full objects if necessary
 * @param {User} user - The user object
 * @returns {Promise<User>} The user with enriched hats
 */
export async function enrichUserHats(user: User): Promise<User> {
  if (!user.hats || !Array.isArray(user.hats) || user.hats.length === 0) {
    return user
  }

  // Check if hats are just IDs (strings) or already full objects
  const firstHat = user.hats[0]
  if (
    typeof firstHat === 'string' ||
    (firstHat && typeof firstHat === 'object' && !firstHat.name)
  ) {
    // Hats are IDs, need to fetch full objects
    const hatIds = user.hats
      .map(hat => (typeof hat === 'string' ? hat : String(hat)))
      .filter(Boolean)

    if (hatIds.length > 0) {
      const fullHats = await getFullHats(hatIds)
      if (fullHats.length > 0) {
        user.hats = fullHats
      }
    }
  }

  return user
}
