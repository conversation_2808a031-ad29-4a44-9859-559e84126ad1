'use server'

import { Surrealized, jsonify, surql } from '@/io/surreal'
import { getSessionUserOrAdmin } from '@/actions/auth/getSessionUserOrAdmin'
import { User } from '@/actions/user'
import { idToRecord } from '@/utils/idToRecord'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'
import { getFullAuthSession } from '@/io/next-auth-config'
import { saveSessionV2 } from '@/actions/auth/save-session'

export async function updateUserAvatar(
  userId: string,
  avatarUrl: string,
): Promise<User | null> {
  const surreal = await Surrealized()

  try {
    const sessionUserOrAdmin = await getSessionUserOrAdmin('updateUserAvatar')

    logger.info(
      `updateUserAvatar(userId: ${maskId(userId)}, avatarUrl: ${avatarUrl})`,
    )

    if (
      sessionUserOrAdmin !== null &&
      sessionUserOrAdmin.toString() !== userId
    ) {
      logger.error(
        `updateUserAvatar: sessionUserOrAdmin:${sessionUserOrAdmin.toString()} userId:${userId}`,
      )
      throw new Error('Unauthorized')
    }

    const response = await surreal.query<[[User]]>(
      surql`
        UPDATE type::thing($userId) SET 
          avatar = $avatarUrl,
          updated = time::now()
        WHERE enabled = true;
        SELECT * FROM type::thing($userId) FETCH hats, paymentMethod;
      `,
      {
        userId: idToRecord('User', userId),
        avatarUrl,
      },
    )

    const user = response?.shift()?.shift()
    const updatedUser = user ? jsonify<User>(user) : null

    if (updatedUser) {
      // Atualizar a sessão com o novo avatar
      const session = await getFullAuthSession()

      if (session?.providerId && session?.provider) {
        await saveSessionV2({
          email: updatedUser.email,
          provider: session.provider,
          providerId: session.providerId,
          credentials: {},
          meta: {
            ...updatedUser,
            providerId: session.providerId,
          },
        })
      }
    }

    return updatedUser
  } catch (err) {
    logger.error(`updateUserAvatar error: ${(err as Error).toString()}`)
    throw err
  }
}
