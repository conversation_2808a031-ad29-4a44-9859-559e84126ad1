'use server'

import { Surrealized, jsonify, surql } from '@/io/surreal'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

import { type User } from '@/actions/user'

export async function getUserByHandler(
  handler: User['handler'],
): Promise<User | null> {
  const surreal = await Surrealized()

  try {
    logger.info(`Getting user, <${maskId(handler)}>, by handler`)

    const response = await surreal.query<[User[]]>(
      surql`SELECT * FROM User WHERE handler = $handler AND enabled = true LIMIT 1;`,
      { handler },
    )

    const user = response?.shift()?.shift()
    const output = user ? jsonify<User>(user) : null
    return output
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    return null
  }
}
