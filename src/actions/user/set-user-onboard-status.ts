'use server'
import { Surrealized } from '@/io/surreal'
import logger from '@/utils/logger'
import { updateOnboardStatus } from '@/data/user/update-onboard-status'
import { getUserById } from './getUserById'
import { UserOnboard } from './index'

export async function setUserOnboardStatus(userId: string, status: string) {
  try {
    // Validate user exists
    const user = await getUserById(userId)

    if (!user) {
      throw new Error('User not found')
    }

    // Validate status
    const validStatuses = [
      UserOnboard.NONE,
      UserOnboard.PENDING,
      UserOnboard.ONBOARDED,
    ]

    logger.info(`onboard status: ${status as UserOnboard}`)

    if (!validStatuses.includes(status as UserOnboard)) {
      throw new Error('Invalid onboard status')
    }

    const updatedUser = await updateOnboardStatus(userId, status)
    return updatedUser ?? null
  } catch (err: unknown) {
    logger.error(`${(err as <PERSON>rror).toString()}`)
    return null
  }
}
