import { getSessionAdmin } from '@/actions/auth/get-session-admin'
import { getUserById } from '@/actions/user/getUserById'
import { updateRelationStatus } from '@/data/user/update-relation-status'
import { getIdFromRecord } from '@/utils'

export async function adminUpdateUserRelationStatus(
  userId: string,
  newStatus: string,
) {
  try {
    const admin = await getSessionAdmin()
    if (!admin) {
      throw new Error('Not an admin')
    }

    const user = await getUserById(userId)
    if (!user) {
      throw new Error('User not found')
    }

    if (user.relationStatus === newStatus) {
      return user
    }

    const updatedUser = await updateRelationStatus(
      getIdFromRecord(user.id),
      newStatus,
    )
    if (!updatedUser) {
      throw new Error('Error updating user relation status')
    }

    return updatedUser
  } catch (error) {
    throw new Error('Error updating user relation status')
  }
}
