'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

import { type User, type UserHat } from '@/actions/user'
import { idToRecord } from '@/utils/idToRecord'
import { getFullHats } from './get-full-hats'

/**
 * @function getUserWithFullHats
 * @description Get a user by ID with complete hat objects (not just IDs)
 * @param {string} userId - The ID of the user
 * @returns {Promise<User | null>} The user with complete hat objects, or null if not found
 */
export async function getUserWithFullHats(
  userId: string,
): Promise<User | null> {
  const surreal = await Surrealized()

  try {
    if (process.env.NODE_ENV === 'development') {
      logger.trace(`getUserWithFullHats(userId: ${maskId(userId)})`)
    }

    // First, search for the user
    const userResponse = await surreal.query<[[User]]>(
      surql`
        SELECT * FROM type::thing($userId) WHERE enabled = true;
      `,
      { userId: idToRecord('User', userId) },
    )

    const user = userResponse?.shift()?.shift()
    if (!user) return null

    // If the user has hats, search for the complete hat objects
    if (user.hats && Array.isArray(user.hats) && user.hats.length > 0) {
      const hatIds = user.hats
        .map(hat => (typeof hat === 'string' ? hat : String(hat.id)))
        .filter(Boolean)

      if (hatIds.length > 0) {
        const fullHats = await getFullHats(hatIds)
        if (fullHats.length > 0) {
          user.hats = fullHats
        }
      }
    }

    return jsonify<User>(user)
  } catch (err: unknown) {
    logger.error(`getUserWithFullHats error: ${(err as Error).toString()}`)
    throw err
  }
}
