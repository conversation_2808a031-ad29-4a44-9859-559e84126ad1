'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'
import { getSessionAdmin } from '@/actions/auth/get-session-admin'
import { getUserById } from '@/actions/user/getUserById'
import { idToRecord } from '@/utils/idToRecord'
import { type User } from '@/actions/user'

export async function updateCreatorPermissions(
  userId: string,
  canAddContent: boolean,
): Promise<User | null> {
  try {
    const admin = await getSessionAdmin()
    if (!admin) {
      throw new Error('Not an admin')
    }

    const user = await getUserById(userId)
    if (!user) {
      throw new Error('User not found')
    }

    // Check if user is a creator
    const isCreator = user.hats?.some(hat => hat.slug === 'creator')
    if (!isCreator) {
      throw new Error('User is not a creator')
    }

    logger.info(
      `Updating creator permissions for user ${maskId(userId)}: ${canAddContent}`,
    )

    const surreal = await Surrealized()

    const response = await surreal.query<[User, User]>(
      surql`
        UPDATE User SET 
          profile.permissions.canAddContent = $canAddContent
        WHERE id = type::thing($userId);
        SELECT * FROM type::thing($userId) FETCH hats, paymentMethod;
      `,
      {
        userId: idToRecord('User', userId),
        canAddContent,
      },
    )

    const updatedUser = response?.pop()
    return updatedUser ? jsonify<User>(updatedUser) : null
  } catch (err: unknown) {
    logger.error(`updateCreatorPermissions error: ${(err as Error).toString()}`)
    throw err
  }
}
