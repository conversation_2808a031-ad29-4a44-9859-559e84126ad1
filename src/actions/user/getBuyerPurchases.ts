'use server'

import { Purchase } from '@/actions/payment'
import { Surrealized, jsonify, surql } from '@/io/surreal'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

export async function getBuyerPurchases(
  userId: string,
  rawFlair: string = '',
  limit: number = 0,
  offset: number = 0,
): Promise<Purchase[]> {
  const surreal = await Surrealized()

  try {
    logger.trace(
      `getBuyerPurchases(userId:${maskId(userId)}, limit:${limit}, offset:${offset})`,
    )

    const singleFlair = rawFlair.split('|').shift()
    // console.log('singleFlair:singleFlair', singleFlair)

    const response = await surreal.query<[Purchase[]]>(
      surql`
        SELECT *, id, product, buyer, (
          SELECT * FROM TransferSend
            WHERE toUser = type::thing($userId) AND paymentLog = $parent.id
            ORDER BY created DESC
          ) AS transfers
        FROM PaymentLog
        WHERE complete = true
          AND enabled = true
          AND product.enabled = true
          AND buyer = type::thing($userId)
          AND (IF $singleFlair != '' THEN array::find(product.flair, $singleFlair) ELSE true END)
        ORDER BY created DESC
        LIMIT $limit
        START $offset
        FETCH
          buyer,
          seller,
          product,
          product.price,
          product.fileSet,
          product.fileSet.files,
          product.owner,
          product.platforms,
          product.platforms.*.assets,
          product.platforms.*.assets.fileSet,
          product.platforms.*.assets.fileSet.files
      ;`,
      { userId, limit, offset, singleFlair },
    )

    // console.log('response:response', response)

    const values = response?.shift()
    const data = values ? jsonify<Purchase[]>(values) : []
    // console.log('response:response', data)
    return data
  } catch (err) {
    logger.error(`${err?.toString()}`)
    throw err
  }
}
