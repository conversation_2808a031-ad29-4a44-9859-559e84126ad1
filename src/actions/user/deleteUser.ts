'use server'

import { Surrealized, surql } from '@/io/surreal'
import { getSessionUserOrAdmin } from '@/actions/auth/getSessionUserOrAdmin'
import { getSessionUser } from '@/actions/auth'
import { idToRecord } from '@/utils/idToRecord'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'
import { deleteSession } from '@/actions/auth/delete-session'

// Function to get all products owned by user
async function getUserProducts(userId: string, surreal: any) {
  // Get all products owned by the user
  const response = await surreal.query(
    surql`
      SELECT id FROM Product 
      WHERE owner = type::thing($userId) 
        AND enabled = true;
    `,
    {
      userId: idToRecord('User', userId),
    },
  )

  const products = response?.shift() || []

  return products.map((product: any) => product.id)
}

export async function deleteUser(userId: string): Promise<boolean> {
  const surreal = await Surrealized()

  try {
    const sessionUserOrAdmin = await getSessionUserOrAdmin('deleteUser')
    const currentSessionUser = await getSessionUser()

    logger.info(`deleteUser(userId: ${maskId(userId)})`)

    // Check authorization
    if (
      sessionUserOrAdmin !== null &&
      sessionUserOrAdmin.toString() !== userId
    ) {
      logger.error(
        `deleteUser: sessionUserOrAdmin:${sessionUserOrAdmin.toString()} userId:${userId}`,
      )
      throw new Error('Unauthorized')
    }

    // Get all products that will be deleted
    const userProducts = await getUserProducts(userId, surreal)

    // Delete all related entities in cascade
    logger.info(`Starting cascade deletion for user ${maskId(userId)}`)

    if (userProducts.length > 0) {
      // 1. Delete all FileSet records related to user's products
      await surreal.query(
        surql`
          DELETE FROM FileSet 
          WHERE parent IN $userProducts;
        `,
        {
          userProducts: userProducts,
        },
      )
      logger.info(
        `Deleted FileSet records for products of user ${maskId(userId)}`,
      )

      // 2. Delete all ImageSet records related to user's products
      await surreal.query(
        surql`
          DELETE FROM ImageSet 
          WHERE parent IN $userProducts;
        `,
        {
          userProducts: userProducts,
        },
      )
      logger.info(
        `Deleted ImageSet records for products of user ${maskId(userId)}`,
      )

      // 3. Delete all Product records
      await surreal.query(
        surql`
          DELETE $userProducts;
        `,
        {
          userProducts: userProducts,
        },
      )
      logger.info(`Deleted Product records for user ${maskId(userId)}`)
    }

    // 4. Delete all PaymentPrice records owned by the user
    await surreal.query(
      surql`
        DELETE FROM PaymentPrice 
        WHERE owner = type::thing($userId);
      `,
      {
        userId: idToRecord('User', userId),
      },
    )
    logger.info(`Deleted PaymentPrice records for user ${maskId(userId)}`)

    // 5. Delete all File records owned by the user
    await surreal.query(
      surql`
        DELETE FROM File 
        WHERE owner = type::thing($userId);
      `,
      {
        userId: idToRecord('User', userId),
      },
    )
    logger.info(`Deleted File records for user ${maskId(userId)}`)

    // 6. Delete all Image records owned by the user
    await surreal.query(
      surql`
        DELETE FROM Image 
        WHERE owner = type::thing($userId);
      `,
      {
        userId: idToRecord('User', userId),
      },
    )
    logger.info(`Deleted Image records for user ${maskId(userId)}`)

    // 7. Finally, delete the user from database
    const response = await surreal.query(
      surql`
        DELETE type::thing($userId) WHERE enabled = true;
      `,
      {
        userId: idToRecord('User', userId),
      },
    )

    // Only delete session if user is deleting their own account
    // Don't delete admin's session when they delete other users
    const isUserDeletingOwnAccount =
      currentSessionUser?.id?.toString() === userId

    if (isUserDeletingOwnAccount) {
      await deleteSession()
      logger.info(
        `Session deleted for user deleting their own account: ${maskId(userId)}`,
      )
    } else {
      logger.info(
        `Admin deleted user ${maskId(userId)}, admin session preserved`,
      )
    }

    logger.info(`User <${userId}> and all related data deleted successfully.`)
    logger.info(`All products owned by the user were permanently deleted.`)
    return true
  } catch (err) {
    logger.error(`deleteUser error: ${(err as Error).toString()}`)
    throw err
  }
}
