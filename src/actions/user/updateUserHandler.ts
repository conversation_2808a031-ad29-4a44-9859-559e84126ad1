'use server'

import { Surrealized, jsonify, surql } from '@/io/surreal'
import { getSessionUserOrAdmin } from '@/actions/auth/getSessionUserOrAdmin'
import { User } from '@/actions/user'
import { idToRecord } from '@/utils/idToRecord'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'
import { getFullAuthSession } from '@/io/next-auth-config'
import { saveSessionV2 } from '@/actions/auth/save-session'
import { checkIfHandleExists } from '@/data/user/check-if-handle-exists'

export async function updateUserHandler(
  userId: string,
  newHandler: string,
): Promise<User | null> {
  const surreal = await Surrealized()

  try {
    const sessionUserOrAdmin = await getSessionUserOrAdmin('updateUserHandler')

    logger.info(
      `updateUserHandler(userId: ${maskId(userId)}, newHandler: ${newHandler})`,
    )

    if (
      sessionUserOrAdmin !== null &&
      sessionUserOrAdmin.toString() !== userId
    ) {
      logger.error(
        `updateUserHandler: sessionUserOrAdmin:${sessionUserOrAdmin.toString()} userId:${userId}`,
      )
      throw new Error('Unauthorized')
    }

    // Check if the new handle already exists
    const existingUser = await checkIfHandleExists({ handler: newHandler })
    if (existingUser && existingUser.id !== userId) {
      throw new Error('Handle already taken')
    }

    const response = await surreal.query<[[User]]>(
      surql`
        UPDATE type::thing($userId) SET 
          handler = $newHandler,
          updated = time::now()
        WHERE enabled = true;
        SELECT * FROM type::thing($userId) FETCH hats, paymentMethod;
      `,
      {
        userId: idToRecord('User', userId),
        newHandler,
      },
    )

    const user = response?.shift()?.shift()
    const updatedUser = user ? jsonify<User>(user) : null

    if (updatedUser) {
      // Atualizar a sessão com o novo handle
      const session = await getFullAuthSession()

      if (session?.providerId && session?.provider) {
        await saveSessionV2({
          email: updatedUser.email,
          provider: session.provider,
          providerId: session.providerId,
          credentials: {},
          meta: {
            ...updatedUser,
            providerId: session.providerId,
          },
        })
      }
    }

    return updatedUser
  } catch (err) {
    logger.error(`updateUserHandler error: ${(err as Error).toString()}`)
    throw err
  }
}
