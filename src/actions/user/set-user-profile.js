// "use server";

// import { prisma } from '@/io/prisma';
// import logger from "@/utils/logger";

// /** @type {import(".").setUserProfile} */
// export async function setUserProfile(userId, profile) {
//   try {
//     const user = await prisma.user.update({
//       where: { id: userId },
//       data: {
//         profile,
//       },
//     });

//     return user;
//   } catch (err) {
//     logger.error(`${err.toString()}`);
//     return null;
//   }
// }
