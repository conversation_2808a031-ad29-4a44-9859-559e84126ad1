'use server'

import { surql } from 'surrealdb'

import { jsonify, Surrealized } from '@/io/surreal'
import logger from '@/utils/logger'

import { type User } from '.'

export interface PaginatedUsersResponse {
  users: User[]
  total: number
}

export async function getNonAdminUsersPaginated(
  limit: number = 20,
  offset: number = 0,
  sortBy: string = 'created',
  sortOrder: 'asc' | 'desc' = 'desc',
): Promise<PaginatedUsersResponse> {
  const surreal = await Surrealized()

  try {
    logger.info(
      `getNonAdminUsersPaginated(limit:${limit}, offset:${offset}, sortBy:${sortBy}, sortOrder:${sortOrder})`,
    )

    // Map frontend column names to database field names
    const sortFieldMap: Record<string, string> = {
      id: 'id',
      hats: 'hats',
      handler: 'handler',
      email: 'email',
      status: 'relationStatus',
      created: 'created',
      name: 'profile.name',
    }

    const sortField = sortFieldMap[sortBy] || 'created'
    const orderDirection = sortOrder.toUpperCase()

    // Get total count
    const countResponse = await surreal.query<[{ count: number }[]]>(
      surql`
        SELECT count() as count
        FROM User
        WHERE 'admin' NOT IN hats.slug
          AND enabled = true
        GROUP ALL
      ;`,
    )

    // Build dynamic query string for sorting
    const queryString = `
      SELECT *
      FROM User
      WHERE 'admin' NOT IN hats.slug
        AND enabled = true
      ORDER BY ${sortField} ${orderDirection}
      LIMIT $limit
      START $offset
      FETCH
        hats,
        paymentMethod
    ;`

    // Get paginated results with sorting
    const response = await surreal.query<[User[]]>(queryString, {
      limit,
      offset,
    })

    const total = countResponse?.[0]?.[0]?.count || 0
    const values = response?.shift()
    const users = values ? jsonify<User[]>(values) : []

    return { users, total }
  } catch (err: unknown) {
    logger.error(
      `getNonAdminUsersPaginated error: ${(err as Error).toString()}`,
    )
    throw err
  }
}
