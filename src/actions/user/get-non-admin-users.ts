'use server'
import { surql } from 'surrealdb'

import { jsonify, Surrealized } from '@/io/surreal'
import logger from '@/utils/logger'

import { type User } from '.'

export async function getNonAdminUsers(): Promise<User[] | undefined> {
  try {
    const surreal = await Surrealized()

    const [users] = await surreal.query<[User[]]>(
      surql`
        SELECT *
          FROM User
          WHERE 'admin' NOT IN hats.slug
          FETCH
            hats,
            paymentMethod
      ;`,
    )

    const output = users ? jsonify<User[]>(users) : undefined
    return output
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    return undefined
  }
}
