'use server'
import set from 'lodash/set'

import { getSessionUserOrAdmin } from '@/actions/auth/getSessionUserOrAdmin'
import { User } from '@/actions/user'
import { Surrealized, jsonify, surql } from '@/io/surreal'
import { idToRecord } from '@/utils/idToRecord'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

export async function updateUserProfileData(
  userId: string,
  key: string,
  value: string,
) {
  const surreal = await Surrealized()

  try {
    const sessionUserOrAdmin = await getSessionUserOrAdmin(
      'updateUserProfileData',
    )

    logger.info(
      `updateUserProfileData(userId: ${maskId(userId)}, key: ${key}, value: ${value})`,
    )

    if (
      sessionUserOrAdmin !== null &&
      sessionUserOrAdmin.toString() !== userId
    ) {
      logger.error(
        `updateUserProfileData: sessionUserOrAdmin:${sessionUserOrAdmin.toString()} userId:${userId}`,
      )
      throw new Error('Unauthorized')
    }

    const profileResponse = await surreal.query<[[[User, User['profile']]]]>(
      surql`SELECT VALUE [id, profile] FROM type::thing($userId) WHERE enabled = true;`,
      { userId: idToRecord('User', userId) },
    )

    const profile = profileResponse?.shift()?.shift()?.pop() ?? null

    const nextProfile = profile || {}
    set(nextProfile, key, value)

    const response = await surreal.query<[[User['profile']]]>(
      surql`
        BEGIN TRANSACTION;
          UPDATE type::thing($userId) SET profile = $nextProfile
            WHERE (
              not(type::is::null($authUserId)) AND id = type::thing($authUserId)
              OR
              type::is::null($authUserId) AND id = type::thing($userId)
            )
          ;
          SELECT VALUE profile FROM type::thing($userId);
        COMMIT TRANSACTION;
        `,
      {
        userId: idToRecord('User', userId),
        authUserId: sessionUserOrAdmin,
        nextProfile,
      },
    )

    const userProfile = response?.shift()?.shift()
    return userProfile ? (jsonify(userProfile) as User['profile']) : null
  } catch (err) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
