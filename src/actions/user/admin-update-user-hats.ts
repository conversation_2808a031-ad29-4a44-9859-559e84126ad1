'use server'

import { getSessionAdmin } from '@/actions/auth/get-session-admin'
import { getSystemUserHats } from '@/actions/user/get-system-user-hats'
import { getUserById } from '@/actions/user/getUserById'
import { getAllUserHats } from '@/data/hats/get-all-user-hats'
import { updateHats } from '@/data/user/update-hats'
import { getIdFromRecord } from '@/utils'

export async function adminUpdateUserHats(userId: string, newHats: string[]) {
  try {
    const admin = await getSessionAdmin()
    if (!admin) {
      throw new Error('Not an admin')
    }

    const user = await getUserById(userId)
    if (!user) {
      throw new Error('User not found')
    }
    if (user.hats?.find(hat => hat.slug === 'admin')) {
      throw new Error('User is admin')
    }

    const systemHats = await getAllUserHats()

    const filteredHats =
      systemHats
        ?.filter(hat => hat.id && newHats.includes(hat.id.toString()))
        ?.map(hat => String(hat.id)) ?? []

    const updatedUser = await updateHats(getIdFromRecord(user.id), filteredHats)
    if (!updatedUser) {
      throw new Error('Error updating user hats')
    }

    return updatedUser
  } catch (error) {
    throw new Error('Error updating user hats')
  }
}
