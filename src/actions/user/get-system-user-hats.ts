'use server'

import { UserHat } from '@/actions/user'
import { getUserIfIsAdmin } from '@/actions/user/get-user-if-is-admin'
import { getAllUserHats } from '@/data/hats/get-all-user-hats'
import { logAndThrow } from '@/utils'
import logger from '@/utils/logger'

export async function getSystemUserHats(): Promise<UserHat[] | undefined> {
  try {
    const user = await getUserIfIsAdmin()
    if (!user) {
      throw new Error('User is not admin')
    }

    logger.info(`Getting system user hats`)
    const output = await getAllUserHats()

    return output
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
