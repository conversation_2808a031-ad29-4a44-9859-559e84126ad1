// "use server";

// import { prisma } from '@/io/prisma';
// import logger from "@/utils/logger";

// /** @type {import(".").updateUserData} */
// export async function updateUserData(userId, userData) {
//   try {
//     const {
//       credentials,
//       created,
//       updated,
//       enabled,
//       hats,
//       email,
//       onboardStatus,
//       ...cleanData
//     } = userData;

//     const user = await prisma.user.update({
//       where: { id: userId },
//       data: {
//         updated: new Date(),
//         ...cleanData,
//       },
//     });

//     return user;
//   } catch (err) {
//     logger.error(`${err.toString()}`);
//     return null;
//   }
// }
