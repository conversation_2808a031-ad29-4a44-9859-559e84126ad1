'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'
import logger from '@/utils/logger'
import maskEmail from '@/utils/mask-email'

import { type User } from '.'

interface UserData {
  email: string
  name?: string
  handler?: string
  avatar?: string
  profile?: {
    firstName?: string
    lastName?: string
    permissions?: {
      canAddContent?: boolean
    }
  }
  newUserToken?: string
}

export async function saveUser(userData: UserData): Promise<User | null> {
  const surreal = await Surrealized()

  try {
    logger.info(`saveUser(userData.email:${maskEmail(userData.email)})`)

    const existingUserResponse = await surreal.query<[[User]]>(
      surql`
        SELECT * FROM User WHERE email = $email FETCH hats, paymentMethod;
      `,
      { email: String(userData?.email) },
    )
    const existingUser = existingUserResponse?.shift()?.shift() ?? null

    let user: User | null = null

    if (existingUser) {
      const updateResponse = await surreal.query<[[User]]>(
        surql`
          UPDATE User SET
            name = $name,
            handler = $handler,
            avatar = $avatar,
            profile = $profile,
            updated = time::now(),
            newUserToken = $newUserToken
          WHERE email = $email;
          SELECT * FROM User WHERE email = $email FETCH hats, paymentMethod;
        `,
        {
          email: String(userData?.email),
          name: String(userData?.name),
          handler: String(userData?.handler),
          avatar: String(userData?.avatar),
          profile: userData?.profile ?? {},
          newUserToken: String(userData?.newUserToken),
        },
      )
      user = updateResponse?.pop()?.shift() ?? null
    } else {
      // Create default profile with permissions set to false
      const defaultProfile = {
        ...userData?.profile,
        permissions: {
          canAddContent: false,
          ...userData?.profile?.permissions,
        },
      }

      const insertResponse = await surreal.query<[[User]]>(
        surql`
          INSERT INTO User {
            email: $email,
            name: $name,
            handler: $handler,
            avatar: $avatar,
            enabled: true,
            paymentMethod: (SELECT VALUE id FROM PaymentMethod WHERE slug = "stripe")[0],
            profile: $profile,
            newUserToken: $newUserToken
          };
          SELECT * FROM User WHERE email = $email FETCH hats, paymentMethod;
        `,
        {
          email: String(userData?.email),
          name: String(userData?.name),
          handler: String(userData?.handler),
          avatar: String(userData?.avatar),
          profile: defaultProfile,
          newUserToken: String(userData?.newUserToken),
        },
      )
      user = insertResponse?.pop()?.shift() ?? null
    }
    return user ? jsonify(user) : null
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    return null
  }
}
