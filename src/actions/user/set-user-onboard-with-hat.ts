'use server'

import { getAllUserHats } from '@/data/hats/get-all-user-hats'
import { updateHats } from '@/data/user/update-hats'
import { User, UserOnboard } from '.'
import logger from '@/utils/logger'
import { setUserOnboardStatus } from './set-user-onboard-status'
import { getFullAuthSession } from '@/io/next-auth-config'
import { saveSession } from '../auth/save-session'

export async function setUserOnboardWithHat(
  userId: string,
  hatSlug: 'creator' | 'member',
): Promise<User | null> {
  try {
    // Get all system hats
    const allHats = await getAllUserHats()

    if (!allHats) {
      logger.error('Failed to get system hats')
      return null
    }

    const selectedHat = allHats.find(hat => hat.slug === hatSlug)

    if (!selectedHat) {
      logger.error(`Hat with slug ${hatSlug} not found`)
      return null
    }

    // Update user's hats
    const updatedUser = await updateHats(userId, [selectedHat.id])

    if (!updatedUser) {
      logger.error('Failed to update user hats')
      return null
    }

    // Update onboard status
    const user = await setUserOnboardStatus(userId, UserOnboard.ONBOARDED)

    if (!user) {
      logger.error('Failed to update user onboard status')
      return null
    }

    const session = await getFullAuthSession()

    if (session?.providerId && session?.provider) {
      await saveSession({
        ...session,
        meta: {
          ...user,
          providerId: session.providerId,
        },
      })
    }

    return user
  } catch (error) {
    logger.error(`Error in setUserOnboardWithHat: ${error}`)
    return null
  }
}
