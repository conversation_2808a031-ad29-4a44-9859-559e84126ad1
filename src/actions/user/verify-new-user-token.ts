'use server'

import { getUserByEmail } from './get-user-by-email'
import { User } from '.'
import { logger } from '@/utils'

export async function verifyUserToken({
  email,
  code,
}: {
  email: string
  code: string
}): Promise<boolean> {
  try {
    let user: User | null = null
    user = await getUserByEmail(email)

    if (!user) {
      return false
    }

    if (user.newUserToken?.toString() !== code) {
      return false
    }

    return true
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    return false
  }
}
