'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'
import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

import { type User } from '@/actions/user'
import { idToRecord } from '@/utils/idToRecord'

/**
 * @function getUserById
 * @description Get a user by ID
 * @param {string} userId - The ID of the user
 * @returns {Promise<User | undefined>} The user, or undefined if not found
 * @example
 * const user = await getUserById('some-id')
 * // user is { id: 'some-id', name: '<PERSON>', ... }
 */
export async function getUserById(userId: string): Promise<User | null> {
  const surreal = await Surrealized()

  try {
    // Log only in development or when necessary
    if (process.env.NODE_ENV === 'development') {
      logger.trace(`getUserById(userId: ${maskId(userId)})`)
    }

    const response = await surreal.query<[[User]]>(
      surql`
        SELECT
          id,
          email,
          name,
          handler,
          avatar,
          enabled,
          created,
          updated,
          onboardStatus,
          relationStatus,
          profile,
          hats,
          paymentMethod
        FROM type::thing($userId)
          WHERE enabled = true
          FETCH hats, paymentMethod
      ;`,
      { userId: idToRecord('User', userId) },
    )

    const user = response?.shift()?.shift()
    return user ? jsonify<User>(user) : null
  } catch (err: unknown) {
    logger.error(`getUserById error: ${(err as Error).toString()}`)
    throw err
  }
}
