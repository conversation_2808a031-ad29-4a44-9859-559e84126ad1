import { DefaultSession } from 'next-auth'
import { RecordId, StringRecordId } from 'surrealdb'

import { PaymentMethod } from '@/actions/payment'

export type UserProfile = Record<string, any> & {
  firstName?: string
  lastName?: string
  name?: string
  headline?: string
  bio?: string
  available?: {
    enabled: boolean
    workTypes: string[]
    hourlyRate: string
  }
  permissions?: {
    canAddContent?: boolean
  }
  social?: {
    website?: { value: string; active: boolean }
    twitter?: { value: string; active: boolean }
    linkedin?: { value: string; active: boolean }
    instagram?: { value: string; active: boolean }
    facebook?: { value: string; active: boolean }
    youtube?: { value: string; active: boolean }
    github?: { value: string; active: boolean }
    twitch?: { value: string; active: boolean }
    dribbble?: { value: string; active: boolean }
    behance?: { value: string; active: boolean }
  }
  accounts?: {
    webflow?: string
  }
}

export type UserHat = {
  id: RecordId | StringRecordId | string
  created?: Date | string
  enabled?: boolean
  name?: string
  slug?: string
}

export enum UserOnboard {
  NONE = 'UserOnboard/NONE',
  PENDING = 'UserOnboard/PENDING',
  ONBOARDED = 'UserOnboard/ONBOARDED',
}

export const enum UserRelation {
  FLAT = 'UserRelation/FLAT',
  ELITE_CREATOR = 'UserRelation/ELITE_CREATOR',
  TOP_CONTRIBUTOR = 'UserRelation/TOP_CONTRIBUTOR',
  RISING_STAR = 'UserRelation/RISING_STAR',
}

export const UserRelationString = {
  [UserRelation.FLAT]: 'Flat',
  [UserRelation.ELITE_CREATOR]: 'Elite Creator',
  [UserRelation.TOP_CONTRIBUTOR]: 'Top Contributor',
  [UserRelation.RISING_STAR]: 'Rising Star',
}

export type User = {
  avatar?: string
  created?: Date | string
  updated?: Date | string
  enabled?: boolean
  email: string
  name: string
  id: RecordId | StringRecordId | string
  handler: string
  hats?: UserHat[]
  newUserToken?: string
  onboardStatus?: UserOnboard.NONE | UserOnboard.PENDING | UserOnboard.ONBOARDED
  relationStatus?:
    | UserRelation.FLAT
    | UserRelation.ELITE_CREATOR
    | UserRelation.TOP_CONTRIBUTOR
    | UserRelation.RISING_STAR
  profile?: UserProfile
  paymentMethod: PaymentMethod
}

type UserAttriburesInSession =
  | 'id'
  | 'avatar'
  | 'email'
  | 'handler'
  | 'hats'
  | 'onboardStatus'
  | 'profile'

export type UserInSession = DefaultSession['user'] &
  Pick<User, UserAttriburesInSession>

export const OnboardStatus = {
  NONE: 'NONE',
  BASIC: 'BASIC',
  PROFILE: 'PROFILE',
  ONBOARDED: 'ONBOARDED',
}

export type TopCreator = User & {
  contributions?: number
}

export * from './getUserById'

export * from './set-user-onboard-status'
export * from './get-user-by-email'
export * from './get-user-by-handler'
export * from './updateUserProfileData'

export * from './set-user-onboard-status'
export * from './update-user-avatar'
export * from './delete-user-avatar'
export * from './updateUserHandler'
export * from './deleteUser'
