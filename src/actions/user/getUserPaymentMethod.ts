'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'

import logger from '@/utils/logger'
import maskId from '@/utils/mask-id'

import { PaymentMethod } from '@/actions/payment'
import maskData from '@/utils/mask-data'

export async function getUserPaymentMethod(userId: string) {
  const surreal = await Surrealized()

  try {
    logger.trace(`getUserPaymentMethod(userId:<${maskId(userId)}>)`)

    const response = await surreal.query<[[PaymentMethod]]>(
      surql`
        SELECT * FROM PaymentMethod WHERE id=(
          SELECT paymentMethod FROM User WHERE id=type::thing($userId)
        )[0].paymentMethod;
      `,
      { userId: String(`User:${userId?.toString()?.split(':')?.pop()}`) },
    )

    const paymentMethod = response?.shift()?.shift()
    logger.trace(`getUserPaymentMethod:returns: ${maskData(paymentMethod)})`)
    return jsonify(paymentMethod) as PaymentMethod
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
