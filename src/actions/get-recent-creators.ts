'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'

import { User } from '@/actions/user'
import logger from '@/utils/logger'

/**
 *
 */
export async function getRecentCreators(
  limit: number = 0,
): Promise<User[] | undefined> {
  if (limit <= 0) {
    return [] as User[]
  }

  const surreal = await Surrealized()

  try {
    const response = await surreal.query<[User[]]>(
      surql`
        RETURN array::map(
            (
                SELECT count() as total, owner FROM Product
                    WHERE
                        enabled = true AND
                        owner.enabled = true AND
                        owner.hats ANYINSIDE (
                            SELECT VALUE id FROM UserHat WHERE slug != 'admin'
                        )
                    GROUP BY owner
                    ORDER BY total DESC
                    LIMIT $limit
                    FETCH owner
            ),
            |$v| $v.owner
        );
      `,
      {
        limit,
      },
    )

    const totals = response?.pop() ?? null
    const output = totals ? jsonify<User[]>(totals) : undefined
    logger.trace(`getTopCreators::${output?.length ?? 0}`)
    return output
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
