'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'

import { Product } from '@/actions/product'
import logger from '@/utils/logger'
import { logAndThrow } from '@/utils'

/**
 *
 */
export async function getRecentProducts(
  limit: number = 0,
): Promise<Product[] | undefined> {
  if (limit <= 0) {
    return [] as Product[]
  }

  try {
    const surreal = await Surrealized()
    const response = await surreal.query<[Product[]]>(
      surql`
        SELECT * FROM Product
            WHERE featured = true
              AND status = 'ProductStatus/PUBLISHED'
            ORDER BY created DESC
            LIMIT $limit
            FETCH
              owner,
              price,
              fileSet,
              fileSet.files,
              platforms.bundle.assets,
              platforms.subscription.assets
        ;
      `,
      {
        limit,
      },
    )

    const totals = response?.pop() ?? null
    const output = totals ? jsonify<Product[]>(totals) : undefined
    return output
  } catch (err: unknown) {
    logAndThrow(err)
  }
}
