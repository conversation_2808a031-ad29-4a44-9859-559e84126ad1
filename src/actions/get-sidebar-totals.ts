'use server'

import { Surrealized, surql, jsonify } from '@/io/surreal'
import logger from '@/utils/logger'

import { type SidebarTotals } from '@/actions'

/**
 *
 */
export async function getSidebarTotals(): Promise<SidebarTotals | undefined> {
  const surreal = await Surrealized()

  try {
    const response = await surreal.query<[SidebarTotals]>(
      surql`
        LET $templates_framer = (
            SELECT VALUE count()
                FROM Product
                WHERE array::find(flair, 'framer')
                  AND array::find(flair, 'template')
                  AND status = 'ProductStatus/PUBLISHED'
                  AND enabled = true
                GROUP ALL
        )[0].count ?? 0;

        LET $templates_webflow = (
            SELECT VALUE count()
                FROM Product
                WHERE array::find(flair, 'webflow')
                  AND array::find(flair, 'template')
                  AND status = 'ProductStatus/PUBLISHED'
                  AND enabled = true
                GROUP ALL
        )[0].count ?? 0;

        LET $templates = $templates_framer + $templates_webflow;

        LET $components_framer = (
            SELECT VALUE count()
                FROM Product
                WHERE array::find(flair, 'framer')
                  AND array::find(flair, 'component')
                  AND status = 'ProductStatus/PUBLISHED'
                  AND enabled = true
                GROUP ALL
        )[0].count ?? 0;

        LET $components_webflow = (
            SELECT VALUE count()
                FROM Product
                WHERE array::find(flair, 'webflow')
                  AND array::find(flair, 'component')
                  AND status = 'ProductStatus/PUBLISHED'
                  AND enabled = true
                GROUP ALL
        )[0].count ?? 0;

        LET $components = $components_framer + $components_webflow;

        LET $webflow = (
            SELECT VALUE count()
                FROM Product
                WHERE array::find(flair, 'webflow')
                  AND status = 'ProductStatus/PUBLISHED'
                  AND enabled = true
                GROUP ALL
        )[0].count ?? 0;

        LET $framer = (
            SELECT VALUE count()
                FROM Product
                WHERE array::find(flair, 'framer')
                  AND status = 'ProductStatus/PUBLISHED'
                  AND enabled = true
                GROUP ALL
        )[0].count ?? 0;

        LET $bundle = (
            SELECT VALUE count()
                FROM Product
                WHERE array::find(flair, 'bundle')
                  AND status = 'ProductStatus/PUBLISHED'
                  AND enabled = true
                GROUP ALL
        )[0].count ?? 0;

        LET $subscription = (
            SELECT VALUE count()
                FROM Product
                WHERE array::find(flair, 'subscription')
                  AND status = 'ProductStatus/PUBLISHED'
                  AND enabled = true
                GROUP ALL
        )[0].count ?? 0;

        LET $featured = (SELECT VALUE count() FROM Product WHERE featured = true GROUP ALL)[0].count ?? 0;

        RETURN {
          template: $templates,
          component: $components,
          webflow: $webflow,
          framer: $framer,
          bundle: $bundle,
          app: 0,
          subscription: $subscription,
          featured: $featured,
        };
      `,
    )

    const totals = response?.pop()
    const output = totals ? jsonify<SidebarTotals>(totals) : undefined
    return output
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
