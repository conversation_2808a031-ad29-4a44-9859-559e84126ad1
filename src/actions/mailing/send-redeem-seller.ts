'use server'

import { HOST, RESEND_API_KEY } from '@/config'
import { getIdFromRecord } from '@/utils'
import { getPlatform } from '@/utils/get-platform'
import { format } from 'date-fns'
import { Resend } from 'resend'
import SaleNotification from '@/emails/sale-notification'

export default async function sendRedeemSeller(paymentLogData: any) {
  const platform =
    getPlatform(paymentLogData.product.platforms) ?? 'Unknown platform'

  const resend = new Resend(RESEND_API_KEY!)

  console.log('Sending redeem email to seller:', paymentLogData)

  try {
    const response = await resend.emails.send({
      from: 'Templace <<EMAIL>>',
      to: paymentLogData.buyer.email,
      subject: 'Your template was Redeemed',
      react: SaleNotification({
        userName: paymentLogData.seller.userName,
        product: {
          productSold: paymentLogData.product.name,
          total: 'Free',
          purchaseDate: format(paymentLogData.created, 'MMMM d, yyyy'),
          platform: platform || 'Unknown',
          user: `${paymentLogData.buyer.profile.firstName} ${paymentLogData.buyer.profile.lastName}(${paymentLogData.buyer.email})`,
        },
      }),
    })

    if (!response || !response.data) {
      return { success: false, error: 'Error sending email.' }
    }

    return true
  } catch (error) {
    return { success: false, error: String(error) }
  }
}
