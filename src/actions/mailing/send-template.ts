'use server'

import { MANDRIL_API_KEY } from '@/config'
import logger from '@/utils/logger'
import maskEmail from '@/utils/mask-email'
import mailchimp, { TemplateContent } from '@mailchimp/mailchimp_transactional'

interface Props {
  destEmail: string
  destName: string
  templateId: string
  subject: string
  vars: TemplateContent[]
}

export default async function sendTemplate({
  destEmail,
  destName,
  templateId,
  subject,
  vars,
}: Props) {
  const mandril = mailchimp(MANDRIL_API_KEY!)

  try {
    const respose = await mandril.messages.sendTemplate({
      template_name: templateId,
      template_content: vars,
      message: {
        from_email: '<EMAIL>',
        subject,
        to: [
          {
            email: destEmail,
            name: destName,
          },
        ],
        track_opens: true,
        track_clicks: true,
        signing_domain: 'tem.place',
        return_path_domain: 'tem.place',
        merge_vars: [
          {
            rcpt: destEmail,
            vars,
          },
        ],
      },
    })

    logger.info(
      `Mail sent to ${maskEmail(destEmail)} email, ${templateId} templateId`,
    )
    return respose
  } catch (error) {
    logger.error(`${(error as Error).toString()}`)
    throw error
  }
}
