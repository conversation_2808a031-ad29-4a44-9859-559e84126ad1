import { TransferSendStatus } from '@/actions/transfer'

type StatusMap = {
  [key in TransferSendStatus]: {
    subject: string
    templateId: string
  }
}

export default function getStatusForTransferSend(status: TransferSendStatus) {
  const statusMap: StatusMap = {
    [TransferSendStatus.INITIAL]: {
      subject: 'Transfer Initiated',
      templateId: 'transfer-initiated',
    },
    [TransferSendStatus.PROGRESS]: {
      subject: 'Transfer Status: "In Progress"',
      templateId: 'transfer-status-in-progress',
    },
    [TransferSendStatus.PENDING]: {
      subject: 'Transfer Status: "Pending"',
      templateId: 'transfer-status-pending',
    },
    [TransferSendStatus.COMPLETE]: {
      subject: 'Transfer Status: "Complete"',
      templateId: 'transfer-status-complete',
    },
    [TransferSendStatus.MULTIPLE]: {
      subject: 'Transfer Status: "Multiple"',
      templateId: 'transfer-status-multiple',
    },
    [TransferSendStatus.UNKNOWN]: {
      subject: 'Transfer Status: "Unknown"',
      templateId: 'transfer-status-unknown',
    },
    [TransferSendStatus.CANCELED]: {
      subject: 'Transfer Status: "Canceled"',
      templateId: 'transfer-status-canceled',
    },
  }

  return statusMap[status]
}
