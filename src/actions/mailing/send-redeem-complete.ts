'use server'

import { HOST, RESEND_API_KEY } from '@/config'
import { getIdFromRecord } from '@/utils'
import { getPlatform } from '@/utils/get-platform'
import { format } from 'date-fns'
import { Resend } from 'resend'
import ConfirmPurchase from '@/emails/confirm-purchase'

export default async function sendRedeemComplete(paymentLogData: any) {
  const platform =
    getPlatform(paymentLogData.product.platforms) ?? 'Unknown platform'

  const resend = new Resend(RESEND_API_KEY!)

  try {
    const response = await resend.emails.send({
      from: 'Templace <<EMAIL>>',
      to: paymentLogData.buyer.email,
      subject: 'Redeem Confirmation',
      react: ConfirmPurchase({
        transferUrl: `${HOST}/dashboard/purchases#product/${getIdFromRecord(paymentLogData.product.id)}`,
        confirmationCode: paymentLogData.hashId,
        email: paymentLogData.buyer.email,
        total: 'Free',
        date: format(paymentLogData.created, 'MMMM d, yyyy'),
        product: paymentLogData.product.name,
        platform: platform,
        createdBy: paymentLogData.seller.name,
      }),
    })

    if (!response || !response.data) {
      return { success: false, error: 'Error sending email.' }
    }

    return true
  } catch (error) {
    return { success: false, error: String(error) }
  }
}
