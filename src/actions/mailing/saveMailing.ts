'use server'

import { MAILCHIMP_API_KEY, MAILCHIMP_AUDIENCE_ID } from '@/config'
import logger from '@/utils/logger'
import maskEmail from '@/utils/mask-email'

export default async function saveMailing(name: string, email: string) {
  try {
    logger.info(`Saving mailing: <${maskEmail(email)}>, name, email`)

    const server = MAILCHIMP_API_KEY!.split('-')[1]

    const url = new URL(
      `https://${server}.api.mailchimp.com/3.0/lists/${MAILCHIMP_AUDIENCE_ID}/members`,
    )
    url.searchParams.set('skip_merge_validation', 'false')

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        Authorization: `apikey ${MAILCHIMP_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email_address: email,
        status: 'subscribed',
        tags: ['early-access'],
        merge_fields: {
          NAME: name,
          EMAIL: email,
        },
      }),
    })

    if (response.status < 400) {
      logger.info(
        `Mailchimp response, <${response.status}, ${response.statusText}>`,
      )
      return true
    } else {
      logger.error(
        `Mailchimp response, <${response.status}, ${response.statusText}>`,
      )
      return false
    }
  } catch (err: unknown) {
    logger.error(`${(err as Error).toString()}`)
    throw err
  }
}
