'use server'

import { sendTemplate } from '@/actions/mailing'
import { TransferSend } from '@/actions/transfer'
import { HOST } from '@/config'
import buildName from '@/utils/buildName'
import objectToTemplateContent from '@/utils/object-to-template-content'
import { format } from 'date-fns'

interface Intake {
  transfer: TransferSend
  subject: string
  templateId: string
}

export default async function sendTransferStatus({
  transfer,
  subject,
  templateId,
}: Intake) {
  if (!transfer.toUser?.email) {
    throw new Error('User has no email')
  }

  const name =
    buildName(
      transfer.toUser.profile?.firstName,
      transfer.toUser.profile?.lastName,
    ) || transfer.toUser.email

  const vars = objectToTemplateContent({
    SUBJECT: subject,
    UPDATED_DATE: format(transfer.updated, 'PPPPp'),
    EMAIL: transfer.toUser.email,
    NAME: name,
    PRODUCT_NAME: transfer?.paymentLog?.product?.name ?? '',
    BUTTON_LINK: `${HOST}/dashboard/purchases`,
  })

  await sendTemplate({
    destEmail: transfer.toUser.email,
    destName: name,
    subject,
    templateId,
    vars,
  })

  return true
}
