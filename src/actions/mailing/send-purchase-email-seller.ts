'use server'

import { RESEND_API_KEY } from '@/config'
import { getPlatform } from '@/utils/get-platform'
import { format } from 'date-fns'
import { Resend } from 'resend'
import SaleNotification from '@/emails/sale-notification'
import centsToDollars from '@/utils/cents-to-dollars'

interface SendPurchaseEmailSellerParams {
  seller: any
  buyer: any
  product: any
  created: string | Date
  hashId: string
}

export default async function sendPurchaseEmailToSeller({
  seller,
  buyer,
  product,
  created,
  hashId,
}: SendPurchaseEmailSellerParams) {
  const resend = new Resend(RESEND_API_KEY!)
  const platform = getPlatform(product.platforms)

  try {
    const response = await resend.emails.send({
      from: 'Templace <<EMAIL>>',
      to: seller.email,
      subject: 'You made a sale!',
      react: SaleNotification({
        userName: seller.userName,
        product: {
          productSold: product.name,
          purchaseDate: format(created, 'MMMM d, yyyy'),
          total: centsToDollars(product.price.amount),
          platform: platform || 'Unknown',
          user: `${buyer.profile.firstName} ${buyer.profile.lastName}(${buyer.email})`,
        },
      }),
    })
    if (!response || !response.data) {
      return { success: false, error: 'Error sending email.' }
    }
    return { success: true }
  } catch (error) {
    return { success: false, error: String(error) }
  }
}
