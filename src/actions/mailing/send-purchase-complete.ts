'use server'

import {
  sendPurchaseEmailT<PERSON><PERSON><PERSON>er,
  sendPurchaseEmailToSeller,
} from '@/actions/mailing'

export default async function sendPurchaseComplete(paymentLogData: any) {
  const buyer = paymentLogData?.buyer
  const seller = paymentLogData?.seller
  const product = paymentLogData?.product
  const created = paymentLogData?.created
  const hashId = paymentLogData?.hashId

  await sendPurchaseEmailToBuyer({
    buyer,
    product,
    created,
    hashId,
  })

  await sendPurchaseEmailToSeller({
    seller,
    buyer,
    product,
    created,
    hashId,
  })
  return true
}
