'use server'

import mailchimp, { TemplateContent } from '@mailchimp/mailchimp_transactional'
import { MANDRIL_API_KEY } from '@/config'
import logger from '@/utils/logger'
import maskEmail from '@/utils/mask-email'

export default async function sendTestEmail(
  dest: string,
  mailId: string,
  vars: TemplateContent[],
) {
  const mandril = mailchimp(MANDRIL_API_KEY!)

  try {
    const respose = await mandril.messages.sendTemplate({
      template_name: mailId,
      template_content: vars,
      message: {
        from_email: '<EMAIL>',
        subject: 'Assunto Principal do Email',
        to: [
          {
            email: dest,
            name: 'Nome do Usuário',
          },
        ],
        track_opens: true,
        track_clicks: true,
        signing_domain: 'tem.place',
        return_path_domain: 'tem.place',
        merge_vars: [
          {
            rcpt: dest,
            vars,
          },
        ],
      },
    })

    logger.info(`Mail sent to ${maskEmail(dest)} email, ${mailId} mailId`)
    return respose
  } catch (error) {
    logger.error(`${(error as Error).toString()}`)
    throw error
  }
}
