'use server'

import { RESEND_API_KEY } from '@/config'
import centsToDollars from '@/utils/cents-to-dollars'
import { getPlatform } from '@/utils/get-platform'
import { format } from 'date-fns'
import { Resend } from 'resend'
import ConfirmPurchase from '@/emails/confirm-purchase'

interface SendPurchaseEmailBuyerParams {
  buyer: any
  product: any
  created: string | Date
  hashId: string
}

export default async function sendPurchaseEmailToBuyer({
  buyer,
  product,
  created,
  hashId,
}: SendPurchaseEmailBuyerParams) {
  const resend = new Resend(RESEND_API_KEY!)
  const platform = getPlatform(product.platforms)

  try {
    const response = await resend.emails.send({
      from: 'Templace <<EMAIL>>',
      to: buyer.email,
      subject: 'Purchase Confirmation',
      react: ConfirmPurchase({
        transferUrl: `${process.env.HOST}/purchase/${hashId}`,
        confirmationCode: hashId,
        email: buyer.email,
        total: centsToDollars(product.price.amount),
        date: format(created, 'PPPPp'),
        product: product.name,
        platform: platform || 'Unknown',
        createdBy: product.createdBy.name,
      }),
    })
    if (!response || !response.data) {
      return { success: false, error: 'Error sending email.' }
    }
    return { success: true }
  } catch (error) {
    return { success: false, error: String(error) }
  }
}
