import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'
import type { JWT } from 'next-auth/jwt'

import { HEADER_PATHNAME } from '@/config'
import { UserHat } from '@/actions/user'

interface TokenUser {
  id: string
  email: string
  name: string | null
  avatar?: string | null
  handler?: string
  hats?: UserHat[]
  [key: string]: any
}

interface CustomJWT extends JWT {
  user?: TokenUser
}

const protectedRoutes = [
  '/onboarding',
  '/payment',
  '/profile',
  '/user/settings',
]
const authRoutes = ['/login']
const adminRoutes = ['/backoffice']

async function checkToken(req: NextRequest) {
  const token = (await getToken({ req })) as CustomJWT | null
  if (!token) {
    const loginUrl = new URL('/login', req.url)
    loginUrl.searchParams.set('redirect', req.url)
    return NextResponse.redirect(loginUrl)
  }

  return NextResponse.next({
    headers: {
      [HEADER_PATHNAME]: req.nextUrl.pathname,
    },
  })
}

async function checkAdminToken(req: NextRequest) {
  const token = (await getToken({ req })) as CustomJWT | null
  if (!token) {
    const loginUrl = new URL('/login', req.url)
    loginUrl.searchParams.set('redirect', req.url)
    return NextResponse.redirect(loginUrl)
  }

  try {
    const user = token.user

    if (!user) {
      return NextResponse.redirect(new URL('/404', req.url))
    }

    const isAdmin = !!user.hats?.find((hat: UserHat) => hat.slug === 'admin')

    if (!isAdmin) {
      return NextResponse.redirect(new URL('/404', req.url))
    }
  } catch (error) {
    return NextResponse.redirect(new URL('/404', req.url))
  }

  return NextResponse.next({
    headers: {
      [HEADER_PATHNAME]: req.nextUrl.pathname,
    },
  })
}

async function checkAuthRoute(req: NextRequest) {
  const token = (await getToken({ req })) as CustomJWT | null
  if (token) {
    // User is authenticated, redirect to home
    return NextResponse.redirect(new URL('/', req.url))
  }

  return NextResponse.next({
    headers: {
      [HEADER_PATHNAME]: req.nextUrl.pathname,
    },
  })
}

async function replaceUserHandler(req: NextRequest) {
  const { pathname } = req.nextUrl
  const newPathname = pathname.replace('/@', '/').slice(1)
  const url = req.nextUrl.clone()
  url.pathname = `/user/${newPathname}`
  return NextResponse.rewrite(url)
}

export async function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl

  if (pathname.startsWith('/@')) {
    return replaceUserHandler(req)
  }

  if (authRoutes.some(path => pathname.startsWith(path))) {
    return checkAuthRoute(req)
  }

  if (adminRoutes.some(path => pathname.startsWith(path))) {
    return checkAdminToken(req)
  }

  if (protectedRoutes.some(path => pathname.startsWith(path))) {
    return checkToken(req)
  }

  return NextResponse.next({
    headers: {
      [HEADER_PATHNAME]: req.nextUrl.pathname,
    },
  })
}
