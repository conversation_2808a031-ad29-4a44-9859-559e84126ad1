import { z } from 'zod'

import { PriceSchema } from '@/actions/price'
import { PlatformsEnum, PlatformsSchema } from '@/actions/product/enum'

export const FormSaveProductBaseSchema = z.object({
  name: z
    .string()
    .min(1, 'Name is required')
    .max(32, 'Name must be at most 32 characters'),
  slug: z
    .string()
    .min(1, 'Slug is required')
    .max(32, 'Slug must be at most 32 characters'),
  description: z
    .string()
    .min(1, 'Byline is required')
    .max(380, 'Byline must be at most 380 characters'),
  platform: z.enum([
    PlatformsEnum.WEBFLOW,
    PlatformsEnum.FRAMER,
    PlatformsEnum.FIGMA,
    PlatformsEnum.VZERO,
    // PlatformsEnum.LOVEABLE,
    // PlatformsEnum.BOLT,
    // PlatformsEnum.SHOPIFY,
    PlatformsEnum.SUBSCRIPTION,
    PlatformsEnum.BUNDLE,
    PlatformsEnum.SPLINE,
  ]),
  platforms: PlatformsSchema,
  price: PriceSchema,
  isFree: z.boolean().optional(),
  flair: z.array(z.string()).optional(),
  includes: z
    .array(z.string())
    .min(1, 'Select at least one category')
    .max(3, 'Select at most 3 categories'),
  features: z
    .array(z.string())
    .min(1, 'Select at least one feature')
    .max(8, 'Select at most 8 features'),
  imagesAndVideos: z
    .array(z.any())
    .min(1, 'Upload at least one image or video')
    .max(12, 'You reach the upload limit'),
  status: z.string().optional(),
  liveUrl: z.string().url('Please enter a valid URL').optional(),
})

export const FormSaveProductSchema = FormSaveProductBaseSchema.superRefine(
  (data, ctx) => {},
)

export type FormSaveProductData = z.infer<typeof FormSaveProductSchema>
