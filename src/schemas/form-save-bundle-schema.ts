import { PlatformsEnum } from '@/actions/product'
import { FormSaveProductBaseSchema } from '@/schemas/form-save-product-schema'
import { z } from 'zod'

const FormSaveBundleBaseSchema = FormSaveProductBaseSchema.extend({
  // bundle only schema
})

export const FormSaveBundleSchema = FormSaveBundleBaseSchema.superRefine(
  (data, ctx) => {
    if (
      data.platform === PlatformsEnum.BUNDLE &&
      data.platforms.bundle?.assets.length === 0
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['platforms', 'bundle', 'assets'],
        message: 'You need to add at least one asset',
      })
    }
  },
)

export type FormSaveBundleData = z.infer<typeof FormSaveBundleSchema>
