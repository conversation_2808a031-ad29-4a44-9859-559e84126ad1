import { z } from 'zod'

export const DatabaseSavePlatformsSchema = z
  .object({
    webflow: z
      .object({
        kind: z.string().optional(),
        auto: z.boolean().optional(),
      })
      .optional(),

    framer: z
      .object({
        kind: z.string().optional(),
        remixUrl: z.string().url().optional(),
        embedPreview: z.boolean().optional(),
      })
      .optional(),

    figma: z
      .object({
        downloadLink: z.string().url().optional(),
        file: z.any(),
      })
      .optional(),

    vzero: z
      .object({
        downloadLink: z.string().url().optional(),
        github: z.string().url().optional(),
        file: z.any(),
        embedPreview: z.boolean().optional(),
      })
      .optional(),

    loveable: z
      .object({
        downloadLink: z.string().url().optional(),
        repo: z.string().url().optional(),
        github: z.string().url().optional(),
        file: z.any(),
        embedPreview: z.boolean().optional(),
      })
      .optional(),

    bolt: z
      .object({
        downloadLink: z.string().url().optional(),
        repo: z.string().url().optional(),
        github: z.string().url().optional(),
        file: z.any(),
        embedPreview: z.boolean().optional(),
      })
      .optional(),

    spline: z
      .object({
        // downloadLink: z.string().url().optional(),
        file: z.any(),
      })
      .optional(),

    shopify: z
      .object({
        downloadLink: z.string().url().optional(),
        repo: z.string().url().optional(),
        github: z.string().url().optional(),
        file: z.any(),
      })
      .optional(),

    subscription: z
      .object({
        assets: z.array(z.any()),
        startDate: z.string().datetime(),
        endDate: z.string().datetime(),
      })
      .optional(),

    bundle: z
      .object({
        assets: z.array(z.any()),
      })
      .optional(),
  })
  .strip()
