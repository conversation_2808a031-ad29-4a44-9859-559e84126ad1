import { z } from 'zod'

import { FileSchema } from '@/actions/file'
import { PlatformsEnum } from '@/actions/product'
import { TransferReceiveStatus } from '@/actions/transfer'
import { FormSaveProductBaseSchema } from '@/schemas/form-save-product-schema'

const FormSaveTemplateBaseSchema = FormSaveProductBaseSchema.extend({
  includeFigmaFile: z.boolean().optional(),
  figmaFile: FileSchema.optional(),
})

export const FormSaveTemplateSchema = FormSaveTemplateBaseSchema.superRefine(
  (data, ctx) => {
    if (
      data.platform === PlatformsEnum.FRAMER &&
      !data.platforms.framer?.remixUrl
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['platforms', 'framer', 'remixUrl'],
        message: 'Remix URL is required for Framer platform',
      })
    }

    if (
      data.platform === PlatformsEnum.VZERO &&
      data.platforms.vzero?.file === undefined
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['platforms', 'vzero', 'file'],
        message: 'Zip file is required for v0 platform',
      })
    }

    if (
      data.platform === PlatformsEnum.WEBFLOW &&
      data.platforms.webflow?.auto === undefined
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['platforms', 'webflow', 'auto'],
        message:
          'Webflow template confirmation is required when Webflow is selected',
      })

      if (
        data.platforms.webflow?.auto === true &&
        data.platforms.webflow?.distributionId === undefined
      ) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['platforms', 'webflow', 'distributionId'],
          message: 'Distribution ID is required when auto distribution is true',
        })
      }

      if (
        data.platforms.webflow?.auto === true &&
        data.platforms.webflow?.distributionId !== undefined &&
        data.platforms.webflow?.distributionStatus !==
          TransferReceiveStatus.COMPLETE
      ) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['platforms', 'webflow', 'distributionStatus'],
          message: 'Distribution is not complete yet.',
        })
      }
    }

    /** */

    if (data.includeFigmaFile && !data.figmaFile) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['figmaFile'],
        message: 'Figma file is required when you opt to add one',
      })
    }
  },
)

export type FormSaveTemplateData = z.infer<typeof FormSaveTemplateSchema>
