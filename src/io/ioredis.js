import Redis from 'ioredis'

import { REDIS_URL } from '@/config'
import logger from '@/utils/logger'

/**
 *
 */
const redisClientSingleton = () => {
  const singleRedis = new Redis(REDIS_URL, {
    // tls: !REDIS_URL.startsWith("redis://localhost"),
    // tls: {
    //   rejectUnauthorized: ,
    // },
    enableReadyCheck: true,
  })

  singleRedis.on('error', err =>
    logger.error(`Redis Client Error ${err.message}`),
  )

  // singleRedis.on("connect", () => logger.info("Redis Client Connected"));

  return singleRedis
}

// declare const globalThis: {
//   redisGlobal: ReturnType<typeof redisClientSingleton>;
// } & typeof global;

// eslint-disable-next-line no-undef
const redis = globalThis.redisGlobal ?? redisClientSingleton()

export default redis

// eslint-disable-next-line no-undef
if (process.env.NODE_ENV !== 'production') globalThis.redisGlobal = redis
