import { QueryClient } from '@tanstack/react-query'

export const QUERY_LOADING = ['loading']
export const QUERY_DIALOG = ['dialog']
export const QUERY_NAV_MENU = ['nav-menu']
export const QUERY_TRANSFER_RECEIVE = ['transfer', 'receive']
export const INFINITE_PRODUCTS = ['infinite', 'products']
export const INFINITE_PURCHASES = ['infinite', 'purchases']

export const QUERY_PRODUCT = ['product']
export const QUERY_PLATFORM = ['platform']

export const QUERY_SUBSCRIPTION = ['subscription']
export const QUERY_PAYMENT_MENTHOD = ['paymentMethod']
export const QUERY_PAYMENT_PRICE = ['paymentPrice']
export const QUERY_PAYMENT_LOG = ['paymentLog']
export const QUERY_ASSET = ['asset']
export const QUERY_ASSET_TYPE = ['childrenType']

export const QUERY_ASSET_BUNDLE = ['assetBundle']
export const QUERY_ASSET_SALE = [...QUERY_ASSET, 'sale']
export const QUERY_USER = ['user']
export const QUERY_USER_COLLECTION = [...QUERY_USER, 'collection']
export const QUERY_MEMBER = ['member']

export const QUERY_TRANSFER = ['transfer']
export const QUERY_SKILLS = ['skills']
export const QUERY_USER_SKILLS = [...QUERY_USER, 'skills']

const queryClient = new QueryClient()

export async function getQueryClientAsync() {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: false,
        retry: false,
      },
    },
  })
  return queryClient
}

export default queryClient
