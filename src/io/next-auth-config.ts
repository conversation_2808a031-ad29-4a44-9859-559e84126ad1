import { getServerSession } from 'next-auth'
import GoogleProvider from 'next-auth/providers/google'
import CredentialsProvider from 'next-auth/providers/credentials'
// import AppleProvider from "next-auth/providers/apple";

import { signIn, session, redirect, jwt } from '@/actions/auth'
import { GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, GOOGLE_SCOPE } from '@/config'

const nextAuthConfig = {
  secret: process.env.NEXTAUTH_SECRET,
  session: {
    strategy: 'jwt' as const, // necessary for credentials provider
    maxAge: 180 * 24 * 60 * 60, // 180 days
    updateAge: 24 * 60 * 60, // 24 hours
  },
  callbacks: {
    signIn,
    session,
    redirect,
    jwt,
  },
  providers: [
    GoogleProvider({
      clientId: GOOGLE_CLIENT_ID,
      clientSecret: GOOGLE_CLIENT_SECRET,
      authorization: {
        params: {
          scope: GOOGLE_SCOPE,
          prompt: 'consent',
          access_type: 'offline',
          response_type: 'code',
        },
      },
    }),
    CredentialsProvider({
      id: 'credentials',
      name: '<PERSON><PERSON>',
      credentials: {
        email: {},
      },
      async authorize(credentials) {
        if (!credentials?.email) {
          return null
        }

        try {
          return {
            id: credentials.email,
            email: credentials.email,
          }
        } catch (error) {
          console.error('Authorization error:', error)
          return null
        }
      },
    }),
  ],
  pages: {
    signIn: '/login',
    signOut: '/logout',
    error: '/login/error',
  },
}

export async function getFullAuthSession() {
  const session = await getServerSession(nextAuthConfig)
  return session
}

export default nextAuthConfig
