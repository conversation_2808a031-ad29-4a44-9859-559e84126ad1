import { Surreal, jsonify as surrealJsonify } from 'surrealdb'
import jwt, { type JwtPayload } from 'jsonwebtoken'

import { surql as utilsSurql } from '@/utils/surql'
import logger from '@/utils/logger'

import { SURREAL_PASSWORD, SURREAL_URL, SURREAL_USER } from '@/config'

interface SurrealAuth {
  database: string
  namespace: string
  username: string
  password: string
}

interface ConnectionStatus {
  isExecutingQuery: boolean
  token?: JwtPayload
}

class SurrealizedSingleton extends Surreal {
  private static readonly CONFIG = {
    TIMEOUT: 20 * 60 * 1000,
    DEFAULT_NAMESPACE: 'dsm',
    DEFAULT_DATABASE: 'dsm',
  } as const

  private static shelf = new Map<string, SurrealizedSingleton>()

  private connectionStatus: ConnectionStatus = {
    isExecutingQuery: false,
    token: undefined,
  }

  private timer?: NodeJS.Timeout

  constructor(...params: any[]) {
    super(...params)
  }

  override async query<T extends unknown[]>(
    ...args: Parameters<Surreal['query']>
  ): Promise<{ [K in keyof T]: T[K] }> {
    this.connectionStatus.isExecutingQuery = true
    try {
      const result = await super.query<T>(...args)
      return result
    } finally {
      this.connectionStatus.isExecutingQuery = false
    }
  }

  private calculateTimeout(): number {
    return (
      (this.connectionStatus.token?.exp || 0) * 1000 -
      SurrealizedSingleton.CONFIG.TIMEOUT -
      Date.now()
    )
  }

  private async handleConnectionClose(): Promise<void> {
    const token = this.connection?.connection.token

    await this.close()

    if (this.timer) {
      clearInterval(this.timer)
      this.timer = undefined
    }

    if (token) {
      SurrealizedSingleton.shelf.delete(token)
    }

    logger.trace(`Surreal connection closed`)
  }

  private setTimer() {
    clearInterval(this.timer)
    this.timer = undefined

    const timeout = this.calculateTimeout()

    this.timer = setInterval(() => {
      (async () => {
        if (
          this?.status === 'connected' &&
          !this.connectionStatus.isExecutingQuery
        ) {
          await this.handleConnectionClose()
        }
      })()
    }, timeout)
  }

  private async handleConnection(authData?: SurrealAuth): Promise<void> {
    await this.connect(SURREAL_URL!, {
      namespace: SurrealizedSingleton.CONFIG.DEFAULT_NAMESPACE,
      database: SurrealizedSingleton.CONFIG.DEFAULT_DATABASE,
    })

    await this.signin(
      !authData
        ? {
            username: SURREAL_USER,
            password: SURREAL_PASSWORD,
          }
        : authData,
    )

    await this.allReady()
  }

  private async allReady(): Promise<void> {
    await this.ready
    await this.connection?.ready
  }

  private decodeToken(): void {
    this.connectionStatus.token = this.connection?.connection.token
      ? (jwt.decode(this.connection.connection.token) as JwtPayload)
      : undefined
  }

  async init(authData?: SurrealAuth): Promise<void> {
    await this.handleConnection(authData)
    this.decodeToken()
    this.setTimer()
    logger.trace(
      `Surreal connection: ${this.status}, token: ${this.connection?.connection.token}`,
    )
  }

  public static getFirstAvailable(): SurrealizedSingleton | null {
    if (SurrealizedSingleton.shelf.size === 0) return null

    const list = Array.from(SurrealizedSingleton.shelf.keys())
    const found = list.reduce<SurrealizedSingleton | null>((acc, token) => {
      if (acc) return acc

      const instance = SurrealizedSingleton.shelf.get(token)
      if (instance?.status === 'connected') {
        return instance
      }

      return null
    }, null)

    return found || null
  }

  public static async getInstance(): Promise<SurrealizedSingleton> {
    let instance = SurrealizedSingleton.getFirstAvailable()
    if (instance) {
      return instance
    }

    instance = new SurrealizedSingleton()
    await instance.init()
    const token = instance.connection?.connection.token
    if (token) {
      SurrealizedSingleton.shelf.set(token, instance)
    }

    return instance
  }
}

export const Surrealized = async () => await SurrealizedSingleton.getInstance()

export const surql = utilsSurql
export const jsonify = surrealJsonify
