'use client'

import { Product } from '@/actions/product'
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group'
import { cn } from '@/lib/utils'
import {
  Laptop,
  Phone,
  Tablet,
  Expand,
  Smartphone,
  Tablets,
  TabletIcon,
  Shrink,
} from 'lucide-react'
import { useRef, useState } from 'react'
import { Separator } from '../ui/separator'
import { Button } from '../ui/button'
import TemplaceLogo from '../common/TemplaceLogo'

type Props = {
  product: Product
}

type Breakpoints = 'mobile' | 'tablet' | 'desktop' | 'full'

type Breakpoint = {
  width: string | number
  height: string | number
}

const breakpoints: Record<Breakpoints, Breakpoint> = {
  mobile: {
    width: 375,
    height: 667,
  },
  tablet: {
    width: 768,
    height: 1024,
  },
  desktop: {
    width: '100%',
    height: '100%',
  },
  full: {
    width: '100%',
    height: '100%',
  },
}

export default function ProductPreview({ product }: Props) {
  const container = useRef<HTMLDivElement>(null)
  const [size, setSize] = useState<Breakpoint>(breakpoints.full)
  const [isFullScreen, setIsFullScreen] = useState(false)

  const onToggleFullScreen = () => {
    setIsFullScreen(!isFullScreen)
  }

  const onValueChange = (value: Breakpoints) => {
    if (value && value !== 'full') {
      setSize(breakpoints[value as keyof typeof breakpoints])
    }
  }

  const wrapperClassName = cn(
    'w-full h-[calc(100vh-113px)] mb-4 bg-muted flex flex-col items-center justify-start',
    isFullScreen && 'fixed top-0 left-0 w-full h-full z-50',
  )

  const iframeClassName = cn('border', isFullScreen && 'border-0')

  return (
    <div ref={container} className={wrapperClassName}>
      <div
        className={cn(
          'p-2 w-full flex items-center justify-center',
          isFullScreen &&
            'justify-between border-b shadow-sm z-50 bg-background',
        )}
      >
        {isFullScreen && <TemplaceLogo className="w-8 h-8" full={false} />}
        <ToggleGroup
          type="single"
          defaultValue="full"
          onValueChange={onValueChange}
        >
          <ToggleGroupItem value="mobile">
            <Smartphone className="w-3 h-3" />
          </ToggleGroupItem>

          <ToggleGroupItem value="tablet">
            <Tablet className="w-5 h-5" />
          </ToggleGroupItem>

          <ToggleGroupItem value="desktop">
            <Laptop />
          </ToggleGroupItem>

          <Separator orientation="vertical" />

          <Button variant="ghost" onClick={onToggleFullScreen}>
            {isFullScreen ? <Shrink /> : <Expand />}
          </Button>
        </ToggleGroup>
        {isFullScreen && (
          <span className="text-sm text-muted-foreground">{product.name}</span>
        )}
      </div>

      <div className={iframeClassName} style={{ ...size }}>
        <iframe
          scrolling={isFullScreen ? 'yes' : 'no'}
          className="w-full h-full overflow-hidden"
          src={product.liveUrl}
          title={product.name}
        />
      </div>
    </div>
  )
}
