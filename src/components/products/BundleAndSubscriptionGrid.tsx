'use client'
import React from 'react'
import { useInView } from 'react-intersection-observer'

import {
  Platforms,
  PlatformsEnum,
  Product,
  ProductPlatformsKeys,
} from '@/actions/product'
import ProductBox from '@/components/common/ProductBox'
import ProductBoxMini from '@/components/common/ProductBoxMini'
import BundleInfo from '@/components/products/BundleInfo'
import { getPlatform } from '@/utils/get-platform'

type Props = React.PropsWithChildren<{
  bundleOrSubscription: Product
  useMini?: boolean
}>

/**
 * BundleAndSubscriptionGrid
 */
export default function BundleAndSubscriptionGrid({
  useMini = false,
  bundleOrSubscription,
}: Props) {
  const platform = getPlatform(bundleOrSubscription.platforms)

  const assets =
    bundleOrSubscription?.platforms?.bundle?.assets ??
    bundleOrSubscription?.platforms?.subscription?.assets ??
    []

  const templates = assets.length

  const total =
    assets.reduce((acc, item) => {
      return acc + (item.price?.amount || 0)
    }, 0) || 0

  return (
    <>
      {platform === PlatformsEnum.BUNDLE && (
        <BundleInfo
          templates={templates}
          total={total}
          value={bundleOrSubscription.price.amount}
        />
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {assets?.map((product: Product) => {
          const link = `/product/${product.slug}`
          return useMini ? (
            <ProductBoxMini key={product.id} product={product} link={link} />
          ) : (
            <ProductBox key={product.id} product={product} link={link} />
          )
        })}
      </div>
    </>
  )
}
