import Link from 'next/link'
import { headers } from 'next/headers'
import { X } from 'lucide-react'

import { HEADER_PATHNAME, HEADER_REFERER } from '@/config'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { User } from '@/actions/user'
import { getInitials } from '@/utils/get-initials'

type Props = React.PropsWithChildren<{
  user: User
}>

export default async function ProductPublicHeader({ user }: Props) {
  const pageHeaders = await headers()
  const referer = pageHeaders.get(HEADER_REFERER)
  const pathname = pageHeaders.get(HEADER_PATHNAME)
  const isSamePage = referer?.includes(pathname ?? '')

  const name = `${user?.profile?.firstName} ${user?.profile?.lastName}`
  const initials = getInitials(
    user?.profile?.firstName,
    user?.profile?.lastName,
  )

  return (
    <nav className="p-4 border-b bg-background/80 backdrop-blur-md sticky top-0 z-50 w-full">
      <div className="w-full flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Avatar asChild>
            <Link href={`/user/${user?.handler}`}>
              <AvatarImage src={user?.avatar} />
              <AvatarFallback className="font-bold">{initials}</AvatarFallback>
            </Link>
          </Avatar>

          <Link href={`/user/${user?.handler}`} className="flex flex-col">
            <h3 className="text-sm font-medium">{name}</h3>
            <div className="text-xs text-gray-500">@{user?.handler}</div>
          </Link>
        </div>

        {!isSamePage && referer ? (
          <Button variant="ghost" size="icon" className="h-8 w-8" asChild>
            <Link href={referer}>
              <X className="h-4 w-4" />
            </Link>
          </Button>
        ) : null}
      </div>
    </nav>
  )
}
