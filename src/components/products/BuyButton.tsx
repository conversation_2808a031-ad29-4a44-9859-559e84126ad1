import { Product } from '@/actions/product'
import { Button } from '@/components/ui/button'
import centsToDollars from '@/utils/cents-to-dollars'

export default function BuyButton({
  isFree,
  redeemLink,
  buyLink,
  product,
}: {
  isFree: boolean
  redeemLink: string
  buyLink: string
  product: Product
}) {
  return (
    <Button className="flex-1" asChild>
      <a href={isFree ? redeemLink : buyLink}>
        {isFree
          ? 'Redeem this product now'
          : `Buy Now - ${centsToDollars(product.price.amount)}`}
      </a>
    </Button>
  )
}
