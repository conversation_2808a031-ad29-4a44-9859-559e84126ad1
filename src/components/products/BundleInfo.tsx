import centsToDollars from '@/utils/cents-to-dollars'
import React, { useMemo } from 'react'

type Props = {
  templates?: number | string
  value?: number | string
  total?: number | string
}

const BundleInfo = ({ templates, value, total }: Props) => {
  const formattedValue = useMemo(() => {
    if (typeof value === 'number') {
      return centsToDollars(value)
    }
    return value
  }, [value])

  const formattedTotal = useMemo(() => {
    if (typeof total === 'number') {
      return centsToDollars(total)
    }
    return total
  }, [total])

  const savingsPercentage = useMemo(() => {
    if (typeof value === 'number' && typeof total === 'number') {
      return `~${centsToDollars(total)} (${Math.round((total / value) * 100)}%)`
    }
    return formattedTotal
  }, [value, total, formattedTotal])

  return (
    <div className="mb-6 p-5 bg-gray-50 rounded-lg border border-gray-200">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="flex flex-col">
          <span className="text-sm text-gray-500">Assets</span>
          <span className="text-lg font-medium">{templates}</span>
        </div>
        <div className="flex flex-col">
          <span className="text-sm text-gray-500">Total Value</span>
          <span className="text-lg font-medium">{formattedValue}</span>
        </div>
        <div className="flex flex-col">
          <span className="text-sm text-gray-500">You Save</span>
          <span className="text-lg font-medium text-green-600">
            {savingsPercentage}
          </span>
        </div>
      </div>
    </div>
  )
}

export default BundleInfo
