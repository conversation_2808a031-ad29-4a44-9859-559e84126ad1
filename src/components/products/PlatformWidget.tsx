import { <PERSON><PERSON><PERSON><PERSON>, CircleHelp, Group } from 'lucide-react'
import { FaWebflow } from 'react-icons/fa6'
import { SiVercel } from 'react-icons/si'

import { PlatformsEnum } from '@/actions/product'
import { cn } from '@/lib/utils'
import { CSSProperties } from 'react'
import { <PERSON><PERSON><PERSON><PERSON> } from 'react-icons/si'

type Props = {
  platform?: string
  className?: string
  justIcon?: boolean
  size?: string | number
}

const platformNameMap = {
  [PlatformsEnum.VZERO]: 'v0',
}

export default function PlatformWidget({
  className,
  platform,
  justIcon = false,
  size = '1',
}: Props) {
  const iconSizeClassName = `${parseFloat(size.toString()) * 1}rem`
  const textSizeClassName = `${parseFloat(size.toString()) * 0.75}rem`

  return (
    <div
      style={
        {
          ['--widget-text-size']: textSizeClassName,
          ['--widget-icon-size']: iconSizeClassName,
        } as CSSProperties
      }
      className={cn('flex flex-row items-center gap-1', className)}
    >
      {platform === PlatformsEnum.WEBFLOW && (
        <div
          className={cn(
            'flex place-items-center justify-center rounded-full bg-[#146EF5]',
            'w-[--widget-icon-size] h-[--widget-icon-size]',
          )}
        >
          <FaWebflow className="w-1/2 h-1/2 text-white" />
        </div>
      )}
      {platform === PlatformsEnum.FRAMER && (
        <div
          className={cn(
            'flex place-items-center justify-center rounded-full bg-[#000]',
            'w-[--widget-icon-size] h-[--widget-icon-size]',
          )}
        >
          <SiFramer className="w-1/2 h-1/2 text-white" />
        </div>
      )}

      {platform === PlatformsEnum.VZERO && (
        <div
          className={cn(
            'flex place-items-center justify-center rounded-full bg-[#000]',
            'w-[--widget-icon-size] h-[--widget-icon-size]',
          )}
        >
          <SiVercel className="w-1/2 h-1/2 text-white" />
        </div>
      )}

      {platform === PlatformsEnum.BUNDLE && (
        <div
          className={cn(
            'flex place-items-center justify-center rounded-full bg-background',
            'w-[--widget-icon-size] h-[--widget-icon-size]',
          )}
        >
          <Group className="w-full text-foreground/45 transform scale-90" />
        </div>
      )}

      {platform === PlatformsEnum.SUBSCRIPTION && (
        <div
          className={cn(
            'flex place-items-center justify-center rounded-full bg-background',
            'w-[--widget-icon-size] h-[--widget-icon-size]',
          )}
        >
          <CalendarCheck className="w-full text-foreground/45 transform scale-90" />
        </div>
      )}

      {platform === undefined && (
        <div
          className={cn(
            'flex place-items-center justify-center rounded-full bg-background',
            'w-[--widget-icon-size] h-[--widget-icon-size]',
          )}
        >
          <CircleHelp
            strokeWidth={1}
            className="w-full text-foreground/45 transform scale-90"
          />
        </div>
      )}

      {Boolean(!justIcon && platform) && (
        <span
          className={cn(
            'text-foreground font-semibold text-[length:var(--widget-text-size)]',
            !platformNameMap?.[platform as keyof typeof platformNameMap]
              ? 'capitalize'
              : '',
          )}
        >
          {platformNameMap?.[platform as keyof typeof platformNameMap] ??
            platform}
        </span>
      )}
    </div>
  )
}
