'use client'

import { format } from 'date-fns'
import { Plus, AlertTriangle, X } from 'lucide-react'
import Link from 'next/link'
import React, { useState } from 'react'
import { useSearchParams } from 'next/navigation'

import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs'

import { FileSet } from '@/actions/file'
import { ImageSet } from '@/actions/image'
import { PaymentPrice } from '@/actions/price'
import { Product } from '@/actions/product'
import MediaBox from '@/components/common/MediaBox'
import EmptyList from '@/components/internal/EmptyList'
import InternalPageHeader from '@/components/internal/InternalPageHeader'
import PlatformWidget from '@/components/products/PlatformWidget'
import { ProductActions } from '@/components/products/ProductActions'
import ProductStatusBadge from '@/components/products/ProductStatusBadge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import useProductList from '@/hooks/use-product-list'
import useSessionUserProduct from '@/hooks/use-session-user-product'
import useSessionUser from '@/hooks/useSessionUser'
import { getIdFromRecord } from '@/utils'
import centsToDollars from '@/utils/cents-to-dollars'
import { getPlatform } from '@/utils/get-platform'
import { useRouter } from 'next/navigation'
import DownloadFigmaFile from '../purchases/DownloadFigmaFile'

const productsKind: Record<string, string> = {
  templates: 'template',
  // components: 'component',
  bundles: 'bundle',
  // subscriptions: 'subscription',
}

export default function ProductList() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [productToDelete, setProductToDelete] = useState<string | undefined>()
  const [kind, setKind] = useState('templates')
  const { data: user } = useSessionUser()
  const {
    data: products,
    isFetchingNextPage,
    isFetched,
    isError,
    ref,
    refetch,
  } = useProductList(
    user?.id?.toString() as string,
    (productsKind?.[kind] as string) ?? '',
  )

  const { deleteProduct } = useSessionUserProduct()

  // Check for permission error in URL params
  const hasPermissionError = searchParams.get('error') === 'no-permission'

  const handleDismissError = () => {
    router.replace('/dashboard/products')
  }

  const clickTabHandler = (kind: string) => () => {
    setKind(kind)
  }

  const clickProductRowHandler =
    (product: Product) => (e: React.MouseEvent) => {
      const productId = getIdFromRecord(product.id)

      const actionMenu = document.querySelector(
        `[data-actionid="product-action-${productId}"]`,
      )
      const menuClick = actionMenu?.contains(e.target as Node)
      if (menuClick) {
        e.stopPropagation()
        e.preventDefault()
        return
      }

      router.push(`/dashboard/products/${kind}/${getIdFromRecord(product.id)}`)
    }

  const clickDeleteHandler = (product: Product) => () => {
    setProductToDelete(getIdFromRecord(product.id))
  }

  const clickConfirmDeleteHandler = async () => {
    if (productToDelete) {
      await deleteProduct(productToDelete)
    }

    await refetch()
    setProductToDelete(undefined)
  }

  const clickEditHandler = (product: Product) => () => {
    router.push(`/dashboard/products/${kind}/${getIdFromRecord(product.id)}`)
  }

  return (
    <>
      <section className="container mx-auto">
        {/* Permission Error Banner */}
        {hasPermissionError && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-start gap-3">
            <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-red-800">
                Access Denied
              </h3>
              <p className="text-sm text-red-700 mt-1">
                You do not have permission to create content. Please contact an
                administrator to enable content creation permissions.
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismissError}
              className="text-red-600 hover:text-red-800 hover:bg-red-100 p-1 h-auto"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        )}

        <InternalPageHeader
          title={`Products/${kind}`}
          subtitle="Manage and organize your collection"
        />

        <Tabs defaultValue="templates" className="mb-2">
          <TabsList>
            {Object.keys(productsKind).map(kind => (
              <TabsTrigger
                key={kind}
                value={kind}
                className="capitalize"
                onClick={clickTabHandler(kind)}
              >
                {kind}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>

        {(products?.pages?.[0]?.length || 0) > 0 ? (
          <>
            <Table className="w-full">
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Platform</TableHead>
                  <TableHead>Creator</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Extras</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>

              <TableBody>
                {products?.pages.map(page =>
                  page.map((product: Product) => {
                    const image =
                      (product.fileSet as FileSet)?.files?.find(file =>
                        file.mimeType?.startsWith('image/'),
                      ) ?? (product?.imageSet as ImageSet)?.images?.[0]

                    return (
                      <TableRow key={product.id.toString()}>
                        <TableCell>
                          <div className="flex flex-row w-full gap-6 items-center justify-start">
                            <div
                              className="w-20 h-14 cursor-pointer"
                              onClick={clickProductRowHandler(product)}
                            >
                              <MediaBox
                                media={image}
                                width={79}
                                height={53}
                                ratio={79 / 53}
                              />
                            </div>

                            <h5>{product?.name}</h5>
                          </div>
                        </TableCell>

                        <TableCell>
                          {centsToDollars(
                            (product?.price as PaymentPrice)?.amount,
                          )}
                        </TableCell>

                        <TableCell className="min-w-[2rem]">
                          <PlatformWidget
                            platform={getPlatform(product.platforms)}
                            justIcon
                          />
                        </TableCell>

                        <TableCell>
                          {product?.owner?.handler ? (
                            <Link href={`/@${product.owner.handler}`}>
                              @{product.owner.handler}
                            </Link>
                          ) : (
                            '-'
                          )}
                        </TableCell>

                        <TableCell>
                          {format(product.created?.toString() ?? '', 'PP')}
                        </TableCell>

                        <TableCell className="max-w-[10rem]">
                          <DownloadFigmaFile product={product} likeIcon />
                        </TableCell>

                        <TableCell className="max-w-[12rem]">
                          <ProductStatusBadge status={product.status} />
                        </TableCell>

                        <TableCell
                          className="min-w-[1rem]"
                          id={`product-actions-${getIdFromRecord(product.id)}`}
                        >
                          <ProductActions
                            productId={getIdFromRecord(product.id) as string}
                            onDelete={clickDeleteHandler(product)}
                            onEdit={clickEditHandler(product)}
                          />
                        </TableCell>
                      </TableRow>
                    )
                  }),
                )}
              </TableBody>
            </Table>

            <div ref={ref} className="flex justify-center mt-4">
              {isFetchingNextPage && <span>Loading...</span>}
            </div>
          </>
        ) : (
          <>
            {!isError && isFetched ? (
              <EmptyList>
                <div className="flex flex-col gap-6 items-center">
                  <h2 className="text-gray-500">
                    You can starting by adding your first asset
                  </h2>

                  <Button
                    asChild
                    variant="default"
                    className="w-fit flex-nowrap"
                    size="xl"
                  >
                    <Link href="/dashboard/products/templates/add">
                      <Plus /> Add Asset
                    </Link>
                  </Button>
                </div>
              </EmptyList>
            ) : null}
          </>
        )}
      </section>

      <Dialog open={productToDelete !== undefined}>
        <DialogTrigger asChild>
          <span id="delete-product-trigger" className="hidden"></span>
        </DialogTrigger>

        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Delete Product</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this product? This action cannot
              be undone.
            </DialogDescription>
          </DialogHeader>

          <div className="flex items-center space-x-2 py-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setProductToDelete(undefined)}
            >
              Cancel
            </Button>

            <Button
              type="button"
              variant="destructive"
              onClick={clickConfirmDeleteHandler}
            >
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
