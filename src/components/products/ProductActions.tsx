import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Edit, MoreVertical, Trash } from 'lucide-react'

type Props = {
  productId?: string
  onDelete?: () => void
  onEdit?: () => void
}

export function ProductActions({ productId, onDelete, onEdit }: Props) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreVertical className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        align="end"
        data-actionid={`product-action-${productId}`}
      >
        <DropdownMenuItem
          className="cursor-pointer flex items-center gap-2"
          onClick={onEdit}
        >
          <Edit className="h-4 w-4" />
          <span>Edit</span>
        </DropdownMenuItem>

        <DropdownMenuItem
          className="cursor-pointer flex items-center gap-2 text-destructive"
          onClick={onDelete}
        >
          <Trash className="h-4 w-4" />
          <span>Delete</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
