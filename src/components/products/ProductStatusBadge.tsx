import { ProductStatus } from '@/actions/product'
import { Badge, BadgeProps } from '@/components/ui/badge'

type Props = {
  status?: ProductStatus
}

export default function ProductStatusBadge({ status }: Props) {
  let variant: BadgeProps['variant'] = 'default'
  let label = 'Unknown'

  if (status === ProductStatus.DRAFT) {
    variant = 'secondary'
    label = 'Draft'
  } else if (status === ProductStatus.HOLDING) {
    variant = 'purple'
    label = 'Holding'
  } else if (status === ProductStatus.INITIAL) {
    // variant = 'purple'
    label = 'Initial'
  } else if (status === ProductStatus.PENDING) {
    variant = 'yellow'
    label = 'Pending'
  } else if (status === ProductStatus.WAITING) {
    variant = 'blue'
    label = 'Waiting'
  } else if (status === ProductStatus.PUBLISHED) {
    variant = 'green'
    label = 'Published'
  }

  return (
    <Badge variant={variant} size="sm" className="uppercase">
      {label}
    </Badge>
  )
}
