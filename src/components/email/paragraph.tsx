import { Text, TextProps } from '@react-email/components'

type ParagraphProps = {
  children: TextProps['children']
  style?: TextProps['style']
}

export default function Paragraph({ children, style }: ParagraphProps) {
  return <Text style={style ? { ...text, ...style } : text}>{children}</Text>
}

const text = {
  color: '#6B7280',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '16px',
  lineHeight: '24px',
  margin: '20px 0',
}
