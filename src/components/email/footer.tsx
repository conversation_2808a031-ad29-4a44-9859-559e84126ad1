import { Img, Link, Section, Text } from '@react-email/components'
import Paragraph from './paragraph'

export default function Footer() {
  return (
    <Section>
      <Paragraph style={text}>
        Ignore this email if you didn&apos;t register at Tem.place
      </Paragraph>

      <Img src={`${process.env.HOST}/email/small-logo.png`} style={logo} />
      <Link
        href="https://tem.place"
        target="_blank"
        style={{ ...link, color: '#9CA3AF' }}
      >
        Templace
      </Link>
      <Text style={footer}>Where creators grow</Text>
      <Text style={footer}>and businesses move faster</Text>

      <Text style={small}>
        Templace is part of SuperSkills LLC, a company based in Texas, USA
      </Text>
    </Section>
  )
}

const logo = {
  margin: '20px 0',
}

const text = {
  margin: '40px 0 20px',
}

const footer = {
  color: '#9CA3AF',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontWeight: '400',
  fontSize: '12px',
  lineHeight: '16px',
  margin: '0',
}

const link = {
  color: '#9CA3AF',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '12px',
  lineHeight: '16px',
  fontWeight: '600',
  textDecoration: 'none',
}

const small = {
  color: '#9CA3AF',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '10px',
  lineHeight: '12px',
  fontWeight: '400',
  textDecoration: 'none',
  marginTop: '15px',
}
