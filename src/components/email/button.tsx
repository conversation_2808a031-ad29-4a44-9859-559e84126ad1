import {
  ButtonProps as BTNPRops,
  Button as REButton,
  Text,
} from '@react-email/components'

type ButtonProps = {
  href: BTNPRops['href']
  children: BTNPRops['children']
}

export default function Button({ href, children }: ButtonProps) {
  return (
    <REButton href={href} style={button}>
      <Text style={text}>{children}</Text>
    </REButton>
  )
}

const button = {
  display: 'block',
  backgroundColor: '#000000',
  borderRadius: '10px',
  textAlign: 'center' as const,
}

const text = {
  display: 'block',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  width: '100%',
  marginTop: '12px',
  marginBottom: '12px',
  fontWeight: '500',
  color: '#ffffff',
  fontSize: '16px',
  lineHeight: '24px',
}
