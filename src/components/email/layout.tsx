import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
} from '@react-email/components'
import type { ReactNode } from 'react'
import Footer from './footer'

type LayoutProps = {
  title: string | ReactNode
  preview?: string
  children: ReactNode
}

export default function Layout({ title, preview, children }: LayoutProps) {
  return (
    <Html>
      <Head />
      {preview && <Preview>{preview}</Preview>}
      <Body style={main}>
        <Img src={`${process.env.HOST}/email/large-logo.png`} style={logo} />
        <Container style={container}>
          {typeof title === 'string' ? (
            <Heading style={h1}>{title}</Heading>
          ) : (
            title
          )}
          {children}
          <Footer />
        </Container>
      </Body>
    </Html>
  )
}

const logo = {
  margin: '64px auto',
}

const main = {
  backgroundColor: '#ffffff',
  marginBottom: '64px',
}

const container = {
  padding: '24px',
  margin: '0 auto',
  maxWidth: '500px',
  display: 'block',
}

const h1 = {
  color: '#000',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '32px',
  lineHeight: '40px',
  letterSpacing: '-0.35px',
  fontWeight: 'bold',
  margin: '0 0 20px 0',
  padding: '0',
}
