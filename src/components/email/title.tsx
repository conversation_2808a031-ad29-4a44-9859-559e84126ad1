import { Heading, HeadingProps } from '@react-email/components'

type TitleProps = {
  children: HeadingProps['children']
}

export default function Title({ children }: TitleProps) {
  return <Heading style={h1}>{children}</Heading>
}

const h1 = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '0 0 16px 0',
  padding: '0',
}
