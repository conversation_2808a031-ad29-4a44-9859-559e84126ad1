import { LinkProps, Link as RELink } from '@react-email/components'

type LnkProps = {
  href: LinkProps['href']
  children: LinkProps['children']
}

export default function Link({ href, children }: LnkProps) {
  return (
    <RELink href={href} style={link}>
      {children}
    </RELink>
  )
}

const link = {
  color: '#2754C5',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  textDecoration: 'underline',
}
