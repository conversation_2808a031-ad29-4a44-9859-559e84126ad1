import { Section } from '@react-email/components'

type CodeProps = {
  loginCode: string
}

export default function Code({ loginCode }: CodeProps) {
  return <Section style={code}>{loginCode}</Section>
}

const code = {
  display: 'inline-block',
  width: '100%',
  maxWidth: '500px',
  backgroundColor: '#f4f4f4',
  borderRadius: '10px',
  border: '1px solid #E5E7EB',
  color: '#000',
  fontSize: '16px',
  lineHeight: '24px',
  fontWeight: '600',
  padding: '16px',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
}
