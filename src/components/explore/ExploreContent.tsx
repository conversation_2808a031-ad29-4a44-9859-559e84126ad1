import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import ExploreHeader from '@/components/explore/ExploreHeader'
import ProductGrid from '@/components/common/ProductGrid'

type ExploreTab = {
  key: string
  label: string
  flair: string
  content?: any[]
}

type Props = React.PropsWithChildren<{
  title: string
  subtitle: string
  tabs: Array<ExploreTab>
  defaultTab: string
  useMiniBox?: boolean
}>

export default function ExploreContent({
  title,
  subtitle,
  tabs,
  defaultTab,
  useMiniBox = false,
}: Props) {
  return (
    <div className="flex flex-col gap-6 min-h-screen">
      <ExploreHeader title={title} subtitle={subtitle} />

      <Tabs defaultValue={defaultTab} className="mb-8 bg-none">
        <TabsList>
          {tabs.map(item => (
            <TabsTrigger value={item.key} key={item.key}>
              {item.label ?? item}
            </TabsTrigger>
          ))}
        </TabsList>

        {tabs.map(item => (
          <TabsContent value={item.key} key={item.key}>
            <ProductGrid
              useMini={useMiniBox}
              flair={item.flair}
              initialData={{ pages: [item.content], pageParams: [0] }}
            />
          </TabsContent>
        ))}
      </Tabs>
    </div>
  )
}
