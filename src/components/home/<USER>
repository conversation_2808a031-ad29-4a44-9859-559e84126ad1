'use client'
import { useState } from 'react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { saveMailing } from '@/actions/mailing'

export default function FormContact() {
  const [status, setStatus] = useState('initial')
  const [form, setForm] = useState({
    name: '',
    email: '',
  })

  const onChangeHandler = e => {
    const nextForm = { ...form, [e.target.name]: e.target.value }
    setForm(nextForm)
  }

  const onClickEarlyAccessHandler = async () => {
    if (!form?.name || !form?.email) {
      return
    }

    setStatus('sending')

    await saveMailing(form.name, form.email)

    setStatus('success')
  }

  const inputClassName =
    'text-sm border-none placeholder:text-gray-400 w-full lg:w-1/3 bg-gray-700 text-gray-300 placeholder-gray-400 rounded-md px-4 py-2 h-10 ring-1 ring-gray-700 outline-none'

  return (
    <div className="flex flex-col lg:flex-row gap-6 py-16 items-center">
      {status !== 'success' ? (
        <>
          <Input
            type="text"
            name="name"
            placeholder="Name"
            className={inputClassName}
            value={form?.name}
            onChange={onChangeHandler}
            disabled={status === 'sending'}
          />
          <Input
            type="email"
            name="email"
            placeholder="Email"
            className={inputClassName}
            value={form?.email}
            onChange={onChangeHandler}
            disabled={status === 'sending'}
          />
          <Button
            type="button"
            variant="secondary"
            className="w-full lg:w-1/3 h-10 py-2"
            onClick={onClickEarlyAccessHandler}
            disabled={status === 'sending'}
          >
            Get early access
          </Button>
        </>
      ) : (
        <div className="w-full flex flex-col gap-4 text-center">
          <h2 className="text-3xl font-bold">Thank you!</h2>
          <p className="text-gray-500 text-sm">
            We will reach out to you shortly.
          </p>
        </div>
      )}
    </div>
  )
}
