'use client'

import TemplaceLogo from '../common/TemplaceLogo'
import Header from '../common/Header'
import { usePageTitle } from '@/hooks/use-page-title'
import UserBox from '../common/UserBox'
import AppSidebarClient from '../common/AppSidebarClient'
import AccessPanel from '../common/Header/AccessPanel'

type Props = React.PropsWithChildren<{
  title?: string
}>

export default function Wrapper({ children, title }: Props) {
  const pageTitle = usePageTitle(title)

  return (
    <>
      <aside className="flex fixed left-0 justify-between top-0 z-40 md:h-screen w-full md:w-16 md:flex-col bg-white md:border-r border-gray-200 border-b md:border-b-0">
        <TemplaceLogo full className="flex w-[3.9rem] md:w-16" />
        <AppSidebarClient />
        <div className="hidden md:flex overflow-x-hidden items-center justify-center h-[5rem]">
          <UserBox side="right" />
        </div>
      </aside>
      <Header className="pr-6 pl-6 md:flex hidden items-center justify-between bg-background/60 backdrop-blur-md">
        <div className="w-full">
          {pageTitle && (
            <h1 className="text-lg font-semibold text-gray-900 truncate">
              {pageTitle}
            </h1>
          )}
        </div>
        <div className="flex items-center justify-end">
          <AccessPanel />
        </div>
      </Header>
      {children}
    </>
  )
}
