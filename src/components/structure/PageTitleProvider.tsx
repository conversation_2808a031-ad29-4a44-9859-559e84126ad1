'use client'

import { createContext, use<PERSON>ontext, ReactNode, useState } from 'react'

interface PageTitleContextType {
  setPageTitle: (title: string) => void
  pageTitle: string | null
}

const PageTitleContext = createContext<PageTitleContextType | undefined>(
  undefined,
)

export function usePageTitleContext() {
  const context = useContext(PageTitleContext)
  if (!context) {
    throw new Error(
      'usePageTitleContext must be used within a PageTitleProvider',
    )
  }
  return context
}

interface PageTitleProviderProps {
  children: ReactNode
}

export function PageTitleProvider({ children }: PageTitleProviderProps) {
  const [pageTitle, setPageTitle] = useState<string | null>(null)

  return (
    <PageTitleContext.Provider value={{ pageTitle, setPageTitle }}>
      {children}
    </PageTitleContext.Provider>
  )
}
