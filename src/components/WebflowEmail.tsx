'use client'

import { useCallback, useState } from 'react'

import ConfirmWebflowEmailModal from '@/components/internal/ConfirmWebflowEmailModal'
import useSessionUser from '@/hooks/useSessionUser'
import useUser from '@/hooks/useUser'

export default function WebflowEmail() {
  const { data: sessionUser } = useSessionUser()
  const {
    data: user,
    isSuccess,
    updateUserProfile,
  } = useUser(sessionUser?.id.toString())
  const [maybeEmail, setMaybeEmail] = useState(
    user?.profile?.webflow?.email ?? '',
  )

  const confirmEmailHandler = useCallback(
    async (email: string) => {
      if (user?.profile?.webflow?.email !== email && email === maybeEmail) {
        await updateUserProfile('webflow.email', email)
        setMaybeEmail('')
      }
    },
    [updateUserProfile, user?.profile?.webflow?.email, maybeEmail],
  )

  const updateEmailHandler = (email: string) => {
    setMaybeEmail(email)
  }

  const openModal =
    Boolean(user) && isSuccess && user?.profile?.webflow?.email === undefined

  return (
    Bo<PERSON>an(user) && (
      <ConfirmWebflowEmailModal
        openModal={openModal}
        clickCloseModal={null}
        email={maybeEmail}
        onEmailConfirmed={confirmEmailHandler}
        onEmailUpdate={updateEmailHandler}
      />
    )
  )
}
