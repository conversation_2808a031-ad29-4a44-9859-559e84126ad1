import React from 'react'
import get from 'lodash/get'
import { useFormContext } from 'react-hook-form'
import { Upload } from 'lucide-react'
import { GoFileZip } from 'react-icons/go'

import { File as FileRecord } from '@/actions/file'
import DropUpload from '@/components/common/DropUpload'
import FieldBox from '@/components/form/FieldBox'

/**
 *
 */
export default function FormAddTemplateZipUpload({
  fieldName,
  title = 'Zip file',
  instructions = 'Include your Zip file',
}: any) {
  const {
    watch,
    setValue,
    formState: { errors },
  } = useFormContext()

  const currentFile = watch(fieldName)

  const handleUpload = (givenFiles: FileRecord[]) => {
    if (givenFiles?.[0]) {
      setValue(fieldName, givenFiles[0], {
        shouldDirty: true,
        shouldTouch: true,
      })
    }
  }

  return (
    <FieldBox>
      <h3 className="font-medium text-sm mb-1">{title}</h3>
      <div className="text-xs text-gray-400 mb-4">{instructions}</div>

      <DropUpload
        onUpload={handleUpload}
        folder="zip"
        dropzoneOptions={{
          maxFiles: 1,
          multiple: false,
          accept: {
            'application/zip': ['.zip'],
          },
        }}
      >
        <div className="bg-gray-100 p-6 rounded-lg grid place-content-center border-2 border-dashed border-gray-300 mb-2">
          <div className="flex flex-col gap-2 items-center text-center text-gray-500 text-sm">
            {currentFile === undefined ? (
              <>
                <Upload className="w-12 h-12 text-gray-400" />
                <span>Drag & drop your Zip file here</span>
                <small>(or click to browse)</small>
              </>
            ) : (
              <>
                <GoFileZip className="w-12 h-12 text-gray-400" />
                <span>{currentFile.metadata.name}</span>
                <small>(click to change)</small>
              </>
            )}
          </div>
        </div>
      </DropUpload>

      <div>
        {get(errors, fieldName)?.message && (
          <small className="text-red-500 text-xs mt-1">
            {String(get(errors, fieldName)?.message)}
          </small>
        )}
      </div>
    </FieldBox>
  )
}
