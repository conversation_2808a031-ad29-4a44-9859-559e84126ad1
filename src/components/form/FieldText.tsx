'use client'

import { useFormContext } from 'react-hook-form'

import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'
import React from 'react'

type Props = Omit<
  Partial<React.InputHTMLAttributes<HTMLInputElement>>,
  'name' | 'onChange' | 'prefix' | 'suffix' | 'disabled' | 'className'
> & {
  label: React.ReactNode
  name: string
  required?: boolean
  maxLength?: number
  instructions?: React.ReactNode
  disabled?: boolean
  prefix?: React.ReactNode
  suffix?: React.ReactNode
  className?: string
  inputClassName?: string
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void
  afterChange?: (value: string, fieldName: string) => void
}

export default function FieldText({
  label,
  name,
  required = false,
  maxLength = Infinity,
  instructions,
  disabled: givenDisabled = false,
  prefix,
  suffix,
  inputClassName,
  className,
  onChange,
  afterChange,
  ...props
}: Props) {
  const {
    watch,
    setValue,
    formState: { errors, disabled },
  } = useFormContext()

  const defaultOnChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setValue(name, value, { shouldDirty: true, shouldTouch: true })
    afterChange?.(value, name)
  }

  const handleChange = onChange ?? defaultOnChange

  return (
    <div className={cn(className)} data-disabled={disabled || givenDisabled}>
      <Label htmlFor={name} className="block mb-1">
        {label}
        {required ? <span className="text-red-500">*</span> : null}
      </Label>

      {instructions ? (
        <>
          {typeof instructions === 'string' ? (
            <div className="text-xs text-gray-500 mb-2">{instructions}</div>
          ) : (
            instructions
          )}
        </>
      ) : null}

      <div className="flex gap-2 items-center">
        {prefix ? (
          <>
            {typeof prefix === 'string' ? (
              <div className="text-gray-500 text-sm">{prefix}</div>
            ) : (
              prefix
            )}
          </>
        ) : null}

        <div className="flex-grow">
          <div
            className={cn(
              'flex-grow h-fit border border-input bg-background rounded-md focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2',
              inputClassName,
            )}
          >
            <Input
              {...props}
              id={name}
              value={watch(name) ?? ''}
              disabled={disabled}
              onChange={handleChange}
              className="w-full border-0 bg-transparent px-2 py-1 text-[0.875rem] focus:outline-none focus:ring-0 focus-visible:ring-0 min-w-[5rem]"
              maxLength={maxLength}
            />
          </div>
        </div>

        {suffix ? (
          <>
            {typeof suffix === 'string' ? (
              <div className="text-gray-500 text-sm">{suffix}</div>
            ) : (
              suffix
            )}
          </>
        ) : null}
      </div>

      <div className="h-4 flex text-xs mt-1">
        {errors?.[name]?.message ? (
          <div className="text-red-500">{String(errors[name].message)}</div>
        ) : null}

        {maxLength < Infinity ? (
          <div className="flex-grow text-right text-gray-500">
            {watch(name)?.length ?? '0'}/{maxLength}
          </div>
        ) : null}
      </div>
    </div>
  )
}
