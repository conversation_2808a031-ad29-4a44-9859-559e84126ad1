'use client'

import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod'
import { ChevronRight } from 'lucide-react'
import Link from 'next/link'
import { useRout<PERSON> } from 'next/navigation'
import { useCallback, useEffect, useRef } from 'react'
import { FormProvider, useForm } from 'react-hook-form'

import {
  PlatformKindEnum,
  PlatformsEnum,
  Product,
  ProductPlatformsKeys,
  ProductStatus,
} from '@/actions/product'
import CategoriesSelection from '@/components/form/CategoriesSelection'
import EmbedPreview from '@/components/form/EmbedPreview'
import FeaturesSelection from '@/components/form/FeaturesSelection'
import FieldBox from '@/components/form/FieldBox'
import FieldRadio from '@/components/form/FieldRadio'
import FieldText from '@/components/form/FieldText'
import FromActions from '@/components/form/FormActions'
import FormAddTemplateFigmaUpload from '@/components/form/FormAddTemplateFigmaUpload'
import FormAddTemplateZipUpload from '@/components/form/FormAddTemplateZipUpload'
import FormContent from '@/components/form/FormContent'
import FormHeader from '@/components/form/FormHeader'
import FormSection from '@/components/form/FormSection'
import FormSubheader from '@/components/form/FormSubheader'
import ImagesAndVideosUpload from '@/components/form/ImagesAndVideosUpload'
import WebflowSendTemplate from '@/components/form/WebflowSendTemplate'
import ProfileProductCreateLink from '@/components/internal/ProfileProductCreateLink'
import PlatformWidget from '@/components/products/PlatformWidget'
import ProductStatusBadge from '@/components/products/ProductStatusBadge'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { useLoading } from '@/hooks/use-loading'
import useSessionUserProduct from '@/hooks/use-session-user-product'
import { FormSaveTemplateData, FormSaveTemplateSchema } from '@/schemas'
import { slugMe } from '@/utils'
import { getPlatform } from '@/utils/get-platform'

type Props = {
  productId: string
  flair?: string[]
}

export default function FormSaveTemplate({ productId, flair }: Props) {
  const lastPlatform = useRef<ProductPlatformsKeys | undefined>(undefined)
  const router = useRouter()
  const { isLoading, setLoading, clearLoading } = useLoading()
  const {
    data: product,
    error,
    isLoading: productIsLoading,
    updateProduct,
  } = useSessionUserProduct(productId)

  const formMethods = useForm<FormSaveTemplateData>({
    disabled: productIsLoading,
    defaultValues: {
      name: '',
      slug: '',
      description: '',
      liveUrl: '',
      isFree: false,
      imagesAndVideos: [],
      includes: [],
      features: [],
      includeFigmaFile: false,
    },
    resolver: zodResolver(FormSaveTemplateSchema),
    mode: 'onChange',
  })

  const { handleSubmit, watch, setValue } = formMethods

  const redirectToDashboard = () => {
    router.push('/dashboard/products')
  }
  const redirectToNewTemplate = () => {
    router.push('/dashboard/products/templates/add')
  }

  const onValid = async (data: FormSaveTemplateData) => {
    try {
      await updateProduct({
        ...data,
        flair: [...(data?.flair ?? []), ...(flair ?? [])],
        status: ProductStatus.PUBLISHED,
      })
    } catch (error) {
      console.error('Error creating product:', error)
      throw error
    } finally {
      formMethods.reset(undefined, { keepValues: true, keepDirty: false })
    }
  }

  const clickPublishHandler = async () => {
    await handleSubmit(onValid, console.log)()
  }

  const clickDraftHandler = useCallback(async () => {
    const data = formMethods.getValues()
    await updateProduct({
      ...data,
      flair: [...(data?.flair ?? []), ...(flair ?? [])],
      status: ProductStatus.DRAFT,
    })
    formMethods.trigger()
  }, [formMethods, updateProduct, flair])

  const clickUnpublishHandler = async () => {
    formMethods.trigger()
    return clickDraftHandler()
  }

  const isFree = watch('isFree')
  useEffect(() => {
    if (isFree) {
      setValue('price', 0)
    }
  }, [isFree, watch, setValue])

  useEffect(() => {
    if (product) {
      setValue('name', product.name)
      setValue('slug', product.slug)
      setValue('description', product.description ?? '')
      setValue('liveUrl', product.liveUrl ?? '')
      setValue('price', (product?.price?.amount ?? 0) / 100)
      setValue('isFree', (product?.price?.amount ?? 0) === 0)
      setValue(
        'platform',
        getPlatform(product?.platforms) as ProductPlatformsKeys,
      )

      if (product?.platforms?.framer) {
        setValue('platforms.framer.remixUrl', product.platforms.framer.remixUrl)
        setValue('platforms.framer.kind', PlatformKindEnum.TEMPLATE)
        setValue(
          'platforms.framer.embedPreview',
          product.platforms.framer.embedPreview,
        )
      } else if (product?.platforms?.webflow) {
        setValue('platforms.webflow.kind', PlatformKindEnum.TEMPLATE)
        setValue('platforms.webflow.auto', product.platforms.webflow.auto)
        setValue(
          'platforms.webflow.distributionId',
          product.platforms.webflow.distributionId,
        )
        setValue(
          'platforms.webflow.distributionStatus',
          product.platforms.webflow.distributionStatus,
        )
      } else if (product?.platforms?.vzero) {
        setValue('platforms.vzero.github', product.platforms.vzero.github)
        setValue('platforms.vzero.file', product.platforms.vzero.file)
        setValue(
          'platforms.vzero.embedPreview',
          product.platforms.vzero.embedPreview,
        )
      }

      setValue(
        'imagesAndVideos',
        product?.fileSet?.files?.filter(
          item =>
            item.mimeType?.startsWith('image') ||
            item.mimeType?.startsWith('video'),
        ) ?? [],
      )

      setValue('includes', product.includes ?? [])
      setValue('features', product.features ?? [])

      const figmaFile = product.fileSet?.files?.find(item =>
        item.flair?.includes('figma'),
      )
      setValue('includeFigmaFile', Boolean(figmaFile))
      if (figmaFile) {
        setValue('figmaFile', figmaFile)
      }
    }
  }, [product, setValue])

  useEffect(() => {
    if (productId && productIsLoading && !isLoading) {
      setLoading('Loading product...')
    } else if (productId && !productIsLoading && isLoading) {
      clearLoading()
    }
  }, [isLoading, setLoading, clearLoading, productIsLoading, productId])

  const platform = watch('platform')
  useEffect(() => {
    if (platform !== lastPlatform.current) {
      // console.log('platform changed', platform, lastPlatform.current)
      if (lastPlatform.current !== undefined) {
        // clickDraftHandler()
      }
      lastPlatform.current = platform
    }
  }, [platform, clickDraftHandler])

  const onChangeIsFree = (value: boolean) => {
    setValue('isFree', value, { shouldDirty: true, shouldValidate: true })
  }

  return (
    <>
      <FormHeader
        link="/dashboard/products"
        title={
          <>
            Templates <ChevronRight className="w-3 h-3" /> New
          </>
        }
      >
        {Boolean(product?.status) && (
          <ProductStatusBadge status={product?.status} />
        )}

        <Separator orientation="vertical" />

        <Button type="button" variant="outline" asChild>
          <Link href="/dashboard/products">Close</Link>
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={clickDraftHandler}
          disabled={productIsLoading || !formMethods.formState.isDirty}
        >
          Save
        </Button>

        {product?.status === ProductStatus.PUBLISHED ? (
          <Button
            type="button"
            variant="outline"
            onClick={clickUnpublishHandler}
          >
            Unpublish
          </Button>
        ) : (
          <Button
            size="sm"
            onClick={clickPublishHandler}
            disabled={
              productIsLoading ||
              !formMethods.formState.isDirty ||
              !formMethods.formState.isValid
            }
          >
            Publish
          </Button>
        )}
      </FormHeader>

      {productId && product?.status === ProductStatus.PUBLISHED ? (
        <>
          <FormContent>
            <ProfileProductCreateLink
              product={product as Product}
              disabled={isLoading}
            />
          </FormContent>
          <Separator className="my-6 md:my-8" />
        </>
      ) : null}

      <FormProvider {...formMethods}>
        <form onSubmit={handleSubmit(onValid)} className="min-h-screen w-full">
          <FormContent>
            <FormSection>
              <ImagesAndVideosUpload />
            </FormSection>

            <Separator />

            <FormSection>
              <FormSubheader
                title="General"
                description="Explain what your template is about."
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                <FieldText
                  label="Name"
                  name="name"
                  instructions="Use a short, catchy name."
                  placeholder="Awesome Template"
                  maxLength={32}
                  required
                  afterChange={value => {
                    const slug = slugMe(value)
                    setValue('slug', slug, {
                      shouldDirty: true,
                      shouldTouch: true,
                    })
                  }}
                />
                <FieldText
                  label="Byline"
                  name="description"
                  instructions="Describe your template in a few words."
                  placeholder="Best Agency Template"
                  maxLength={380}
                  required
                />
              </div>

              <FieldRadio
                required
                label="Platform"
                name="platform"
                instructions="Select the platform for your template."
                options={[
                  {
                    label: <PlatformWidget platform={PlatformsEnum.WEBFLOW} />,
                    value: PlatformsEnum.WEBFLOW,
                  },
                  {
                    label: <PlatformWidget platform={PlatformsEnum.FRAMER} />,
                    value: PlatformsEnum.FRAMER,
                  },
                  {
                    label: <PlatformWidget platform={PlatformsEnum.VZERO} />,
                    value: PlatformsEnum.VZERO,
                  },
                ]}
              />

              <EmbedPreview />

              <FieldText
                label="Preview Link"
                name="liveUrl"
                instructions="A link to see previews of your template."
                placeholder="https://example.com/preview"
                required
              />

              {watch('platform') === PlatformsEnum.FRAMER && (
                <FieldText
                  label="Remix Link"
                  name="platforms.framer.remixUrl"
                  instructions="A link to remix your Framer project."
                  placeholder="https://framer.com/projects/project-id"
                  required
                />
              )}

              {watch('platform') === PlatformsEnum.VZERO && (
                <>
                  <FieldText
                    label="Github URL"
                    name="platforms.vzero.github"
                    instructions="A link to your v0 repository."
                    placeholder="https://github.com/username/repo"
                  />
                  <FormAddTemplateZipUpload
                    title="Upload your v0 project"
                    fieldName="platforms.vzero.file"
                  />
                </>
              )}

              {watch('platform') === PlatformsEnum.WEBFLOW && (
                <WebflowSendTemplate productId={productId} />
              )}
            </FormSection>

            <Separator />

            <FormSection>
              <FormSubheader
                title="Details"
                description="Make your template easy to find."
              />

              <FieldBox>
                <FieldText
                  name="price"
                  label="Price"
                  instructions="Price for your template, please specify when it's free."
                  prefix={
                    <span className="flex items-center text-gray-500 h-full text-sm">
                      $
                    </span>
                  }
                  required
                  inputClassName="w-32"
                  disabled={isFree}
                  suffix={
                    <div className="flex flex-shrink items-center gap-2 text-nowrap self-end">
                      <Switch
                        onCheckedChange={onChangeIsFree}
                        checked={isFree}
                      />
                      <Label
                        htmlFor="isFree"
                        className="text-xs cursor-pointer"
                      >
                        Free template
                      </Label>
                    </div>
                  }
                />
              </FieldBox>

              <CategoriesSelection fieldName="includes" />

              <FeaturesSelection fieldName="features" />

              <FormAddTemplateFigmaUpload
                swtichFieldName="includeFigmaFile"
                fileFieldName="figmaFile"
              />
            </FormSection>

            <Separator />

            <FromActions>
              <Button type="button" variant="outline" asChild>
                <Link href="/dashboard/products">Close</Link>
              </Button>

              <Button
                type="button"
                variant="outline"
                onClick={clickDraftHandler}
                disabled={productIsLoading || !formMethods.formState.isDirty}
              >
                Save
              </Button>

              <Button
                type="button"
                onClick={clickPublishHandler}
                disabled={productIsLoading}
              >
                Publish
              </Button>
            </FromActions>
          </FormContent>
        </form>
      </FormProvider>

      <Dialog
        open={Boolean(productId && error)}
        onOpenChange={redirectToDashboard}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Error</DialogTitle>
            <DialogDescription>{error?.message}</DialogDescription>
          </DialogHeader>

          <DialogFooter className="sm:justify-end">
            <DialogClose asChild>
              <Button type="button" variant="secondary" size="xs">
                Dashboard
              </Button>
            </DialogClose>

            <Button type="button" size="xs" onClick={redirectToNewTemplate}>
              New Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
