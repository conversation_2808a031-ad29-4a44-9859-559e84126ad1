import { Product } from '@/actions/product'
import TemplateSelectorCard from '@/components/form/TemplateSelectorCard'
import { Button } from '@/components/ui/button'
import useProductAssetsList from '@/hooks/use-product-assets-list'
import useSessionUser from '@/hooks/useSessionUser'
import { FormSaveBundleData } from '@/schemas'
import { getPlatform } from '@/utils/get-platform'
import { FieldPath, useFieldArray, useFormContext } from 'react-hook-form'
import FieldBox from './FieldBox'
import FormSubheader from './FormSubheader'

type Props = {
  fieldName: FieldPath<FormSaveBundleData>
}

export default function TemplateSelector({ fieldName }: Props) {
  const { data: user } = useSessionUser()
  const {
    watch,
    control,
    formState: { errors },
    getFieldState,
  } = useFormContext<FormSaveBundleData>()

  const { fields, append, remove } = useFieldArray<any>({
    control,
    name: fieldName,
  })

  const items = watch(fieldName) as string[]
  // console.log('items', items)

  const {
    data: products,
    isFetchingNextPage,
    // isFetched,
    // isError,
    ref,
  } = useProductAssetsList(user?.id?.toString())

  const clickProductHandler = (product: Product) => () => {
    const index = items.findIndex(item => item === product.id.toString())
    if (index !== -1) {
      remove(index)
    } else {
      append(product.id.toString())
    }
  }

  const clickClearSelection = () => {
    remove()
  }

  const fieldState = getFieldState(fieldName)

  return (
    <div>
      <FormSubheader
        title="Select Assets"
        description="Select what composes your bundle"
      />

      <FieldBox>
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-medium text-sm">
            Your published Templates and Components
          </h3>

          <Button
            variant="outline"
            size="xs"
            onClick={clickClearSelection}
            disabled={items.length === 0}
          >
            Clear Selection
          </Button>
        </div>

        {(products?.pages?.[0]?.length || 0) > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 overflow-y-auto max-h-56">
            {products?.pages.map(page =>
              page?.map((product: Product) => {
                const media = product.fileSet?.files?.find(
                  file =>
                    file.mimeType?.startsWith('video/') ||
                    file.mimeType?.startsWith('image/'),
                )

                return (
                  <TemplateSelectorCard
                    key={product.id.toString()}
                    id={product.id.toString()}
                    media={media}
                    checked={Boolean(
                      items.find(item => item === product.id.toString()),
                    )}
                    title={product.name}
                    platform={getPlatform(product.platforms) ?? ''}
                    onClick={clickProductHandler(product)}
                  />
                )
              }),
            )}

            <div ref={ref} className="flex mt-1">
              {isFetchingNextPage && (
                <small className="text-xs text-gray-400">Loading...</small>
              )}
            </div>
          </div>
        ) : null}

        <small className="text-xs text-red-500">
          {fieldState?.error?.message ? (
            String(fieldState?.error?.message)
          ) : (
            <>&nbsp;</>
          )}
        </small>
      </FieldBox>
    </div>
  )
}
