'use client'

import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { ChevronRight } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { FormProvider, useForm } from 'react-hook-form'

import { PlatformsEnum, Product, ProductStatus } from '@/actions/product'
import CategoriesSelection from '@/components/form/CategoriesSelection'
import FeaturesSelection from '@/components/form/FeaturesSelection'
import FieldText from '@/components/form/FieldText'
import FromActions from '@/components/form/FormActions'
import FormContent from '@/components/form/FormContent'
import FormHeader from '@/components/form/FormHeader'
import FormSection from '@/components/form/FormSection'
import FormSubheader from '@/components/form/FormSubheader'
import ImagesAndVideosUpload from '@/components/form/ImagesAndVideosUpload'
import PriceBox from '@/components/form/PriceBox'
import TemplateSelector from '@/components/form/TemplateSelector'
import ProfileProductCreateLink from '@/components/internal/ProfileProductCreateLink'
import ProductStatusBadge from '@/components/products/ProductStatusBadge'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Separator } from '@/components/ui/separator'
import { useLoading } from '@/hooks/use-loading'
import useSessionUserProduct from '@/hooks/use-session-user-product'
import { FormSaveBundleData, FormSaveBundleSchema } from '@/schemas'
import { slugMe } from '@/utils'

type Props = {
  productId: string
  flair?: string[]
}

export default function FormSaveBundle({ productId, flair }: Props) {
  const router = useRouter()
  const { isLoading, setLoading, clearLoading } = useLoading()
  const {
    data: product,
    error,
    isLoading: productIsLoading,
    updateProduct,
  } = useSessionUserProduct(productId)

  const formMethods = useForm<FormSaveBundleData>({
    disabled: productIsLoading,
    defaultValues: {
      name: '',
      slug: '',
      description: '',
      isFree: false,
      imagesAndVideos: [],
      includes: [],
      features: [],
      platform: PlatformsEnum.BUNDLE,
      platforms: {
        bundle: {
          assets: [],
        },
      },
    },
    resolver: zodResolver(FormSaveBundleSchema),
    mode: 'onChange',
  })

  const { handleSubmit, watch, setValue } = formMethods

  const redirectToDashboard = () => {
    router.push('/dashboard/products')
  }
  const redirectToNewBundle = () => {
    router.push('/dashboard/products/bundles/add')
  }

  const onValid = async (data: FormSaveBundleData) => {
    try {
      await updateProduct({
        ...data,
        platform: PlatformsEnum.BUNDLE,
        flair: [...(data?.flair ?? []), ...(flair ?? [])],
        status: ProductStatus.PUBLISHED,
      })
    } catch (error) {
      console.error('Error creating product:', error)
      throw error
    } finally {
      formMethods.reset(undefined, { keepValues: true, keepDirty: false })
    }
  }

  const clickPublishHandler = async () => {
    await handleSubmit(onValid, console.log)()
  }

  const clickDraftHandler = async () => {
    const data = formMethods.getValues()
    await updateProduct({
      ...data,
      flair: [...(data?.flair ?? []), ...(flair ?? [])],
      status: ProductStatus.DRAFT,
    })
    formMethods.trigger()
  }

  useEffect(() => {
    if (product) {
      setValue('name', product.name)
      setValue('slug', product.slug)
      setValue('description', product.description ?? '')
      setValue('price', (product?.price?.amount ?? 0) / 100)
      setValue('isFree', (product?.price?.amount ?? 0) === 0)
      setValue('platform', PlatformsEnum.BUNDLE)

      setValue(
        'platforms.bundle.assets',
        product.platforms?.bundle?.assets.map(item => item.id.toString()) ?? [],
      )

      setValue(
        'imagesAndVideos',
        product?.fileSet?.files?.filter(
          item =>
            item.mimeType?.startsWith('image') ||
            item.mimeType?.startsWith('video'),
        ) ?? [],
      )

      setValue('includes', product.includes ?? [])
      setValue('features', product.features ?? [])
    }
  }, [product, setValue])

  useEffect(() => {
    if (productId && productIsLoading && !isLoading) {
      setLoading('Loading product...')
    } else if (productId && !productIsLoading && isLoading) {
      clearLoading()
    }
  }, [isLoading, setLoading, clearLoading, productIsLoading, productId])

  const currentPrice = watch('price')
  useEffect(() => {
    if (currentPrice !== 0) {
      setValue('isFree', false, { shouldDirty: true, shouldValidate: true })
    }
  }, [currentPrice, setValue])

  return (
    <>
      <FormHeader
        link="/dashboard/products"
        title={
          <>
            Templates <ChevronRight className="w-3 h-3" /> New
          </>
        }
      >
        {Boolean(product?.status) && (
          <ProductStatusBadge status={product?.status} />
        )}

        <Separator orientation="vertical" />

        <Button type="button" variant="outline" asChild>
          <Link href="/dashboard/products">Close</Link>
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={clickDraftHandler}
          disabled={productIsLoading || !formMethods.formState.isDirty}
        >
          Save
        </Button>

        {product?.status === ProductStatus.PUBLISHED ? (
          <Button type="button" variant="outline" onClick={clickDraftHandler}>
            Unpublish
          </Button>
        ) : (
          <Button
            size="sm"
            onClick={clickPublishHandler}
            disabled={
              productIsLoading ||
              !formMethods.formState.isDirty ||
              !formMethods.formState.isValid
            }
          >
            Publish
          </Button>
        )}
      </FormHeader>

      {productId && product?.status === ProductStatus.PUBLISHED ? (
        <>
          <FormContent>
            <ProfileProductCreateLink
              product={product as Product}
              disabled={isLoading}
            />
          </FormContent>
          <Separator className="my-6 md:my-8" />
        </>
      ) : null}

      <FormProvider {...formMethods}>
        <form onSubmit={handleSubmit(onValid)} className="min-h-screen w-full">
          <FormContent>
            <FormSection>
              <FormSubheader
                title="General"
                description="Explain what your template is about."
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                <FieldText
                  label="Name"
                  name="name"
                  instructions="Use a short, catchy name."
                  placeholder="Awesome Template"
                  maxLength={32}
                  required
                  afterChange={value => {
                    const slug = slugMe(value)
                    setValue('slug', slug, {
                      shouldDirty: true,
                      shouldTouch: true,
                    })
                  }}
                />

                <FieldText
                  label="Byline"
                  name="description"
                  instructions="Describe your template in a few words."
                  placeholder="Best Agency Template"
                  maxLength={380}
                  required
                />
              </div>
            </FormSection>

            <Separator />

            <FormSection>
              <ImagesAndVideosUpload title="Bundle Thumbnail" subtitle="" />
            </FormSection>

            <Separator />

            <TemplateSelector fieldName="platforms.bundle.assets" />

            <Separator />

            <FormSection>
              <FormSubheader
                title="Details"
                description="Make your template easy to find."
              />

              <PriceBox />

              <CategoriesSelection fieldName="includes" />

              <FeaturesSelection fieldName="features" />
            </FormSection>

            <Separator />

            <FromActions>
              <Button type="button" variant="outline" asChild>
                <Link href="/dashboard/products">Close</Link>
              </Button>

              <Separator orientation="vertical" />

              <Button
                type="button"
                variant="outline"
                onClick={clickDraftHandler}
                disabled={productIsLoading || !formMethods.formState.isDirty}
              >
                Save
              </Button>

              <Button
                type="button"
                onClick={clickPublishHandler}
                disabled={
                  productIsLoading ||
                  !formMethods.formState.isDirty ||
                  !formMethods.formState.isValid
                }
              >
                Publish
              </Button>
            </FromActions>
          </FormContent>
        </form>
      </FormProvider>

      <Dialog
        open={Boolean(productId && error)}
        onOpenChange={redirectToDashboard}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Error</DialogTitle>
            <DialogDescription>{error?.message}</DialogDescription>
          </DialogHeader>

          <DialogFooter className="sm:justify-end">
            <DialogClose asChild>
              <Button type="button" variant="secondary" size="xs">
                Dashboard
              </Button>
            </DialogClose>

            <Button type="button" size="xs" onClick={redirectToNewBundle}>
              New Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
