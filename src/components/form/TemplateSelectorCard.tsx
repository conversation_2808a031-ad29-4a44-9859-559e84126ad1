import { Checkbox } from '@/components/ui/checkbox'
import MediaBox from '@/components/common/MediaBox'
import PlatformWidget from '@/components/products/PlatformWidget'

type Props = {
  id: string
  title: string
  platform: string
  checked?: boolean
  media: any
  onClick?: () => void
}

export default function TemplateSelectorCard({
  id,
  title,
  platform,
  checked = false,
  media,
  onClick,
}: Props) {
  return (
    <div
      className="rounded-lg border bg-card text-card-foreground shadow-sm hover:border-blue-200 transition-colors cursor-pointer"
      onClick={onClick}
    >
      <div className="p-3 flex items-center space-x-3">
        <Checkbox
          type="button"
          role="checkbox"
          aria-checked={checked}
          data-state={checked ? 'checked' : 'unchecked'}
          value="on"
          className="peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
          id={id}
        />

        <div className="w-12 h-12 bg-gray-100 rounded overflow-hidden flex-shrink-0">
          <MediaBox
            className="w-full h-full object-cover"
            width={48}
            height={48}
            media={media}
            ratio={1 / 1}
          />
        </div>

        <div className="font-medium text-sm">
          <h3>{title}</h3>
          <PlatformWidget platform={platform} />
        </div>
      </div>
    </div>
  )
}
