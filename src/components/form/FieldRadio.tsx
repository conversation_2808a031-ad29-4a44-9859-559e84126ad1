import React from 'react'
import { useFormContext } from 'react-hook-form'

import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'

export default function FieldRadio({
  className = '',
  name,
  options = [],
  required = false,
  instructions = '',
  label = '',
  error = '',
}: any) {
  const {
    watch,
    setValue,
    formState: { errors },
  } = useFormContext()

  const cols = React.useMemo(() => {
    if (options?.length % 4 === 0) return 'grid-cols-4'
    if (options?.length % 3 === 0) return 'grid-cols-3'
    if (options?.length % 2 === 0) return 'grid-cols-2'
    return 'grid-cols-1'
  }, [options])

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setValue(name, e.target.value, { shouldDirty: true, shouldTouch: true })
  }

  return (
    <div className={cn('flex flex-col gap-2 w-full mb-2', className)}>
      <div className="flex flex-col justify-between w-full text-gray-800 text-sm">
        <Label className="block mb-1 text-sm">
          {label}
          {required ? (
            <span className="text-red-500">{'\u00A0'}*</span>
          ) : null}
        </Label>
        <p className="text-xs text-gray-500">{instructions}</p>
      </div>

      <div className={cn(`w-full grid gap-6`, cols)}>
        {options?.map((option: any) => (
          <label
            key={option.value}
            className="has-[:checked]:border-primary w-full flex items-center justify-center px-4 py-6 border-[1px] border-gray-200 rounded-md outline-none has-[:focus]:outline-none has-[:focus]:border-gray-400 cursor-pointer shadow-sm"
          >
            <input
              key={option.value}
              type="radio"
              value={option.value}
              className="hidden"
              disabled={option.disabled}
              onChange={onChange}
              checked={watch(name) === option.value}
            />
            {option.label}
          </label>
        ))}
      </div>

      <div className="min-h-1">
        {errors?.[name]?.message ? (
          <small className="text-red-500">{String(errors[name].message)}</small>
        ) : null}
      </div>
    </div>
  )
}
