import { InputHTMLAttributes } from 'react'
import { useFormContext } from 'react-hook-form'

interface InputFieldProps extends InputHTMLAttributes<HTMLInputElement> {
  label: string
  name: string
  required?: boolean
}

export function InputField({
  label,
  name,
  required,
  ...props
}: InputFieldProps) {
  const {
    register,
    formState: { errors },
  } = useFormContext()

  return (
    <div>
      <label htmlFor={name} className="text-sm font-medium text-gray-900">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <input
        id={name}
        {...register(name, { required })}
        {...props}
        className="flex w-full rounded-md px-3 py-2 h-12 bg-gray-50 border-0 text-base placeholder:text-gray-400 focus-visible:ring-2 focus-visible:ring-black focus:outline-none mt-1 shadow-sm"
      />
      {errors[name] && (
        <p className="text-sm text-red-500 mt-1">
          {(errors[name] as any).message || 'This field is required'}
        </p>
      )}
    </div>
  )
}
