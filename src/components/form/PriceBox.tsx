import { useEffect, useMemo } from 'react'
import { useFormContext } from 'react-hook-form'

import { PlatformsEnum } from '@/actions/product'
import FieldBox from '@/components/form/FieldBox'
import FieldText from '@/components/form/FieldText'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'

export default function PriceBox() {
  const { trigger, watch, setValue } = useFormContext()

  const isFree = watch('isFree')
  useEffect(() => {
    if (isFree) {
      setValue('price', 0)
    }
  }, [isFree, watch, setValue])

  const onChangeIsFree = (value: boolean) => {
    setValue('isFree', value, { shouldDirty: true, shouldValidate: true })
  }

  const platform = watch('platform')
  const freeLabel = useMemo(() => {
    if (platform === PlatformsEnum.BUNDLE) {
      return 'Free Bundle'
    }

    if (platform === PlatformsEnum.SUBSCRIPTION) {
      return 'Free Subscription'
    }

    return 'Free item'
  }, [platform])

  const assets = watch('platforms.bundle.assets')

  return (
    <FieldBox>
      <FieldText
        name="price"
        label="Price"
        instructions="Price for your template, please specify when it's free."
        prefix={
          <span className="flex items-center text-gray-500 h-full text-sm">
            $
          </span>
        }
        required
        inputClassName="w-32"
        disabled={isFree}
        suffix={
          <div className="flex flex-shrink items-center gap-2 text-nowrap self-end">
            <Switch onCheckedChange={onChangeIsFree} checked={isFree} />
            <Label htmlFor="isFree" className="text-xs cursor-pointer">
              {freeLabel}
            </Label>
          </div>
        }
      />
    </FieldBox>
  )
}
