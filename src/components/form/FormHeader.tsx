import Link from 'next/link'
import { X } from 'lucide-react'

import { But<PERSON> } from '@/components/ui/button'

type Props = React.PropsWithChildren<
  PropsWithClassName<{
    link?: string
    title?: React.ReactNode
  }>
>

export default function FormHeader({ children, link, title }: Props) {
  return (
    <header className="sticky top-0 z-20 w-full border-b bg-background">
      <div className="flex h-14 md:h-16 items-center justify-between w-full px-2 md:px-6">
        <Button type="button" variant="ghost" asChild>
          <Link href={link ?? '/'}>
            <X className="h-4 w-4 md:h-5 md:w-5" />
          </Link>
        </Button>

        <div className="flex items-center">
          <h1 className="flex items-center font-medium text-xs md:text-sm">
            {title}
          </h1>
        </div>

        <div className="flex items-center gap-2 md:gap-3">{children}</div>
      </div>
    </header>
  )
}
