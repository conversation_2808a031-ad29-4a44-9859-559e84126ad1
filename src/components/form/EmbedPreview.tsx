import { useFormContext } from 'react-hook-form'

import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'

export default function EmbedPreview() {
  const { watch, setValue } = useFormContext()

  const platform = watch('platform')

  const platformFormId = `platforms.${platform}`

  const hasEmbedPreview =
    platform === 'framer' ||
    platform === 'vzero' ||
    platform === 'loveable' ||
    platform === 'bolt'

  return hasEmbedPreview ? (
    <div className="flex flex-col gap-2">
      <h3 className="font-medium text-sm">Embed Preview</h3>
      <div className="flex items-center gap-2">
        <Switch
          id={`${platformFormId}.embedPreview`}
          checked={watch(`${platformFormId}.embedPreview`)}
          onCheckedChange={value => {
            setValue(`${platformFormId}.embedPreview`, value, {
              shouldDirty: true,
              shouldTouch: true,
            })
          }}
        />
        <Label
          htmlFor={`${platformFormId}.embedPreview`}
          className="text-xs cursor-pointer"
        >
          Embed preview
        </Label>
      </div>
    </div>
  ) : null
}
