import { useFormContext } from 'react-hook-form'

import FieldBox from '@/components/form/FieldBox'
import FieldText from '@/components/form/FieldText'
import { Badge } from '@/components/ui/badge'

type Props = {
  fieldName: string
}

const features = [
  'Responsive',
  'Dark Mode',
  'Animation',
  'Forms',
  'CMS',
  'SEO Optimized',
]

export default function FeaturesSelection({ fieldName }: Props) {
  const { watch, setValue } = useFormContext()

  return (
    <FieldBox>
      <FieldText
        label="Features"
        name={fieldName}
        instructions={
          <div className="flex items-center text-xs text-gray-500 mb-2">
            List the features you&apos;ve used in your template, comma
            separated. &nbsp;
            <small className="flex-1 text-xxs text-right">
              {watch(fieldName)?.length || 0}/8
            </small>
          </div>
        }
        inputClassName="w-[32rem]"
        required
        onChange={e => {
          const value = e.target.value
          const splitted = value.split(',')
          // console.log('splitted', splitted.length)
          if (splitted.length < 8) {
            setValue(fieldName, splitted, {
              shouldDirty: true,
              shouldTouch: true,
            })
          }
        }}
        suffix={
          <div className="flex w-full flex-grow flex-col">
            <small className="text-[0.6rem] text-gray-500 mb-2">
              Suggested features:
            </small>

            <div className="flex flex-wrap gap-2">
              {features
                .filter(
                  item =>
                    !watch(fieldName).some((feature: string) =>
                      feature.toLowerCase().includes(item.toLowerCase()),
                    ),
                )
                .map(feature => (
                  <Badge
                    key={feature}
                    variant="secondary"
                    className="cursor-pointer"
                    size="sm"
                    onClick={() => {
                      setValue(fieldName, [...watch(fieldName), feature])
                    }}
                  >
                    {feature}
                  </Badge>
                ))}
            </div>
          </div>
        }
      />
    </FieldBox>
  )
}
