import { formatDistanceToNow } from 'date-fns'
import { Alert<PERSON>ircle, CheckCircle2, HelpCircle, Loader2 } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useFormContext } from 'react-hook-form'

import { TransferReceiveStatus } from '@/actions/transfer'
import ClippboardClient from '@/components/common/ClippboardClient'
import CodeBox from '@/components/common/CodeBox'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import useTransfer from '@/hooks/use-transfer'
import { cn } from '@/lib/utils'

export default function WebflowSendTemplate({ productId }) {
  const [open, setOpen] = useState(false)
  const {
    setValue,
    watch,
    getValues,
    formState: { errors },
  } = useFormContext()

  const webflowAuto = watch('platforms.webflow.auto')

  const {
    data: transfer,
    error,
    isLoading,
  } = useTransfer(productId, webflowAuto)

  const handleChangeWebflowAuto = checked => {
    setValue('platforms.webflow.auto', checked, {
      shouldDirty: true,
      shouldTouch: true,
    })
  }

  useEffect(() => {
    if (webflowAuto === undefined) {
      setValue('platforms.webflow.auto', true)
    }
  }, [webflowAuto, setValue])

  useEffect(() => {
    if (transfer?.incomingId) {
      setValue('platforms.webflow.distributionId', transfer?.incomingId, {
        shouldDirty: false,
        shouldTouch: false,
      })
    }

    if (transfer?.status !== undefined) {
      setValue('platforms.webflow.distributionStatus', transfer.status, {
        shouldDirty: false,
        shouldTouch: false,
      })
    }
  }, [transfer, setValue])

  const webflowDistributionId = watch('platforms.webflow.distributionId')

  const updated = transfer?.updated
    ? formatDistanceToNow(transfer?.updated, { addSuffix: true })
    : '-'

  // console.log('getValues', getValues())
  // console.log('errors', errors)

  return (
    <>
      <div className="grid gap-1">
        <div className="flex items-center justify-between">
          <div>
            <Label>Distribution Option</Label>
            <div className="text-xs text-gray-500 mb-2">
              Choose how you want to distribute your template, this is an
              exclusive feature currently only for Webflow templates.
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Label htmlFor="webflowAuto">
              {webflowAuto ? 'Automatic' : 'Manual'} distribution
            </Label>
            <Switch
              id="webflowAuto"
              checked={webflowAuto}
              onCheckedChange={handleChangeWebflowAuto}
            />
          </div>
        </div>

        <Card
          className={cn(
            'relative',
            !webflowAuto && 'opacity-50 pointer-events-none',
          )}
        >
          <CardContent className="grid gap-4 p-6">
            <div className="flex flex-col gap-2">
              <div className="grid gap-1">
                <p className="text-xs text-muted-foreground">
                  Duplicate your Webflow template and rename it to:
                </p>

                <div className="flex items-center gap-2">
                  <CodeBox className="text-xs">
                    {webflowDistributionId || 'Waiting for generation...'}
                  </CodeBox>

                  <ClippboardClient
                    valueToCopy={webflowDistributionId ?? transfer?.incomingId}
                  />
                </div>
              </div>

              <Separator className="my-1" />

              {(transfer?.status === TransferReceiveStatus.INITIAL ||
                transfer?.status === TransferReceiveStatus.PENDING) && (
                <div className="flex flex-col items-start gap-2">
                  <p className="text-xs text-muted-foreground">
                    Send your template to:
                  </p>

                  <div className="flex items-center gap-2">
                    <CodeBox className="text-xs"><EMAIL></CodeBox>
                    <ClippboardClient valueToCopy="<EMAIL>" />
                  </div>
                </div>
              )}

              <Separator className="my-1" />

              <div className="flex flex-col items-start gap-2">
                <div className="flex items-center justify-between gap-2 w-full">
                  <span className="text-xs text-muted-foreground">
                    Your template status will appear here:
                  </span>

                  <span className="text-xs text-muted-foreground">
                    Transfer update: {updated}
                  </span>
                </div>

                <div className="flex items-center justify-between w-full gap-2">
                  <div className="flex items-center gap-2">
                    {(transfer?.status === TransferReceiveStatus.INITIAL ||
                      transfer?.status === TransferReceiveStatus.PENDING) && (
                      <>
                        <AlertCircle className="h-4 w-4 text-yellow-500" />
                        <span className="text-sm">
                          Waiting for template submission...
                        </span>
                      </>
                    )}

                    {transfer?.status === TransferReceiveStatus.PROGRESS && (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="text-sm">Transfering template...</span>
                      </>
                    )}

                    {transfer?.status === TransferReceiveStatus.COMPLETE && (
                      <>
                        <CheckCircle2 className="h-4 w-4 text-green-500" />
                        <span className="text-sm">
                          Template received successfully!
                        </span>
                      </>
                    )}

                    {transfer?.status === TransferReceiveStatus.CANCELED && (
                      <>
                        <AlertCircle className="h-4 w-4 text-red-500" />
                        <span className="text-sm text-red-500">{error}</span>
                      </>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    {isLoading && (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="text-sm">updating...</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* {transfer?.status === TransferReceiveStatus.INITIAL &&
              distributionId && (
                <div className="flex items-center gap-2 text-blue-500">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-sm">
                    Please apply the ID {distributionId} to your Webflow
                    template and submit it.
                  </span>
                </div>
              )} */}

            <Dialog>
              <DialogTrigger asChild>
                <Button
                  variant="ghost"
                  size="xs"
                  className="absolute right-1 top-1 gap-1"
                >
                  <HelpCircle className="h-3 w-3" />
                  How it works
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>How to submit your Webflow template</DialogTitle>
                  <DialogDescription>
                    Follow these steps to submit your template:
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">
                      1. Copy the Template ID
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      Copy the Distribution ID in the box.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">
                      2. Duplicate your template
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      Duplicate your Webflow template and rename it exactly to
                      the given Distribution ID.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">
                      3. Wait for confirmation
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      Once we receive your template, the status will update
                      automatically.
                    </p>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </CardContent>
        </Card>
      </div>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Disclaimer</DialogTitle>
            <DialogDescription>
              About your template distribution, please note the following:
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">
                1. Choose the distribution method
              </h4>
              <p className="text-sm text-muted-foreground">
                Select &quot;Manual Distribution&quot; to manually send the
                template to each buyer. Select &quot;Automatic
                Distribution&quot; for our system to send the template at no
                additional cost.
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">2. Notifications</h4>
              <p className="text-sm text-muted-foreground">
                You will be notified if there are any changes to the automatic
                submission.
              </p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
