import { TextareaHTMLAttributes } from 'react'
import { useFormContext } from 'react-hook-form'

interface TextareaFieldProps
  extends TextareaHTMLAttributes<HTMLTextAreaElement> {
  label: string
  name: string
  maxLength?: number
}

export function TextareaField({
  label,
  name,
  maxLength,
  ...props
}: TextareaFieldProps) {
  const { register, watch } = useFormContext()
  const value = watch(name) || ''

  return (
    <div className="relative">
      <label htmlFor={name} className="text-sm font-medium text-gray-900">
        {label}
      </label>
      <textarea
        id={name}
        maxLength={maxLength}
        {...register(name)}
        {...props}
        className="flex min-h-[80px] w-full rounded-md px-3 py-2 text-sm mt-1 bg-gray-50 border-0 resize-none placeholder:text-gray-400 focus-visible:ring-2 focus-visible:ring-black outline-none"
      />
      {typeof maxLength === 'number' && (
        <span className="absolute right-2 bottom-2 text-xs text-gray-400 select-none">
          {value.length}/{maxLength}
        </span>
      )}
    </div>
  )
}
