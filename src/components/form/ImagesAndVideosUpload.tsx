import {
  closest<PERSON>enter,
  DndContext,
  Drag<PERSON>nd<PERSON><PERSON>,
  Mouse<PERSON><PERSON>or,
  useSensor,
  useSensors,
} from '@dnd-kit/core'
import { SortableContext } from '@dnd-kit/sortable'
import { ArrowUp, Image as ImageSvg, Trash2, Video } from 'lucide-react'
import React, { MouseEvent } from 'react'
import { useFormContext } from 'react-hook-form'

import { File as FileRecord } from '@/actions/file'
import DropUpload from '@/components/common/DropUpload'
import FormSubheader from '@/components/form/FormSubheader'
import { Button } from '@/components/ui/button'
import { SortableItem } from '@/components/common/SortableItem'

type Props = {
  title?: string
  subtitle?: string
}

function shouldHandleEvent(element: HTMLElement | null): boolean {
  let current = element
  while (current) {
    if (current.dataset && current.dataset.noDnd !== undefined) {
      return false
    }
    current = current.parentElement
  }
  return true
}

export class CustomMouseSensor extends MouseSensor {
  static activators = [
    {
      eventName: 'onMouseDown' as const,
      handler: ({ nativeEvent: event }: MouseEvent) => {
        // console.log('onMouseDown', event)
        return shouldHandleEvent(event.target as HTMLElement)
      },
    },
  ]
}

/**
 *
 */
export default function ImagesAndVideosUpload({
  title = 'Visuals',
  subtitle = 'Add at least two images that showcase your template. You can also upload videos in MP4, MPEG, or OGG formats (max 6MB).',
}: Props) {
  const sensors = useSensors(useSensor(CustomMouseSensor))

  const fieldName = 'imagesAndVideos'
  const {
    watch,
    setValue,
    formState: { errors },
  } = useFormContext()

  const fileSet = watch(fieldName)

  const handleSetCover = (file: FileRecord) => (e: React.MouseEvent) => {
    e.stopPropagation()
    e.preventDefault()

    const newCover = fileSet.find((item: FileRecord) => item.id === file.id)
    const nextFileSet = fileSet.filter(
      (item: FileRecord) => item.id !== newCover?.id,
    )
    nextFileSet.unshift(newCover)

    setValue(fieldName, nextFileSet, {
      shouldDirty: true,
      shouldTouch: true,
    })
  }

  const handleDelete = (file: FileRecord) => (e: React.MouseEvent) => {
    e.stopPropagation()
    e.preventDefault()

    const nextFileSet = fileSet.filter(
      (item: FileRecord) => item.id !== file.id,
    )

    setValue(fieldName, nextFileSet, {
      shouldDirty: true,
      shouldTouch: true,
    })
  }

  const handleUpload = (givenFiles: FileRecord[]) => {
    const files = [
      ...new Map<string, FileRecord>(
        [...(fileSet ?? []), ...givenFiles].map(item => {
          const nextItem = {
            ...item,
            tags: [
              ...(item.mimeType?.startsWith('image/') ? ['image'] : []),
              ...(item.mimeType?.startsWith('video/') ? ['video'] : []),
            ],
          }

          return [item.id.toString(), nextItem]
        }),
      ).values(),
    ]

    if (files.length > 12) {
      alert('You reach the upload limit')
      return
    }

    setValue(fieldName, files, { shouldDirty: true, shouldTouch: true })
  }

  const reorder = (
    list: FileRecord[],
    startId: string,
    endId: string,
  ): FileRecord[] => {
    const startIndex = list.findIndex(item => item.id === startId)
    const endIndex = list.findIndex(item => item.id === endId)
    const [removed] = list.splice(startIndex, 1)
    list.splice(endIndex, 0, removed)
    return list
  }

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event
    if (active.id !== over?.id) {
      const nextFileSet = reorder(
        fileSet,
        active.id.toString(),
        over?.id?.toString() ?? '',
      )

      setValue(fieldName, nextFileSet, {
        shouldDirty: true,
        shouldTouch: true,
      })
    }
  }

  return (
    <>
      <FormSubheader
        title={title}
        description={
          subtitle ? (
            <p className="text-xs text-foreground/80 mb-1 max-w-96">
              {subtitle}
            </p>
          ) : null
        }
      />

      <DropUpload
        onUpload={handleUpload}
        folder="videos"
        className="w-full min-h-min rounded-md overflow-hidden cursor-pointer"
        classNameUpload="w-12 h-12 cursor-pointer text-foreground/80"
        dropzoneOptions={{
          maxFiles: 12,
          multiple: true,
          accept: {
            'video/mp4': ['.mp4'],
            'image/webp': ['.webp'],
            'image/jpeg': ['.jpg', '.jpeg'],
            'image/png': ['.png'],
          },
        }}
      >
        <div className="grid gap-6">
          {fileSet?.length > 0 ? (
            <div className="aspect-[4/3] bg-gray-100 rounded-lg overflow-hidden relative">
              {fileSet[0]?.url &&
                (fileSet[0].mimeType?.startsWith('video') ? (
                  <video
                    src={fileSet[0].url}
                    autoPlay
                    loop
                    muted
                    playsInline
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <img
                    src={fileSet[0].url}
                    alt="cover"
                    className="w-full h-full object-cover"
                  />
                ))}
              <Button
                variant="secondary"
                className="absolute top-2 right-2"
                onClick={handleDelete(fileSet[0])}
              >
                <Trash2 className="!w-4 !h-4" />
              </Button>
            </div>
          ) : (
            <div className="aspect-[4/3] bg-secondary rounded-lg grid place-content-center border-2 border-dashed border-foreground/20">
              <div className="flex flex-col gap-2 items-center text-center">
                <div className="flex items-center justify-center gap-2">
                  <ImageSvg className="w-12 h-12 text-foreground/20 dark:text-foreground/80" />
                  <Video className="w-12 h-12 text-foreground/20 dark:text-foreground/80" />
                </div>

                <p className="text-sm text-foreground/80 dark:text-foreground/40">
                  Drag an image or video to upload
                  <br />
                  <small>(4:3 ratio recommended)</small>
                </p>

                <Button variant="outline" size="sm">
                  Select Files
                </Button>
              </div>
            </div>
          )}
        </div>
      </DropUpload>

      {fileSet?.length > 0 && (
        <div className="grid grid-cols-5 gap-3">
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={fileSet?.slice(1).map((item: FileRecord) => item.id)}
            >
              {fileSet?.slice(1).map((item: FileRecord) => (
                <SortableItem key={item.id} id={item.id}>
                  <div
                    key={item.url}
                    className="aspect-[4/3] bg-foreground/10 rounded-lg overflow-hidden relative"
                  >
                    {item.mimeType?.startsWith('video') ? (
                      <video
                        src={item.url}
                        autoPlay
                        loop
                        muted
                        playsInline
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <img
                        src={item.url}
                        alt="image"
                        className="w-full h-full object-cover pointer-events-none"
                      />
                    )}

                    <div className="absolute top-2 right-2 w-min h-min flex items-center justify-center gap-2">
                      <Button
                        variant="secondary"
                        onClick={handleSetCover(item)}
                        className="w-6 h-6"
                        data-no-dnd
                      >
                        <ArrowUp className="!w-3 !h-3" />
                      </Button>

                      <Button
                        variant="secondary"
                        onClick={handleDelete(item)}
                        className="w-6 h-6"
                        data-no-dnd
                      >
                        <Trash2 className="!w-3 !h-3" />
                      </Button>
                    </div>
                  </div>
                </SortableItem>
              ))}
            </SortableContext>

            {/* {fileSet?.length < 12 && (
              <Button
                variant="outline"
                className="w-full h-full aspect-[4/3] border-2 border-dashed border-foreground/20 hover:border-foreground/50 transition-colors grid place-content-center"
              >
                <Plus
                  className="!w-6 !h-6 text-foreground/20 dark:text-foreground/80"
                  strokeWidth={1}
                />
              </Button>
            )} */}
          </DndContext>
        </div>
      )}

      {Boolean(errors?.[fieldName]?.message) && (
        <div className="text-xs text-red-600 mt-6">
          {String(errors?.[fieldName]?.message)}
        </div>
      )}
    </>
  )
}
