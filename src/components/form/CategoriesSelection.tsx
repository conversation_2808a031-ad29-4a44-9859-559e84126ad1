import { useFormContext } from 'react-hook-form'

import FieldBox from '@/components/form/FieldBox'
import FieldText from '@/components/form/FieldText'
import { Badge } from '@/components/ui/badge'

type Props = {
  fieldName: string
}

const suggestedCategories = [
  'Design',
  'Portfolio',
  'Business',
  'E-commerce',
  'Blog',
  'Landing Page',
]

export default function CategoriesSelection({ fieldName }: Props) {
  const { trigger, watch, setValue } = useFormContext()

  const clickCategoryHandler = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    const splitted = value.split(',')
    if (splitted.length < 3) {
      setValue(fieldName, splitted, {
        shouldDirty: true,
        shouldTouch: true,
      })
      // trigger(fieldName)
    }
  }

  return (
    <FieldBox>
      <FieldText
        label="Categories"
        name={fieldName}
        instructions={
          <div className="flex items-center text-xs text-gray-500 mb-2">
            Let your template be in up to three categories. Comma separated
            &nbsp;
            <small className="flex-1 text-xxs text-right">
              {watch(fieldName)?.length || 0}/3
            </small>
          </div>
        }
        inputClassName="w-[32rem]"
        required
        onChange={clickCategoryHandler}
        suffix={
          <div className="flex w-full flex-grow flex-col">
            <small className="text-[0.6rem] text-gray-500 mb-2">
              Suggested categories:
            </small>

            <div className="flex flex-wrap gap-2">
              {suggestedCategories
                .filter(
                  item =>
                    !watch(fieldName).some((formCategory: string) =>
                      formCategory.toLowerCase().includes(item.toLowerCase()),
                    ),
                )
                .map(category => (
                  <Badge
                    key={category}
                    variant="light"
                    className="cursor-pointer"
                    size="sm"
                    onClick={() => {
                      setValue(fieldName, [...watch(fieldName), category])
                    }}
                  >
                    {category}
                  </Badge>
                ))}
            </div>
          </div>
        }
      />
    </FieldBox>
  )
}
