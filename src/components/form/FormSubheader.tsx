import { cn } from '@/lib/utils'

type Props = PropsWithClassName<{
  title: string
  description?: React.ReactNode
}>

export default function FormSubheader({
  className,
  title,
  description,
}: Props) {
  return (
    <hgroup className={cn('mb-2', className)}>
      <h2 className={cn('font-medium text-xl md:text-2xl')}>{title}</h2>
      {Boolean(description) && (
        <div className="text-sm text-gray-500 ">{description}</div>
      )}
    </hgroup>
  )
}
