import { Upload } from 'lucide-react'
import { useFormContext } from 'react-hook-form'
import { FaFigma } from 'react-icons/fa'

import { File as FileRecord } from '@/actions/file'
import DropUpload from '@/components/common/DropUpload'
import FieldBox from '@/components/form/FieldBox'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { cn } from '@/lib/utils'

type Props = {
  swtichFieldName: string
  fileFieldName: string
}

/**
 *
 */
export default function FormAddTemplateFigmaUpload({
  swtichFieldName,
  fileFieldName,
}: Props) {
  const {
    watch,
    setValue,
    formState: { errors },
  } = useFormContext()

  const handleUpload = (givenFiles: FileRecord[]) => {
    if (givenFiles?.[0]) {
      setValue(fileFieldName, givenFiles[0], {
        shouldDirty: true,
        shouldTouch: true,
      })
    }
  }

  const currentFile = watch(fileFieldName)
  const shouldShowFigmaUpload = Boolean(watch(swtichFieldName))

  return (
    <FieldBox>
      <h3 className="font-medium text-sm mb-1">Figma Upload</h3>
      <div className="flex items-center space-x-2 mb-3">
        <Switch
          id="includeFigma"
          checked={watch(swtichFieldName)}
          onCheckedChange={value => {
            setValue(swtichFieldName, value, {
              shouldDirty: true,
              shouldTouch: true,
            })
          }}
        />
        <Label htmlFor="includeFigma" className="text-xs cursor-pointer">
          Include Figma file
        </Label>
      </div>

      <DropUpload
        onUpload={handleUpload}
        folder="figma"
        className={cn(!shouldShowFigmaUpload && 'hidden')}
        flair={['figma']}
        dropzoneOptions={{
          maxFiles: 1,
          multiple: false,
          accept: {
            'application/octet-stream': ['.fig'],
            'application/zip': ['.zip'],
          },
        }}
      >
        <div className="bg-gray-100 p-6 rounded-lg grid place-content-center border-2 border-dashed border-gray-300 mb-2">
          <div className="flex flex-col gap-2 items-center text-center text-gray-500 text-sm">
            {currentFile === undefined ? (
              <>
                <Upload className="w-12 h-12 text-gray-400" />
                <span>Drag & drop your Figma file here (.fig and .zip)</span>
                <small>(or click to browse)</small>
              </>
            ) : (
              <>
                <FaFigma className="w-12 h-12 text-gray-400" />
                <span>{currentFile.metadata.name}</span>
                <small>(click to change)</small>
              </>
            )}
          </div>
        </div>
      </DropUpload>
    </FieldBox>
  )
}
