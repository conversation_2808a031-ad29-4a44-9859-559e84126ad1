'use client'

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table'

import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button'
import PlatformWidget from '@/components/products/PlatformWidget'
import { useFieldArray, useFormContext } from 'react-hook-form'
import { Product } from '@/actions/product'
import { Badge } from '@/components/ui/badge'
import { X } from 'lucide-react'

// Product has assetId now because id is swapped by useFieldArray
type ProductWithAssetId = Product & { assetId: string }

export default function MultiAssetsSelection({
  assets,
  fieldName,
  children,
}: any) {
  const {
    control,
    watch,
    formState: { errors },
  } = useFormContext()
  const { fields, append, remove } = useFieldArray<any>({
    control,
    name: fieldName,
  })

  const selectHandler = (asset: ProductWithAssetId) => () => {
    const exists = fields.findIndex((item: any) => item.assetId === asset.id)
    if (exists > -1) {
      remove(exists)
    } else {
      append({ ...asset, assetId: asset.id })
    }
  }

  const columns: ColumnDef<any>[] = [
    {
      accessorKey: 'selected',
      header: '-',
      cell: ({ row }) => (
        <Checkbox
          role="button"
          className="w-6 h-6"
          checked={
            !!fields.find((item: any) => item.assetId === row.original.id)
          }
          onClick={selectHandler(row.original)}
        />
      ),
    },
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'children',
      header: 'Platform',
      cell: ({ row }) => (
        <div className="w-[8rem]">
          <PlatformWidget platform={row.original.children.kind} />
        </div>
      ),
    },
  ]

  const table = useReactTable({
    data: assets,
    columns,
    getCoreRowModel: getCoreRowModel(),
  })

  const badges = watch('assets')

  return (
    <div className="w-full flex flex-col gap-4 items-start justify-start">
      {errors?.[fieldName]?.message && (
        <p className="text-red-500 text-sm">
          {String(errors[fieldName].message)}
        </p>
      )}
      <div className="flex flex-row flex-wrap gap-4">
        {badges.map((item: ProductWithAssetId) => (
          <Badge
            key={item.id}
            size="lg"
            variant="purple"
            className="cursor-pointer"
            onClick={selectHandler(item)}
          >
            {item.name}
            <X className="w-4 h-4" />
          </Badge>
        ))}
      </div>

      <Dialog>
        <DialogTrigger className="sm:justify-start" asChild>
          <Button type="button" variant="secondary" size="lg">
            Attach Assets
          </Button>
        </DialogTrigger>

        <DialogContent className="!max-w-max">
          <DialogHeader>
            <DialogTitle>Attach Assets</DialogTitle>

            <DialogDescription>
              Select the assets you want to attach
            </DialogDescription>
          </DialogHeader>

          <div className="h-80 overflow-y-auto">
            <Table className="w-[50rem]">
              <TableHeader>
                {table.getHeaderGroups().map(headerGroup => (
                  <TableRow key={headerGroup.id} className="h-8 text-xs">
                    {headerGroup.headers.map(header => {
                      return (
                        <TableHead key={header.id}>
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext(),
                              )}
                        </TableHead>
                      )
                    })}
                  </TableRow>
                ))}
              </TableHeader>

              <TableBody>
                {table.getRowModel().rows.map(row => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                  >
                    {row.getVisibleCells().map(cell => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          <DialogFooter className="sm:justify-end">
            <DialogClose asChild>
              <Button type="button" variant="secondary" size="lg">
                Done
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
