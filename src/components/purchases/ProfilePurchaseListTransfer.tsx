'use client'

import { PlatformsEnum, ProductChildrenKind } from '@/actions/product'

import RevealFramerLink from '@/components/common/RevealFramerLink'
import TransferStatusControl from '@/components/internal/TransferStatusControl'
import BundleMultiTransfer from '@/components/purchases/BundleMultiTransfer'
import { getPlatform } from '@/utils/get-platform'

export default function ProfilePurchaseListTransfer({ purchase }: any) {
  const platform = getPlatform(purchase?.product?.platforms)

  return (
    <div>
      {platform === PlatformsEnum.WEBFLOW && purchase.transfers?.[0]?.id ? (
        <TransferStatusControl transferId={purchase.transfers[0].id} />
      ) : null}

      {platform === PlatformsEnum.FRAMER && purchase.transfers?.[0]?.id ? (
        <RevealFramerLink
          transferId={purchase.transfers[0].id}
          remixUrl={purchase?.product?.platforms?.framer?.remixUrl}
        />
      ) : null}
    </div>
  )
}
