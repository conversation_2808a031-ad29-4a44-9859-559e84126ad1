'use client'

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table'

import { PlatformsEnum } from '@/actions/product'
import MediaBox from '@/components/common/MediaBox'
import RevealFramerLink from '@/components/common/RevealFramerLink'
import TransferStatusControl from '@/components/internal/TransferStatusControl'
import PlatformWidget from '@/components/products/PlatformWidget'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { getPlatform } from '@/utils/get-platform'
import DownloadFigmaFile from './DownloadFigmaFile'
import { PaymentLog } from '@/actions/payment'

export default function BundleMultiTransfer({ assets, transfers }: any) {
  const columns: ColumnDef<any>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
      cell: ({ row }) => {
        const media = row.original?.fileSet?.files?.find(
          (file: any) =>
            file.mimeType?.startsWith('video/') ||
            file.mimeType?.startsWith('image/'),
        )

        return (
          <div className="flex flex-row items-center w-auto gap-6 min-w-[7rem]">
            {media ? (
              <div className="h-8 aspect-video">
                <MediaBox media={media} width={64} height={36} ratio={16 / 9} />
              </div>
            ) : null}
            <div className="w-full">{row?.original?.name}</div>
          </div>
        )
      },
    },
    {
      accessorKey: 'platforms',
      header: 'Platform',
      cell: ({ row }) => (
        <div className="w-[6rem]">
          <PlatformWidget platform={getPlatform(row?.original?.platforms)} />
        </div>
      ),
    },
    {
      accessorKey: 'figma',
      header: 'Extras',
      cell: ({ row }) => {
        // console.log(row.original)

        return (
          <div className="w-[6rem]">
            <DownloadFigmaFile product={row.original} />
          </div>
        )
      },
    },
    {
      accessorKey: 'transfer',
      header: 'Transfer',
      cell: ({ row }) => {
        const transfer = transfers.find(
          (transfer: any) => transfer.product === row?.original?.id,
        )

        return transfer ? (
          <div className="w-[12rem]">
            {getPlatform(row?.original?.platforms) === PlatformsEnum.FRAMER ? (
              <RevealFramerLink
                transferId={transfer?.id}
                remixUrl={row?.original?.platforms?.framer?.remixUrl}
              />
            ) : null}

            {getPlatform(row.original.platforms) === PlatformsEnum.WEBFLOW ? (
              <TransferStatusControl transferId={transfer.id} />
            ) : null}
          </div>
        ) : null
      },
    },
  ]

  const table = useReactTable({
    data: assets,
    columns,
    getCoreRowModel: getCoreRowModel(),
  })

  return (
    <Table className="w-full border-b-2 border-muted-foreground/20">
      <TableHeader className="h-4 text-xs">
        {table.getHeaderGroups().map(headerGroup => (
          <TableRow key={headerGroup.id}>
            {headerGroup.headers.map(header => {
              return (
                <TableHead
                  key={header.id}
                  className="h-4 text-[0.625rem] py-2 border-b-[1px] border-muted-foreground/10"
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                </TableHead>
              )
            })}
          </TableRow>
        ))}
      </TableHeader>

      <TableBody>
        {table.getRowModel().rows.map(row => (
          <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
            {row.getVisibleCells().map(cell => (
              <TableCell key={cell.id}>
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </TableCell>
            ))}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
