import { InfiniteData } from '@tanstack/react-query'
import { format } from 'date-fns'
import { ChevronDown, ChevronUp } from 'lucide-react'
import Link from 'next/link'
import React, { useState } from 'react'

import { Purchase } from '@/actions/payment'
import MediaBox from '@/components/common/MediaBox'
import BundleMultiTransfer from '@/components/purchases/BundleMultiTransfer'
import PurchaseActionsMenu from '@/components/purchases/PurchaseActionsMenu'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import centsToDollars from '@/utils/cents-to-dollars'

type Props = {
  purchases?: InfiniteData<Purchase[] | undefined, unknown>
}

export default function PurchaseTableBundle({ purchases }: Props) {
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({})

  const toggleRowExpansion = (purchaseId: string) => {
    setExpandedRows(prev => ({
      ...prev,
      [purchaseId]: !prev[purchaseId],
    }))
  }

  return (
    <Table className="w-full">
      <TableHeader>
        <TableRow>
          <TableHead>Thumb/Name</TableHead>
          <TableHead>Price</TableHead>
          {/* <TableHead>Creator</TableHead> */}
          <TableHead>Date</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>

      <TableBody>
        {purchases?.pages?.map(page =>
          page?.map((purchase: Purchase) => {
            const metadata = purchase?.metadata as Record<string, string>
            const isExpanded = expandedRows[purchase.id.toString()] || false

            const media =
              purchase?.product?.fileSet?.files?.find(
                file =>
                  file.mimeType?.startsWith('video/') ||
                  file.mimeType?.startsWith('image/'),
              ) ?? {}

            return (
              <React.Fragment key={purchase.id.toString()}>
                <TableRow
                  key={`${purchase.id.toString()}-row`}
                  className="relative max-h-auto"
                >
                  <TableCell>
                    <div className="flex flex-row w-full gap-6 items-center justify-start">
                      <div className="w-20 h-14">
                        <MediaBox
                          className="bg-gray-100"
                          media={media}
                          width={96}
                          height={54}
                          ratio={16 / 9}
                        />
                      </div>

                      <h5>{purchase?.product?.name}</h5>
                    </div>
                  </TableCell>

                  <TableCell className="min-w-[4rem]">
                    {purchase?.product?.price?.amount === 0 ? (
                      <Badge variant="green" size="sm">
                        Free
                      </Badge>
                    ) : (
                      centsToDollars(purchase?.product?.price?.amount)
                    )}
                  </TableCell>

                  {/* 
                  <TableCell className="max-w-[6rem]">
                    {purchase?.product?.owner?.handler ? (
                      <Link href={`/@${purchase.product.owner.handler}`}>
                        @{purchase.product.owner.handler}
                      </Link>
                    ) : (
                      '-'
                    )}
                  </TableCell>
                  */}

                  <TableCell className="max-w-[6rem]">
                    {purchase?.created ? format(purchase?.created, 'PP') : '-'}
                  </TableCell>

                  <TableCell className="max-w-[8rem]">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleRowExpansion(purchase.id.toString())}
                    >
                      <span className="hidden sm:block text-xs">Details</span>
                      {isExpanded ? <ChevronUp /> : <ChevronDown />}
                    </Button>
                  </TableCell>

                  <TableCell className="min-w-[1rem]">
                    <PurchaseActionsMenu
                      hashId={purchase?.hashId ?? undefined}
                      invoiceUrl={metadata?.invoiceUrl ?? undefined}
                      invoicePdf={metadata?.invoicePdf ?? undefined}
                    />
                  </TableCell>
                </TableRow>

                {isExpanded && (
                  <TableRow
                    key={`${purchase.id.toString()}-details`}
                    className="bg-muted/50"
                  >
                    <TableCell colSpan={6} className="p-0">
                      <BundleMultiTransfer
                        assets={purchase?.product?.platforms?.bundle?.assets}
                        transfers={purchase?.transfers}
                      />
                    </TableCell>
                  </TableRow>
                )}
              </React.Fragment>
            )
          }),
        )}
      </TableBody>
    </Table>
  )
}
