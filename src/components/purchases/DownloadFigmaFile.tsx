import { Download } from 'lucide-react'
import prettyBytes from 'pretty-bytes'
import { useEffect, useMemo, useRef, useState } from 'react'
import { FaFigma } from 'react-icons/fa'
import { File as FileRecord } from '@/actions/file'
import { Product } from '@/actions/product'
import { Button } from '@/components/ui/button'
import type { ReactNode } from 'react'

type Props = {
  product: Product
  likeIcon?: boolean
  className?: string
  children?: ReactNode
  noIcon?: boolean
}

export default function DownloadFigmaFile({
  product,
  likeIcon = false,
  className,
  children = 'Figma File',
  noIcon = true,
}: Props) {
  const download = useRef<HTMLAnchorElement | null>(null)
  const timer = useRef<NodeJS.Timeout | null>(null)
  const [isDownloading, setIsDownloading] = useState(false)

  const { url, metadata } = useMemo(() => {
    const file = product?.fileSet?.files.find(item =>
      item.flair?.includes('figma'),
    )

    return file ?? ({} as FileRecord)
  }, [product?.fileSet?.files])

  const clickDownloadHandler = () => {
    if (url && download.current) {
      setIsDownloading(true)
      download.current.click()
      timer.current = setTimeout(() => {
        setIsDownloading(false)
      }, 1000)
    }
  }

  useEffect(
    () => () => {
      if (timer.current) {
        clearTimeout(timer.current)
      }
    },
    [],
  )

  return (
    <div>
      {url ? (
        <>
          <a ref={download} href={url} className="hidden" />
          {likeIcon ? (
            <Button
              variant="clear"
              size="xs"
              onClick={clickDownloadHandler}
              disabled={isDownloading}
              className={className}
            >
              <FaFigma />
              {isDownloading
                ? 'Downloading...'
                : metadata?.name && metadata?.size
                  ? `${metadata.name} (${prettyBytes(metadata.size)} bytes)`
                  : children}
            </Button>
          ) : (
            <Button
              variant="outline"
              size="xs"
              onClick={clickDownloadHandler}
              disabled={isDownloading}
              className={className}
            >
              {noIcon && <Download />}
              {isDownloading ? 'Downloading...' : children}
            </Button>
          )}
        </>
      ) : (
        <span className="text-xs">No Figma file</span>
      )}
    </div>
  )
}
