'use client'

import { format } from 'date-fns'
import Link from 'next/link'
import React from 'react'

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs'

import EmptyList from '@/components/internal/EmptyList'
import InternalPageHeader from '@/components/internal/InternalPageHeader'
import PurchaseActionsMenu from '@/components/purchases/PurchaseActionsMenu'
import ProfilePurchaseListTransfer from './ProfilePurchaseListTransfer'

import usePurchaseList from '@/hooks/use-purchase-list'
import useSessionUser from '@/hooks/useSessionUser'

import { PaymentLog } from '@/actions/payment'
import MediaBox from '@/components/common/MediaBox'
import { Badge } from '@/components/ui/badge'
import centsToDollars from '@/utils/cents-to-dollars'
import DownloadFigmaFile from './DownloadFigmaFile'
import PurchaseTableBundle from './PurchaseTableBundle'

const purchasesKind = [
  'templates',
  // 'components',
  // 'app',
  'bundles',
  // 'subscriptions',
]

const flairStringMap: any = {
  templates: 'template',
  components: 'component',
  bundles: 'bundle',
  subscriptions: 'subscription',
}

/**
 *
 */
export default function PurchaseList() {
  const [flair, setFlair] = React.useState('templates')
  const { data: user } = useSessionUser()
  const {
    data: purchases,
    isFetchingNextPage,
    ref,
  } = usePurchaseList(
    user?.id?.toString(),
    (flairStringMap?.[flair] as string) ?? 'template',
  )

  const clickTabHandler = (kind: string) => () => {
    setFlair(kind)
  }

  // const clickDownladHandler = (purchase: any) => () => {
  //   // Add download logic here
  //   // console.log('Download clicked for', purchase)
  // }

  return purchases ? (
    <section className="container mx-auto">
      <InternalPageHeader
        title={`Purchases/${flair}`}
        subtitle="Access your purchased products"
      />

      <Tabs defaultValue="templates" className="mb-2">
        <TabsList>
          {purchasesKind.map(kind => (
            <TabsTrigger
              key={kind}
              value={kind}
              className="capitalize"
              onClick={clickTabHandler(kind)}
            >
              {kind}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>

      {flair === 'bundles' ? (
        <PurchaseTableBundle key="bundles" purchases={purchases} />
      ) : (
        <Table key={flair} className="w-full">
          <TableHeader>
            <TableRow>
              <TableHead>Thumb/Name</TableHead>
              <TableHead>Price</TableHead>
              {/* <TableHead>Platform</TableHead> */}
              <TableHead>Creator</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Extras</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>

          <TableBody>
            {purchases.pages.map(page =>
              page?.map((purchase: PaymentLog) => {
                const metadata = purchase?.metadata as Record<string, string>

                const media =
                  purchase?.product?.fileSet?.files?.find(
                    file =>
                      file.mimeType?.startsWith('video/') ||
                      file.mimeType?.startsWith('image/'),
                  ) ?? {}

                return (
                  <TableRow key={purchase.id.toString()}>
                    <TableCell>
                      <div className="flex flex-row w-full gap-6 items-center justify-start">
                        <div className="w-20 h-14">
                          <MediaBox
                            className="bg-gray-100"
                            media={media}
                            width={79}
                            height={53}
                            ratio={79 / 53}
                          />
                        </div>

                        <h5>{purchase?.product?.name}</h5>
                      </div>
                    </TableCell>

                    <TableCell className="min-w-[4rem]">
                      {purchase?.product?.price?.amount === 0 ? (
                        <Badge variant="green" size="sm">
                          Free
                        </Badge>
                      ) : (
                        centsToDollars(purchase?.product?.price?.amount)
                      )}
                    </TableCell>

                    {/* <TableCell className="min-w-[6rem]">
                    {getPlatform(purchase?.product?.platforms) ? (
                      <PlatformWidget
                        platform={getPlatform(purchase.product.platforms)}
                        justIcon
                      />
                    ) : (
                      '\u00A0'
                    )}
                  </TableCell> */}

                    <TableCell className="max-w-[6rem]">
                      {purchase?.product?.owner?.handler ? (
                        <Link href={`/@${purchase.product.owner.handler}`}>
                          @{purchase.product.owner.handler}
                        </Link>
                      ) : (
                        '-'
                      )}
                    </TableCell>

                    <TableCell className="max-w-[8rem]">
                      {format(purchase?.created, 'PP')}
                    </TableCell>

                    <TableCell className="max-w-[8rem]">
                      <DownloadFigmaFile product={purchase.product} />
                    </TableCell>

                    <TableCell className="max-w-[8rem]">
                      <ProfilePurchaseListTransfer purchase={purchase} />
                    </TableCell>

                    <TableCell className="min-w-[1rem]">
                      <PurchaseActionsMenu
                        hashId={purchase?.hashId ?? undefined}
                        invoiceUrl={metadata?.invoiceUrl ?? undefined}
                        invoicePdf={metadata?.invoicePdf ?? undefined}
                      />
                    </TableCell>
                  </TableRow>
                )
              }),
            )}
          </TableBody>
        </Table>
      )}

      <div ref={ref} className="flex justify-center mt-4">
        {isFetchingNextPage && <span>Loading...</span>}
      </div>
    </section>
  ) : purchases !== undefined ? (
    <EmptyList>You don&apos;t have any purchase yet</EmptyList>
  ) : null
}
