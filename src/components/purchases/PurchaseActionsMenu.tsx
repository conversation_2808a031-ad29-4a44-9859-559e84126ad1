'use client'

import { EllipsisVerticalIcon } from '@heroicons/react/24/outline'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

import ClippboardClient from '@/components/common/ClippboardClient'

interface Props {
  hashId?: string
  invoiceUrl?: string
  invoicePdf?: string
}

export default function PurchaseActionsMenu({
  hashId,
  invoiceUrl,
  invoicePdf,
}: Props) {
  const clickOpenReceipt = async () => {
    window.open(invoiceUrl, '_blank')
  }

  const clickDownloadInvoice = async () => {
    window.open(invoicePdf, '_blank')
  }

  const clickReportProblem = async () => {
    window.open('mailto:<EMAIL>', '_blank')
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <EllipsisVerticalIcon className="w-6 h-6" />
      </DropdownMenuTrigger>

      <DropdownMenuContent className="w-[14rem]">
        <DropdownMenuLabel>
          <div className="flex flex-col gap-1">
            <span className="text-gray-400 text-[0.625rem] font-bold uppercase tracking-wide">
              Transaction id
            </span>
            <div className="flex flex-row justify-between">
              <ClippboardClient
                valueToCopy={hashId!}
                className="relative end-0"
              >
                <pre className="flex items-center relative text-[0.75rem]">
                  {hashId}
                </pre>
              </ClippboardClient>
            </div>
          </div>
        </DropdownMenuLabel>

        <DropdownMenuSeparator />

        {invoiceUrl ? (
          <DropdownMenuItem
            className="cursor-pointer"
            onClick={clickOpenReceipt}
          >
            Open receipt
          </DropdownMenuItem>
        ) : null}
        {invoicePdf ? (
          <DropdownMenuItem
            className="cursor-pointer"
            onClick={clickDownloadInvoice}
          >
            Download invoice pdf
          </DropdownMenuItem>
        ) : null}
        <DropdownMenuItem
          className="cursor-pointer"
          onClick={clickReportProblem}
        >
          Report a problem
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
