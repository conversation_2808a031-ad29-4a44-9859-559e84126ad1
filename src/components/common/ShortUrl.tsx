'use client'

import React, { useEffect, useState } from 'react'
import Link from 'next/link'

import { saveUrl } from '@/actions/url'
import ClippboardClient from '@/components/common/ClippboardClient'
import { Button } from '@/components/ui/button'

interface Props {
  url: string
  children?: React.ReactNode
}

export default function ShortUrl({ children, url }: Props) {
  const [shortURL, setShortURL] = useState<string>()

  useEffect(() => {
    (async () => {
      if (shortURL) return
      const short = await saveUrl(url)
      setShortURL(short)
    })()
  }, [url, shortURL])

  return shortURL ? (
    <div className="flex flex-row items-center">
      <Button variant="ghost" size="xs" className="overflow-hidden">
        <Link
          href={shortURL}
          target="_blank"
          className="whitespace-nowrap overflow-hidden overflow-ellipsis"
        >
          {String(shortURL).replace('https://', '').replace('http://', '')}
        </Link>
      </Button>

      <ClippboardClient valueToCopy={shortURL} size="xxs" />
    </div>
  ) : (
    <span className="inline-block w-[8rem] bg-gray-300">&nbsp;</span>
  )
}
