'use client'

import React, { useState } from 'react'
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline'

import Button from './HomeButton'

interface Props {
  children?: React.ReactNode
}

export default React.forwardRef<HTMLLIElement, Props>(function SlideListItem(
  { children, ...props },
  ref,
) {
  const [open, setOpen] = useState(false)
  return (
    <li
      ref={ref}
      {...props}
      className="grid gap-0 w-full justify-end duration-300 relative border-b-[1px] border-gray-100 last:border-b-0"
      style={{
        gridTemplateRows: open ? '5rem calc(100% - 5rem)' : '5rem 0',
        maxHeight: open ? '100%' : '5rem',
      }}
    >
      <Button
        className="absolute top-0 left-0  bg-gray-100 hover:bg-gray-200 rounded-md p-1"
        onClick={() => setOpen(!open)}
        design="none"
      >
        {open ? (
          <ChevronUpIcon className="w-4 h-4 text-gray-500" />
        ) : (
          <ChevronDownIcon className="w-4 h-4 text-gray-500" />
        )}
      </Button>
      {children}
    </li>
  )
})
