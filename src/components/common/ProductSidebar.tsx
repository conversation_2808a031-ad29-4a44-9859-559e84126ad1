import Link from 'next/link'
import { Check, ExternalLink } from 'lucide-react'

import { Product, PlatformsEnum } from '@/actions/product'
import {
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from '@/components/ui/tooltip'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import centsToDollars from '@/utils/cents-to-dollars'
import generateBuyLink from '@/actions/url/generateBuyLink'
import PlatformWidget from '@/components/products/PlatformWidget'
// import FigmaWidget from '@/components/common/FigmaWidget'
import ProductSidebarHelp from '@/components/common/ProductSidebarHelp'

import { HOST } from '@/config'
import { PaymentPrice } from '@/actions/price'
import { getIdFromRecord } from '@/utils'
import generateRedeemLink from '@/actions/url/generateRedeemLink'

type Props = React.PropsWithChildren<{ product: Product }>

export default async function ProductSidebar({ product }: Props) {
  const buyLink = await generateBuyLink({
    website: `${HOST}/product/${product.slug}`,
    redirect: `${HOST}/product/${product.slug}`,
    productId: getIdFromRecord(product.id),
  })

  const redeemLink = await generateRedeemLink({
    website: `${HOST}/product/${product.slug}`,
    productId: getIdFromRecord(product.id),
  })

  const isFree =
    (product?.price?.externalId?.startsWith('free_') ||
      product?.price?.amount === 0) ??
    true

  return product ? (
    <div className="flex flex-col min-h-[calc(100vh-6rem)] sticky top-24">
      <div className="flex-1">
        <div className="flex flex-col gap-4">
          <div className="flex items-center justify-between gap-2">
            <div className="flex items-center gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <PlatformWidget
                      platform={Object.keys(product.platforms)[0]}
                      justIcon
                    />
                  </TooltipTrigger>
                  <TooltipContent>
                    {Object.keys(product.platforms)[0] === PlatformsEnum.FRAMER
                      ? 'Build on Framer'
                      : null}
                    {Object.keys(product.platforms)[0] === PlatformsEnum.WEBFLOW
                      ? 'Build on Webflow'
                      : null}
                    {Object.keys(product.platforms)[0] === PlatformsEnum.BUNDLE
                      ? 'Build on multiple platforms'
                      : null}
                    {Object.keys(product.platforms)[0] ===
                    PlatformsEnum.SUBSCRIPTION
                      ? 'Stay connected to your creator'
                      : null}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <h1 className="text-lg font-medium">
                {`${product?.name} ${centsToDollars((product?.price as PaymentPrice)?.amount)}`}
              </h1>
            </div>

            {product?.liveUrl && (
              <Button variant="ghost" size="icon" className="rounded-full">
                <Link href={product.liveUrl} target="_blank">
                  <ExternalLink className="w-4 h-4" />
                </Link>
              </Button>
            )}
          </div>

          {/* <FigmaWidget /> */}

          <div className="flex">
            <Button className="flex-1" asChild>
              <a href={isFree ? redeemLink : buyLink}>
                {isFree
                  ? 'Redeem this product now'
                  : `Buy Now - ${centsToDollars(product?.price?.amount)}`}
              </a>
            </Button>
          </div>

          <div className="flex flex-wrap gap-2">
            {product?.features?.map(tag => (
              <Badge key={tag} variant="secondary">
                {tag}
              </Badge>
            ))}
          </div>

          <Tabs defaultValue="description" className="w-full">
            <TabsList className="w-full">
              <TabsTrigger value="description">Description</TabsTrigger>
              <TabsTrigger value="includes">
                {'What\u2019s Included'}
              </TabsTrigger>
            </TabsList>

            {product?.description ? (
              <TabsContent value="description">
                <p className="text-sm leading-relaxed my-4 text-gray-600">
                  {product.description}
                </p>
              </TabsContent>
            ) : null}

            {(product?.includes ?? [])?.length > -1 && (
              <TabsContent value="includes">
                <ul className="text-sm flex flex-col gap-2 py-4">
                  {product?.includes?.map(item => (
                    <li
                      key={item}
                      className="flex items-center text-gray-600 gap-2"
                    >
                      <Check className="w-3 h-3 text-green-600" />
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </TabsContent>
            )}
          </Tabs>
        </div>
      </div>

      <ProductSidebarHelp />
    </div>
  ) : null
}
