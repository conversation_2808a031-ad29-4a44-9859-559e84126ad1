import { cn } from '@/lib/utils'

type Props = React.PropsWithChildren<{
  className?: string
  id?: string
}>

export default function Header({ children, className }: Props) {
  return (
    <header
      className={cn(
        'fixed top-0 left-0 md:left-16 right-0 z-30 h-16 bg-white border-b border-gray-200 flex items-center justify-end',
        className,
      )}
    >
      {children}
    </header>
  )
}
