import { Button } from '@/components/ui/button'
import SmartButton from '../SmartButton'
import { useRouter } from 'next/navigation'
import useSessionUser from '@/hooks/useSessionUser'
import UserBox from '../UserBox'

export default function AccessPanel() {
  const { data: user } = useSessionUser()
  const router = useRouter()

  return (
    <>
      {user ? (
        <>
          <div className="flex items-center gap-4 justify-between mr-2">
            <SmartButton />
          </div>
          <UserBox />
        </>
      ) : (
        <Button
          variant="ghost"
          className="h-[2.25rem]"
          onClick={() => router.push('/login')}
        >
          Connect to Template
        </Button>
      )}
    </>
  )
}
