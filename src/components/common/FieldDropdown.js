export default function FieldDropdown({
  register,
  className = "",
  fieldName,
  options = {},
  error = "",
}) {
  // console.log("FieldDropdown:options", options.selected);
  return (
    <div className={`flex flex-col w-full p-2 ${className}`}>
      <label
        className="flex justify-between w-full text-gray-800 text-sm"
        htmlFor={fieldName}
      >
        <span>{options?.label}</span>
        <small className="text-gray-400">{options?.instructions}</small>
      </label>
      <div className="w-full flex justify-center align-center p-3 border-[1px] border-gray-200 rounded-md outline-none has-[:focus]:outline-none has-[:focus]:border-gray-400 cursor-text">
        {options?.input?.prefix ? <div>{options.input.prefix}</div> : null}
        <select
          name={fieldName}
          className="outline-none w-full"
          placeholder={options?.input?.placeholder}
          {...register(fieldName, options)}
        >
          {options?.options?.map((option) => (
            <option
              key={option.value}
              value={option.value}
              selected={option.value === options.selected}
            >
              {option.label}
            </option>
          ))}
        </select>
      </div>
      <div className="min-h-[1.5rem]">
        {error ? <small className="text-red-500">{error}</small> : null}
      </div>
    </div>
  );
}
