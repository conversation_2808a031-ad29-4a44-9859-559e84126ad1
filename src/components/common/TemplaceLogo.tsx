import { cn } from '@/lib/utils'
import Link from 'next/link'

type Props = React.PropsWithChildren<{
  className?: string
  full?: boolean
}>

export default function TemplaceLogo({ className = '', full = false }: Props) {
  const logo = (
    <svg
      viewBox="0 0 1200 1200"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <rect width="1200" height="1200" rx="180" fill="#151515" />
      <path d="M661.34 269H982V541.073H661.34V269Z" fill="white" />
      <path
        d="M459.495 458.019C617.617 458.019 745.799 586.242 745.799 744.413V932H473.811V730.093H219V458.019H459.495Z"
        fill="white"
      ></path>
    </svg>
  )

  return full ? (
    <>
      <Link href="/">
        <div
          className={cn(
            'flex h-16 items-center justify-center pl-[0.95rem] pr-[0.95rem]',
            className,
          )}
        >
          {logo}
        </div>
      </Link>
    </>
  ) : (
    logo
  )
}
