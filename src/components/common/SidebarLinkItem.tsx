import Link from 'next/link'
import { LucideIcon } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'

interface Props {
  path: string
  title: string
  icon?: LucideIcon
  isActive: boolean
  count?: number
}

export default function SidebarLinkItem({
  path,
  title,
  icon: Icon,
  isActive,
  count,
}: Props) {
  return (
    <Link
      href={path}
      className={cn(
        'flex items-center w-full px-3 py-2 rounded-lg transition-colors',
        isActive
          ? 'bg-gray-100 text-gray-900'
          : 'hover:bg-gray-100 text-gray-600',
      )}
    >
      {Icon !== undefined && (
        <Icon
          className={cn(
            'h-4 w-4 mr-2',
            isActive ? 'text-gray-900' : 'text-gray-600',
          )}
          strokeWidth={1.25}
        />
      )}
      <span className="text-sm font-medium flex-1">{title}</span>
      {count !== undefined && (
        <Badge
          key={`${title}-${count}`}
          variant="secondary"
          className="bg-[#F1F1F1] text-gray-400 border-none h-5"
        >
          {count}
        </Badge>
      )}
    </Link>
  )
}
