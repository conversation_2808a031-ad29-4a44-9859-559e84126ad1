import { Loader2, Upload } from 'lucide-react'
import React from 'react'
import { DropzoneOptions, useDropzone } from 'react-dropzone'

import {
  File as FileRecord,
  getUploadToken,
  getUploadUrl,
} from '@/actions/file'
import { cn } from '@/lib/utils'
import { getTypeByExtension } from '@/utils'
import { convertFileToImage } from '@/utils/encode-file-to-image'
import { convertFileToVideo } from '@/utils/encode-file-to-video'

type FileWithPublicUrl = FileRecord & { publicUrl?: string }
type MimeDropper = (
  files: File,
  metadata?: Record<string, any>,
) => Promise<FileWithPublicUrl>
type MimeDroppers = Record<string, MimeDropper>

type Props = React.PropsWithChildren<{
  onUpload: (files: FileRecord[]) => void
  label?: string
  dropzoneOptions?: DropzoneOptions
  className?: string
  classNameUpload?: string
  folder?: string
  disabled?: boolean
  flair?: string[]
}>

const defaultDropzoneOptions: DropzoneOptions = {
  accept: {
    'image/webp': ['.webp'],
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png'],
  },
  maxFiles: 1,
  multiple: false,
}

export async function uploadFile(
  file: File,
  folder: string,
  meta: Record<string, any>,
): Promise<FileRecord> {
  const token = await getUploadToken(meta)
  const { url: signedUrl, fileCreated } = await getUploadUrl(
    folder,
    file.name,
    meta.type,
    token,
    meta,
  )

  const uploadResponse = await fetch(signedUrl, {
    method: 'PUT',
    body: file,
    headers: {
      'Content-Type': meta.type,
    },
  })

  if (!uploadResponse.ok) {
    throw new Error(
      `Upload failed: ${uploadResponse.status} ${uploadResponse.statusText}`,
    )
  }

  return fileCreated as FileRecord
}

/**
 *
 */
export default function DropUpload({
  onUpload,
  dropzoneOptions,
  className,
  classNameUpload,
  children,
  disabled,
  folder = 'files',
  flair = [],
}: Props) {
  const [isUploading, setIsUploading] = React.useState(false)

  const onDrop = async (files: File[]) => {
    setIsUploading(true)
  }

  const onDropZip = async (
    file: File,
    metadata?: Record<string, any>,
  ): Promise<FileRecord> => {
    const meta = {
      ...metadata,
      name: file.name,
      size: file.size,
      type: file?.type || metadata?.type,
      lastModified: file.lastModified,
      flair: [...(metadata?.flair ?? []), ...(file.type ? [file.type] : [])],
    }

    const fileCreated = await uploadFile(file, folder, meta)
    return fileCreated as FileRecord
  }

  const onDropImages = async (
    file: File,
    metadata?: Record<string, any>,
  ): Promise<FileRecord> => {
    const { imageFile, width, height } = await convertFileToImage(file)
    if (!imageFile) {
      throw new Error('File cannot be converted to image')
    }

    if (imageFile.size / 1024 > 2048) {
      throw new Error('Image is too large')
    }

    const meta = {
      ...metadata,
      name: imageFile.name,
      size: imageFile.size,
      type: imageFile.type,
      originalWidth: width,
      originalHeight: height,
      aspectRatio: width / height,
      lastModified: imageFile.lastModified,
      flair: [
        ...(metadata?.flair ?? []),
        ...(imageFile.type ? [imageFile.type] : []),
      ],
    }

    const fileCreated = await uploadFile(file, folder, meta)
    return fileCreated as FileRecord
  }

  const onDropVideo = async (
    file: File,
    metadata?: Record<string, any>,
  ): Promise<FileRecord> => {
    const { videoFile } = await convertFileToVideo(file)

    const meta = {
      ...metadata,
      name: videoFile.name,
      size: videoFile.size,
      type: videoFile.type,
      flair: [
        ...(metadata?.flair ?? []),
        ...(videoFile.type ? [videoFile.type] : []),
      ],
    }

    const fileCreated = await uploadFile(file, folder, meta)
    return fileCreated as FileRecord
  }

  const onDropAccepted = async (files: File[]) => {
    const mimeDroppers: MimeDroppers = {
      'image/webp': onDropImages,
      'image/jpeg': onDropImages,
      'image/png': onDropImages,
      'video/mp4': onDropVideo,
      'application/zip': onDropZip,
      'application/octet-stream': onDropZip,
    }

    try {
      const droppedFiles = await files.reduce(
        async (promise: Promise<FileRecord[]>, file: File) => {
          const droppedFiles = await promise
          const type = file?.type || getTypeByExtension(file)
          const dropper = mimeDroppers?.[type]
          if (dropper) {
            const dropped = await dropper(file, { type, flair })
            droppedFiles.push(dropped)
          }
          return Promise.resolve([...droppedFiles])
        },
        Promise.resolve([]),
      )

      onUpload(droppedFiles)
    } catch (err) {
      console.error(err)
    } finally {
      setIsUploading(false)
    }
  }

  const { getRootProps, getInputProps, isDragActive, acceptedFiles, open } =
    useDropzone({
      ...defaultDropzoneOptions,
      ...dropzoneOptions,
      onDropAccepted,
      onDrop,
      disabled: disabled || isUploading,
    })

  return (
    <div
      {...getRootProps()}
      className={cn(
        className,
        'relative w-full h-full cursor-pointer',
        isDragActive && 'ring-1 ring-foreground/30 ring-inset',
      )}
    >
      <input {...getInputProps()} />

      {children}

      {isDragActive ? (
        <div
          className={cn(
            'absolute top-0 left-0 w-full h-full bg-background/50 grid place-content-center hover:bg-background/70',
            acceptedFiles.length > 0 && !isDragActive && 'hidden',
            isDragActive && 'bg-background/70',
          )}
        >
          <Upload className={cn('w-8 h-8', classNameUpload)} />
        </div>
      ) : null}

      {isUploading && (
        <div className="absolute top-0 left-0 w-full h-full bg-background/50 grid place-content-center">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      )}
    </div>
  )
}
