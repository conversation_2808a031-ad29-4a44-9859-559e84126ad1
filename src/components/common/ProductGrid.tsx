'use client'
import React, { useEffect } from 'react'
import { useInView } from 'react-intersection-observer'
import { InfiniteData, useInfiniteQuery } from '@tanstack/react-query'

import { getProductsByFlair, Product } from '@/actions/product'
import { INFINITE_PRODUCTS } from '@/io/query-client'
import ProductBox from '@/components/common/ProductBox'
import ProductBoxMini from '@/components/common/ProductBoxMini'

type Props = React.PropsWithChildren<{
  flair: string
  initialData?: InfiniteData<Product[] | undefined, number>
  useMini?: boolean
}>

const PAGE_SIZE = 12

const fetchItems =
  (flair: string) =>
  async ({ pageParam = 0 }) =>
    getProductsByFlair(flair, PAGE_SIZE, PAGE_SIZE * pageParam)

/**
 * ProductGrid
 */
export default function ProductGrid({
  flair,
  initialData = { pages: [], pageParams: [] },
  useMini = false,
}: Props) {
  const { ref, inView } = useInView()

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useInfiniteQuery({
      queryKey: [...INFINITE_PRODUCTS, { flair }],
      queryFn: fetchItems(flair),
      getNextPageParam: (lastPage, pages) => {
        return lastPage?.length ? pages.length + 0 : undefined
      },
      initialPageParam: 0,
      initialData,
    })

  React.useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage()
    }
  }, [inView, hasNextPage, fetchNextPage])

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {data?.pages.map(page =>
          page?.map((product: Product) => {
            const link = `/product/${product.slug}`
            return useMini ? (
              <ProductBoxMini key={product.id} product={product} link={link} />
            ) : (
              <ProductBox key={product.id} product={product} link={link} />
            )
          }),
        )}
      </div>

      <div ref={ref} className="flex justify-center mt-4">
        {isFetchingNextPage && <span>Loading...</span>}
      </div>
    </>
  )
}
