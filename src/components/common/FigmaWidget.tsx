import { cn } from '@/lib/utils'

type Props = {
  className?: string
  justIcon?: boolean
  size?: string
}

export default function FigmaWidget({
  className,
  justIcon = false,
  size = '6',
}: Props) {
  const sizeClassName = `w-${size} h-${size}`
  return (
    <div className={cn('flex flex-row items-center gap-2', className)}>
      <div
        className={cn(
          'grid place-items-center rounded-sm bg-gray-100 p-1',
          sizeClassName,
        )}
      >
        <svg
          width="8"
          height="12"
          viewBox="0 0 41 61"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className={`w-[77%] h-[77%]`}
        >
          <path
            d="M10.3531 60.7526C15.9543 60.7526 20.5002 56.2067 20.5002 50.6056V40.4585H10.3531C4.75194 40.4585 0.206055 45.0044 0.206055 50.6056C0.206055 56.2067 4.75194 60.7526 10.3531 60.7526Z"
            fill="#0ACF83"
          />
          <path
            d="M0.206055 30.8751C0.206055 25.2739 4.75194 20.728 10.3531 20.728H20.5002V41.0221H10.3531C4.75194 41.0221 0.206055 36.4763 0.206055 30.8751Z"
            fill="#A259FF"
          />
          <path
            d="M0.206055 10.5807C0.206055 4.97948 4.75194 0.433594 10.3531 0.433594H20.5002V20.7277H10.3531C4.75194 20.7277 0.206055 16.1818 0.206055 10.5807Z"
            fill="#F24E1E"
          />
          <path
            d="M20.5 0.433594H30.6471C36.2482 0.433594 40.7941 4.97948 40.7941 10.5807C40.7941 16.1818 36.2482 20.7277 30.6471 20.7277H20.5V0.433594Z"
            fill="#FF7262"
          />
          <path
            d="M40.7941 30.8751C40.7941 36.4763 36.2482 41.0221 30.6471 41.0221C25.0459 41.0221 20.5 36.4763 20.5 30.8751C20.5 25.2739 25.0459 20.728 30.6471 20.728C36.2482 20.728 40.7941 25.2739 40.7941 30.8751Z"
            fill="#1ABCFE"
          />
        </svg>
      </div>
      {!justIcon ? (
        <span className="text-[.9rem] text-gray-700 font-semibold">
          Figma File Included
        </span>
      ) : null}
    </div>
  )
}
