'use client'

import { Loader2 } from 'lucide-react'

import { useLoading } from '@/hooks/use-loading'

export default function Loading() {
  const { data: loading } = useLoading()

  return loading?.message !== undefined ? (
    <div className="grid fixed inset-0 place-items-center h-screen w-screen z-50 bg-[#fffe]">
      <div className="flex flex-col md:flex-row gap-2 items-center justify-center">
        <Loader2 className="animate-spin w-4 h-4" />
        <span className="text-xs">{loading?.message || 'Loading...'}</span>
      </div>
    </div>
  ) : null
}
