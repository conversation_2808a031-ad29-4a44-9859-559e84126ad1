'use client'

import {
  <PERSON>a<PERSON><PERSON><PERSON>pp,
  FaFacebookMessenger,
  FaTwitter,
  FaLinkedin,
  FaRegClipboard,
  FaClipboardCheck,
} from 'react-icons/fa'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Button } from '../ui/button'
import React, { useState, type ReactNode } from 'react'

type ShareModalProps = {
  children: ReactNode
  text: string
  url: string
}

export default function ShareModal({ children, text, url }: ShareModalProps) {
  const [copied, setCopied] = useState(false)
  const message = encodeURIComponent(`${text} ${url}`)

  const handleWhatsAppShare = () => {
    window.open(`https://wa.me/?text=${message}`, '_blank')
  }

  const handleMessengerShare = () => {
    window.open(
      `fb-messenger://share/?link=${encodeURIComponent(url)}&app_id=YOUR_APP_ID`,
      '_blank',
    )
  }

  const handleTwitterShare = () => {
    window.open(`https://twitter.com/intent/tweet?text=${message}`, '_blank')
  }

  const handleLinkedInShare = () => {
    window.open(
      `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(
        url,
      )}`,
      '_blank',
    )
  }

  const handleCopy = () => {
    navigator.clipboard.writeText(url).then(() => {
      setCopied(true)
      setTimeout(() => setCopied(false), 1500)
    })
  }

  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>

      <DialogContent aria-describedby="Share this content">
        <DialogDescription className="sr-only">
          Share this content
        </DialogDescription>
        <div className="w-full py-6">
          <div className="flex flex-col items-center justify-center py-2 ">
            <DialogTitle className="text-2xl font-bold mb-4">
              Share this content
            </DialogTitle>
            <div className="flex space-x-4 py-3">
              <button
                onClick={handleWhatsAppShare}
                className="flex items-center justify-center p-2 bg-green-500 text-white rounded-full hover:bg-green-600"
              >
                <FaWhatsapp size={24} />
              </button>
              <button
                onClick={handleMessengerShare}
                className="flex items-center justify-center p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600"
              >
                <FaFacebookMessenger size={24} />
              </button>
              <button
                onClick={handleTwitterShare}
                className="flex items-center justify-center p-2 bg-blue-400 text-white rounded-full hover:bg-blue-500"
              >
                <FaTwitter size={24} />
              </button>
              <button
                onClick={handleLinkedInShare}
                className="flex items-center justify-center p-2 bg-blue-600 text-white rounded-full hover:bg-blue-900"
              >
                <FaLinkedin size={24} />
              </button>
            </div>
          </div>

          <div className="flex flex-col items-center justify-center mb-2 py-2">
            <h1 className="text-xl font-bold mb-4">Copy this URL</h1>
            <div className="flex items-center justify-center w-full max-w-xs">
              <Button
                variant={copied ? 'secondary' : 'default'}
                onClick={handleCopy}
                className="w-full rounded-md"
              >
                {copied ? (
                  <FaClipboardCheck size={24} />
                ) : (
                  <FaRegClipboard size={16} />
                )}
                <span className="ml-2">{copied ? 'Copied!' : 'Copy URL'}</span>
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
