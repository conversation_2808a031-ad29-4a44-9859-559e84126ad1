'use client'

import Image from 'next/image'
import { signIn } from 'next-auth/react'

import { Button } from '@/components/ui/button'

export default function AppleLoginButton({
  redirect = '',
  className = '',
  disabled = false,
}) {
  const handleClick = () => {
    const options = redirect ? { callbackUrl: redirect } : undefined
    signIn('apple', options)
  }

  return (
    <Button
      onClick={handleClick}
      variant="pill"
      size="xxl"
      className={className}
      disabled={disabled}
    >
      <Image
        src="/assets/logo-apple.svg"
        alt="Apple Logo"
        width={26}
        height={26}
      />
      Continue With Apple
    </Button>
  )
}
