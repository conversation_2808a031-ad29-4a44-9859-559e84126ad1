'use client'
import Image from 'next/image'
import { signIn } from 'next-auth/react'

import { Button } from '@/components/ui/button'
import { useRouter } from 'next/navigation'

type Props = React.PropsWithChildren<{
  redirect?: string
  className?: string
}>

export default function GoogleLoginButton({ redirect = '', className }: Props) {
  const router = useRouter()

  const handleClick = () => {
    signIn('google', { redirect: false })
    router.push(redirect || '/post-login-redirect')
  }

  return (
    <Button className={className} onClick={handleClick} size="xl">
      <Image
        src="/assets/logo-google.svg"
        alt="Google Logo"
        width={16}
        height={16}
      />
      Continue With Google
    </Button>
  )
}
