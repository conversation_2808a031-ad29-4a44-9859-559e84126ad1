import ProductBox from '@/components/common/ProductBox'
import { Product } from '@/actions/product'
import { getRecentProducts } from '@/actions'

type Props = React.PropsWithChildren<{
  total: number
}>

export default async function RecentProducts({ total = 0 }: Props) {
  const templates = await getRecentProducts(total)
  return (
    total !== 0 && (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {Array.isArray(templates) &&
          templates?.map((template: Product) => (
            <ProductBox
              key={template.id.toString()}
              product={template}
              link={`/product/${template.slug}`}
            />
          ))}
      </div>
    )
  )
}
