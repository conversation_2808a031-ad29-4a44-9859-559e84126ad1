'use client'
import { But<PERSON> } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogTrigger,
  DialogTitle,
} from '@/components/ui/dialog'
import GoogleLoginButton from '@/components/common/GoogleLoginButton'
import AppleLoginButton from './AppleLoginButton'

export default function LoginModal({ isLogin, dialogId, onOpenChange }: any) {
  const open = dialogId === 'login'
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="max-w-[80dvw] lg:max-w-[28em]"
        aria-describedby="login dialog"
      >
        <DialogTitle className="text-xl mb-6 text-center">
          {isLogin ? 'Welcome back' : 'Select your social account'}
        </DialogTitle>

        <div className="grid grid-cols-auto gap-4 justify-center">
          {/* {isLogin ? (
            <LoginForm onSuccess={onClose} />
          ) : (
            <SignupForm onSuccess={onClose} />
          )} */}

          <GoogleLoginButton />
          {/* <AppleLoginButton disabled className="disabled:opacity-50" /> */}

          {/* <div className="text-center space-y-4">
            <div className="text-sm text-gray-600">
              {isLogin ? "Don't have an account?" : 'Already have an account?'}
            </div>
            <Button
              variant="ghost"
              className="w-full"
              // onClick={() => setIsLogin(!isLogin)}
            >
              {isLogin ? 'Sign up' : 'Log in'}
            </Button>
          </div> */}
        </div>
      </DialogContent>
    </Dialog>
  )
}
