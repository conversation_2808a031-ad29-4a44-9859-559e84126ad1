import Link from 'next/link'
import { Button } from '@/components/ui/button'
import ProductBox from '@/components/common/ProductBox'
import { Product } from '@/actions/product'
import { getTrendingTemplates } from '@/actions'

type Props = React.PropsWithChildren<{
  total: number
}>

export default async function TrendingTemplates({ total = 0 }: Props) {
  const templates = await getTrendingTemplates(total)

  return templates?.length !== 0 ? (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
      {Array.isArray(templates) &&
        templates.map((template: Product) => (
          <ProductBox
            key={template.id.toString()}
            product={template}
            link={`/product/${template.slug}`}
          />
        ))}
    </div>
  ) : null
}
