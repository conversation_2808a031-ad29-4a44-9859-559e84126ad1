'use client'
import { useState } from 'react'
import { <PERSON><PERSON><PERSON><PERSON> } from 'react-icons/si'

import { TransferSendStatus, setTransferStatus } from '@/actions/transfer'
import ShortUrl from '@/components/common/ShortUrl'
import { Button } from '@/components/ui/button'

interface Props {
  transferId: string
  remixUrl: string
}

export default function RevealFramerLink({ transferId, remixUrl }: Props) {
  const [reveal, setReveal] = useState(false)

  const revealUrlHandler = async () => {
    if (transferId) {
      await setTransferStatus(transferId, TransferSendStatus.COMPLETE)
    }
    setReveal(true)
  }

  return !reveal ? (
    <Button variant="outline" size="xs" onClick={revealUrlHandler}>
      <SiFramer />
      Remix URL
    </Button>
  ) : (
    <>{remixUrl ? <ShortUrl url={remixUrl} /> : null}</>
  )
}
