import { Crown } from 'lucide-react'

import CreatorsSection from '@/components/common/CreatorsSection'
import { getTopCreators } from '@/actions'

type Props = React.PropsWithChildren<{
  total?: number
}>

export default async function TopCreatorsSection({ total = 2 }: Props) {
  const creators = await getTopCreators(total)

  return (
    <CreatorsSection
      title="Top Creators"
      icon={Crown}
      iconColor="text-yellow-500"
      creators={creators ?? []}
      stats={['contributions', 'relation']}
    />
  )
}
