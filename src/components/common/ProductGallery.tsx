import Image from 'next/image'

import { ImageSet } from '@/actions/image'
import { FileSet } from '@/actions/file'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import MediaBox from './MediaBox'

type Props = React.PropsWithChildren<{
  imageSet?: ImageSet
  fileSet?: FileSet
}>

const WIDTH = 1080
const HEIGHT = 675

export default function ProductGallery({ imageSet, fileSet }: Props) {
  const mediaSet = [
    ...(fileSet?.files ?? []).filter(
      file =>
        file.mimeType?.startsWith('video/') ||
        file.mimeType?.startsWith('image/'),
    ),
    ...(imageSet?.images ?? [])
      .filter(image => image.url)
      .map(item => ({ ...item, mimeType: 'image/' })),
  ]

  return mediaSet ? (
    <div className="flex flex-col gap-4">
      {mediaSet?.map((media, index) =>
        media.url ? (
          <div key={index} className="overflow-hidden border-0">
            <MediaBox
              media={media}
              ratio={4 / 3}
              width={WIDTH}
              height={HEIGHT}
            />
          </div>
        ) : null,
      )}
    </div>
  ) : null
}
