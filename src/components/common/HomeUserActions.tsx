import { Cog, Gift, Plus, Users2 } from 'lucide-react'

const userActions = [
  {
    id: 'invite-friends',
    icon: Users2,
    title: 'Invite 3 designer friends',
    description: 'Grow your network and discover amazing creators',
    bgColor: 'bg-blue-100',
    iconColor: 'text-blue-600',
  },
  {
    id: 'creator-account',
    icon: Cog,
    title: 'Set up your creator account',
    description: 'Start monetizing your design skills',
    bgColor: 'bg-green-100',
    iconColor: 'text-green-600',
  },
  {
    id: 'add-product',
    icon: Plus,
    title: 'Add your first product',
    description: 'Upload templates, components, or bundles',
    bgColor: 'bg-purple-100',
    iconColor: 'text-purple-600',
  },
  {
    id: 'weekly-gift',
    icon: Gift,
    title: 'Claim your weekly free gift',
    description: "Get this week's free template or resource",
    bgColor: 'bg-yellow-100',
    iconColor: 'text-yellow-600',
  },
]

export default function HomeUserActions() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-8">
      {userActions.map(action => {
        const IconComponent = action.icon

        return (
          <div
            key={action.id}
            className="rounded-lg border border-gray-100 bg-card text-card-foreground transition-all cursor-pointer hover:border-neutral-200"
          >
            <div className="p-6 flex items-start space-x-4">
              <div
                className={`size-8 ${action.bgColor} rounded-full flex items-center justify-center`}
              >
                <IconComponent className={`size-3 ${action.iconColor}`} />
              </div>
              <div className="flex-1 gap-2">
                <h3 className="text-sm font-semibold text-gray-900">
                  {action.title}
                </h3>
                <p className="text-xs text-gray-500">{action.description}</p>
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}
