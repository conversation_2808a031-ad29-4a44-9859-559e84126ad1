import { cn } from '@/lib/utils'
import React from 'react'

interface Props extends React.HTMLAttributes<HTMLPreElement> {
  children?: React.ReactNode
}

export default React.forwardRef<HTMLPreElement, Props>(function CodeBox(
  { children, className, ...props },
  ref,
) {
  return (
    <pre
      ref={ref}
      style={{
        wordBreak: 'break-all',
        overflowWrap: 'anywhere',
        whiteSpace: 'pre-wrap',
      }}
      className={cn(
        `w-[max-content] max-w-[40rem] p-2 border-[1px] bg-foreground/5 border-muted-foreground/25 text-foreground rounded-md outline-none focus:outline-none focus:border-accent`,
        className,
      )}
      {...props}
    >
      {children}
    </pre>
  )
})
