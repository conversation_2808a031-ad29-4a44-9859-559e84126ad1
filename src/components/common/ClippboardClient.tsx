'use client'

import { Check, ClipboardCopy } from 'lucide-react'
import { Button, ButtonProps } from '@/components/ui/button'
import { useEffect, useRef, useState } from 'react'

type Props = React.PropsWithChildren<{
  valueToCopy: string
  variant?: ButtonProps['variant']
  size?: ButtonProps['size']
  className?: string
  onlyChildren?: boolean
}>

export default function ClippboardClient({
  valueToCopy,
  className,
  children,
  onlyChildren = false,
  variant = 'ghost',
  size = 'xs',
}: Props) {
  const timer = useRef<NodeJS.Timeout | null>(null)
  const [isCopied, setIsCopied] = useState(false)

  const handleCopy = () => {
    navigator.clipboard.writeText(valueToCopy)
    setIsCopied(true)
    timer.current = setTimeout(() => setIsCopied(false), 2000)
  }

  useEffect(() => {
    return () => {
      if (timer.current) {
        clearTimeout(timer.current)
      }
    }
  }, [])

  return (
    <Button
      size={size}
      variant={variant}
      className={className}
      onClick={handleCopy}
    >
      {onlyChildren && children ? (
        (children ??
        (isCopied ? <Check className="text-green-600" /> : <ClipboardCopy />))
      ) : (
        <>
          {children}
          {isCopied ? <Check className="text-green-600" /> : <ClipboardCopy />}
        </>
      )}
    </Button>
  )
}
