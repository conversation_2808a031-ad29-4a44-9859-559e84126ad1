import { Asterisk, ChevronRight } from 'lucide-react'

import { cn } from '@/lib/utils'
import {
  CollapsibleContent,
  Collapsible,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import { SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar'
import TemplateSidebarItemLink from '@/components/common/TemplateSidebarItemLink'
import { MenuItem } from '@/actions/get-menu-items'

type Props = React.PropsWithChildren<
  MenuItem & {
    subItems?: MenuItem[]
    pathname?: string
  }
>

export default function TemplateSidebarItem({
  icon: Icon,
  label,
  count,
  url,
  subItems,
  pathname,
}: Props) {
  if (!subItems) {
    return (
      <TemplateSidebarItemLink
        icon={Icon ?? Asterisk}
        label={label}
        count={count}
        url={url}
        pathname={pathname}
      />
    )
  }

  const hasActiveChild = subItems.some(item => item.url === pathname)

  return (
    <Collapsible defaultOpen className="group/collapsible">
      <CollapsibleTrigger asChild>
        <SidebarMenuItem>
          <SidebarMenuButton
            className={cn(
              'flex items-center w-full px-4 py-2 rounded-lg transition-colors text-gray-500',
              'hover:bg-gray-100 text-gray-600',
              hasActiveChild && 'bg-gray-50',
            )}
          >
            {Icon !== undefined && (
              <Icon
                className={cn(
                  'h-4 w-4',
                  hasActiveChild ? 'text-gray-900' : 'text-gray-600',
                )}
                strokeWidth={1.25}
              />
            )}{' '}
            <span
              className={cn(
                'text-sm font-medium flex-1 text-left',
                hasActiveChild && 'text-gray-900',
              )}
            >
              {label}
            </span>
            <ChevronRight
              className="!h-3 !w-3 text-gray-500 ml-auto transition-transform group-data-[state=open]/collapsible:rotate-90"
              strokeWidth={1.25}
            />
          </SidebarMenuButton>
        </SidebarMenuItem>
      </CollapsibleTrigger>
      <CollapsibleContent className="relative ml-6 flex flex-col gap-y-2 group-data-[state=open]/collapsible:mt-2">
        <div
          className={cn(
            'absolute left-0 top-2 bottom-2 w-px',
            hasActiveChild ? 'bg-gray-400/40' : 'bg-gray-200',
          )}
        />
        {subItems?.map((item, index) => (
          <TemplateSidebarItemLink
            key={item.label}
            pathname={pathname}
            {...item}
          />
        ))}
      </CollapsibleContent>
    </Collapsible>
  )
}
