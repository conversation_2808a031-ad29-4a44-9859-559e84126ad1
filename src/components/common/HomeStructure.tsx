'use client'

import useSessionUser from '@/hooks/useSessionUser'
import useUser from '@/hooks/useUser'
import Image from 'next/image'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import HomeUserActions from '@/components/common/HomeUserActions'
import YourRecentPurchases from '@/components/common/YourRecentPurchases'

export default function HomeStructure() {
  // const { data: userSession } = useSessionUser()
  // const userId = userSession?.id ? String(userSession.id) : ''
  // const { data: user } = useUser(userId)

  return (
    <>
      {/* <div className="flex items-center justify-between">
        <div className="flex gap-4 items-center justify-center">
          {user?.avatar ? (
            <Image
              src={user?.avatar}
              className="aspect-square size-12 rounded-full"
              alt={`${user?.profile?.firstName} ${user?.profile?.lastName} Avatar`}
              priority={false}
              loading="lazy"
              height={48}
              width={48}
            />
          ) : (
            <Image
              src="/assets/user-profile-example.png"
              height={48}
              width={48}
              alt="Default Avatar"
              className="aspect-square size-12 rounded-full"
              loading="lazy"
              priority={false}
            />
          )}
          <h1 className="text-xl sm:text-2xl font-semibold">
            Welcome, {user?.profile?.firstName} {user?.profile?.lastName}
          </h1>
        </div>

        <Link href="/user/settings">
          <Button
            variant="secondary"
            className="gap-2 bg-gray-100 text-gray-700 hover:bg-gray-200 text-xs h-10 px-4 py-2"
          >
            Edit Profile
            <span className="sr-only">Edit your profile</span>
          </Button>
        </Link>
      </div>

      <Separator className="my-6" />

      {process.env.NODE_ENV === 'development' && <HomeUserActions />}

      <YourRecentPurchases /> */}
    </>
  )
}
