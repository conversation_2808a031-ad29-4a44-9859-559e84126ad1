'use client'

import React from 'react'
import { cn } from '@/lib/utils'

type Tag = {
  id: string
  name: string
}

type Props = PropsWithClassName<{
  tags: Tag[]
  selectedTags: string[]
  onTagToggle: (tagId: string) => void
}>

export default function TagSelector({
  tags,
  selectedTags,
  onTagToggle,
  className,
}: Props) {
  const defaultColor =
    'bg-muted text-muted-foreground hover:bg-muted-foreground/10'

  return (
    <div className={cn('flex flex-wrap gap-1', className)}>
      {tags.map(tag => (
        <button
          key={tag.id}
          onClick={() => onTagToggle(tag.id)}
          className={cn(
            'flex items-center gap-1 px-2 py-1 rounded-full text-xs transition-colors',
            selectedTags.includes(tag.id)
              ? 'bg-blue-200 text-blue-600 hover:bg-blue-300'
              : defaultColor,
          )}
        >
          <span
            className={cn(
              'w-1.5 h-1.5 rounded-full',
              selectedTags.includes(tag.id) ? 'bg-blue-600' : 'bg-gray-400',
            )}
          />
          {tag.name}
        </button>
      ))}
    </div>
  )
}
