import trimDouble from "@/utils/trim-double";
import React from "react";

export const enum ChipStatus {
  STARTED = "STARTED",
  PENDING = "PENDING",
  SUCCESS = "SUCCESS",
  FAILED = "FAILED",
  WAITING = "WAITING",
  UNKNOWN = "UNKNOWN",
}

interface Props extends React.HTMLAttributes<HTMLSpanElement> {
  children?: React.ReactNode;
  status: ChipStatus;
}

export default React.forwardRef<HTMLSpanElement, Props>(function Chip(
  { children, status = ChipStatus.WAITING, ...props },
  ref
) {
  const statusMapChip = {
    [ChipStatus.STARTED]: "bg-blue-200 text-blue-500",
    [ChipStatus.PENDING]: "bg-purple-200 text-purple-500",
    [ChipStatus.SUCCESS]: "bg-green-200 text-green-600",
    [ChipStatus.FAILED]: "bg-red-200 text-red-500",
    [ChipStatus.WAITING]: "bg-gray-200 text-gray-500",
    [ChipStatus.UNKNOWN]: "bg-gray-600 text-gray-300",
  };

  const statusMapLight = {
    [ChipStatus.STARTED]: "bg-blue-500",
    [ChipStatus.PENDING]: "bg-purple-500",
    [ChipStatus.SUCCESS]: "bg-green-600",
    [ChipStatus.FAILED]: "bg-red-500",
    [ChipStatus.WAITING]: "bg-gray-500",
    [ChipStatus.UNKNOWN]: "bg-gray-300",
  };

  const classNameChip = trimDouble(
    `flex gap-1 items-center justify-center w-fit h-[1rem] px-[0.7rem] py-[0.7rem] rounded-full disabled:opacity-50 disabled:cursor-not-allowed text-center text-[0.65rem] whitespace-nowrap ${statusMapChip[status]}`
  );

  const classNameLight = trimDouble(
    `rounded-full w-[0.4rem] h-[0.4rem] ${statusMapLight[status]}`
  );

  return (
    <span ref={ref} {...props} className={classNameChip}>
      <span className={classNameLight} />
      {children}
    </span>
  );
});
