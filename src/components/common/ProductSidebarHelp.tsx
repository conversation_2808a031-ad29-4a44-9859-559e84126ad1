import { Button } from '@/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Flag, Mail, MoreVertical } from 'lucide-react'

export default function ProductSidebarHelp() {
  return (
    <div className="mt-auto py-4 border-t">
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-500">Need help?</p>
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent align="end" className="w-56 p-0">
            <div className="flex flex-col">
              <Button
                variant="ghost"
                className="flex items-center gap-2 justify-start h-9 px-3 rounded-none hover:bg-gray-50"
              >
                <Mail className="w-4 h-4" />
                Contact Creator
              </Button>
              <Button
                variant="ghost"
                className="flex items-center gap-2 justify-start h-9 px-3 rounded-none text-red-600 hover:bg-red-50 hover:text-red-700"
              >
                <Flag className="w-4 h-4" />
                Report a Problem
              </Button>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  )
}
