import { useState, ChangeEvent, useRef, useCallback } from 'react'
import { uploadFile } from '@/components/common/DropUpload'
import { Camera, User, Trash2, Check, LoaderCircle } from 'lucide-react'
import { Button } from '../ui/button'
import Image from 'next/image'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '../ui/dialog'

interface CropArea {
  x: number
  y: number
  width: number
  height: number
}

export function AvatarUploadWithCrop({
  onUpload,
  onDelete,
  currentAvatar,
  showDeleteButton = true,
}: {
  onUpload: (url: string) => void
  onDelete?: () => void
  currentAvatar?: string | null
  showDeleteButton?: boolean
}) {
  const [preview, setPreview] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showCropModal, setShowCropModal] = useState(false)
  const [cropImage, setCropImage] = useState<string | null>(null)
  const [cropArea, setCropArea] = useState<CropArea>({
    x: 0,
    y: 0,
    width: 200,
    height: 200,
  })
  const [isDragging, setIsDragging] = useState(false)
  const [isResizing, setIsResizing] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [imageScale, setImageScale] = useState({ scaleX: 1, scaleY: 1 })
  const [imageDisplaySize, setImageDisplaySize] = useState({
    width: 0,
    height: 0,
  })
  const [imageOffset, setImageOffset] = useState({ left: 0, top: 0 })
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  const handleFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // Check if the file is a GIF
      if (file.type === 'image/gif') {
        setError(
          'GIF files are not allowed. Please use JPEG, PNG, or WebP format.',
        )
        return
      }

      // Check file size (3MB = 3 * 1024 * 1024 bytes)
      const maxSize = 3 * 1024 * 1024 // 3MB in bytes
      if (file.size > maxSize) {
        setError(
          'File size must be less than 3MB. Please choose a smaller image.',
        )
        return
      }

      // Show crop modal
      const imageUrl = URL.createObjectURL(file)
      setCropImage(imageUrl)
      setShowCropModal(true)
      setError(null)
    }
  }

  const handleCropConfirm = async () => {
    if (!cropImage || !canvasRef.current || !imageRef.current) return

    setIsUploading(true)
    setError(null)

    try {
      const canvas = canvasRef.current
      const ctx = canvas.getContext('2d')
      if (!ctx) throw new Error('Failed to get canvas context')

      // Calculate final resolution (minimum 500x500, maximum 1000x1000 for optimization)
      const calculatedSize = Math.max(
        cropArea.width * imageScale.scaleX,
        cropArea.height * imageScale.scaleY,
      )
      const finalSize = Math.min(1000, Math.max(500, calculatedSize))

      // Resize canvas to final size
      canvas.width = finalSize
      canvas.height = finalSize

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Calculate real image coordinates (considering scale)
      const realCropArea = {
        x: Math.round(cropArea.x * imageScale.scaleX),
        y: Math.round(cropArea.y * imageScale.scaleY),
        width: Math.round(cropArea.width * imageScale.scaleX),
        height: Math.round(cropArea.height * imageScale.scaleY),
      }

      // Draw the cropped area
      ctx.drawImage(
        imageRef.current,
        realCropArea.x,
        realCropArea.y,
        realCropArea.width,
        realCropArea.height,
        0,
        0,
        finalSize,
        finalSize,
      )

      // Convert canvas to blob
      canvas.toBlob(
        async blob => {
          if (!blob) {
            setError('Failed to process image. Please try again.')
            setIsUploading(false)
            return
          }

          // Create file from blob
          const croppedFile = new File([blob], 'avatar.jpg', {
            type: 'image/jpeg',
          })

          const meta = {
            name: croppedFile.name,
            size: croppedFile.size,
            type: croppedFile.type,
            lastModified: croppedFile.lastModified,
          }

          try {
            const fileRecord = await uploadFile(croppedFile, 'avatars', meta)
            setPreview(URL.createObjectURL(croppedFile))

            if (fileRecord?.url) {
              onUpload(fileRecord.url)
            }
          } catch (err) {
            setError('Failed to upload avatar. Please try again.')
          } finally {
            setIsUploading(false)
            setShowCropModal(false)
            setCropImage(null)
          }
        },
        'image/jpeg',
        0.8,
      )
    } catch (err) {
      setError('Failed to process image. Please try again.')
      setIsUploading(false)
    }
  }

  const handleCropCancel = () => {
    setShowCropModal(false)
    if (cropImage) {
      URL.revokeObjectURL(cropImage)
      setCropImage(null)
    }
  }

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!imageRef.current) return

    const rect = imageRef.current.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    // Allow drag whenever clicking on the crop area
    setIsDragging(true)
    setDragStart({ x, y })
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!imageRef.current) return

    const rect = imageRef.current.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    const deltaX = x - dragStart.x
    const deltaY = y - dragStart.y

    // If resizing
    if (isResizing) {
      // Calculate the larger delta to maintain square format
      const delta = Math.max(Math.abs(deltaX), Math.abs(deltaY))
      // Determine direction based on mouse movement
      // If mouse moves right/down, increase size
      // If mouse moves left/up, decrease size
      const sign = deltaX + deltaY > 0 ? 1 : -1

      setCropArea(prev => {
        // Use stored display dimensions of the image
        const imageWidth = imageDisplaySize.width
        const imageHeight = imageDisplaySize.height

        // Calculate new size based on accumulated delta
        // Use a sensitivity factor to make resize more responsive
        const sensitivity = 2
        const newSize = Math.max(
          50,
          Math.min(
            prev.width + delta * sign * sensitivity,
            Math.min(imageWidth, imageHeight),
          ),
        )

        // Keep the center of the crop area when resizing
        const centerX = prev.x + prev.width / 2
        const centerY = prev.y + prev.height / 2

        // Calculate new position maintaining center
        let newX = centerX - newSize / 2
        let newY = centerY - newSize / 2

        // Apply limits to keep area within the image
        if (newX < 0) newX = 0
        if (newY < 0) newY = 0
        if (newX + newSize > imageWidth) newX = imageWidth - newSize
        if (newY + newSize > imageHeight) newY = imageHeight - newSize

        return {
          x: newX,
          y: newY,
          width: newSize,
          height: newSize,
        }
      })
    }
    // If dragging
    else if (isDragging) {
      setCropArea(prev => {
        // Use stored display dimensions of the image
        const imageWidth = imageDisplaySize.width
        const imageHeight = imageDisplaySize.height

        // Calculate new position without restrictions first
        let newX = prev.x + deltaX
        let newY = prev.y + deltaY

        // Apply limits only if necessary
        if (newX < 0) newX = 0
        if (newY < 0) newY = 0
        if (newX + prev.width > imageWidth) newX = imageWidth - prev.width
        if (newY + prev.height > imageHeight) newY = imageHeight - prev.height

        return {
          ...prev,
          x: newX,
          y: newY,
        }
      })
    }

    setDragStart({ x, y })
  }

  const handleMouseUp = () => {
    setIsDragging(false)
    setIsResizing(false)
  }

  const handleResizeStart = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (!imageRef.current) return

    const rect = imageRef.current.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    setIsResizing(true)
    setIsDragging(false) // Ensure not dragging
    setDragStart({ x, y })
  }

  const handleDelete = async () => {
    if (!onDelete) return

    setIsDeleting(true)
    setError(null)
    try {
      await onDelete()
      setPreview(null)
    } catch (err) {
      setError('Failed to delete avatar. Please try again.')
    } finally {
      setIsDeleting(false)
    }
  }

  const hasAvatar = currentAvatar || preview

  return (
    <>
      <div className="flex items-center gap-4">
        <div className="relative w-32 h-32">
          <span className="relative flex overflow-hidden rounded-full h-32 w-32 bg-gray-50 items-center justify-center">
            {preview ? (
              <Image
                src={preview}
                width={128}
                height={128}
                alt="Avatar Preview"
                className="object-cover size-full rounded-full"
                loading="lazy"
                priority={false}
              />
            ) : currentAvatar ? (
              <Image
                src={currentAvatar}
                width={128}
                height={128}
                alt="Current Avatar"
                className="object-cover size-full rounded-full"
                loading="lazy"
                priority={false}
              />
            ) : (
              <Image
                src="/assets/user-profile-example.png"
                width={128}
                height={128}
                alt="Default Avatar"
                className="object-cover size-full rounded-full"
                loading="lazy"
                priority={false}
              />
            )}
          </span>

          <div className="overflow-hidden hover:opacity-100 bg-black/50 backdrop-blur-sm absolute inset-0 flex items-center justify-center rounded-full cursor-pointer transition-all duration-200 opacity-0">
            <div className="flex items-center gap-3">
              <label
                htmlFor="avatar-upload"
                className="flex gap-3 items-center justify-center text-white cursor-pointer hover:text-gray-200 transition-colors w-32 h-32"
              >
                <div className="flex flex-col items-center gap-1">
                  <Camera className="h-5 w-5" />
                  <span className="text-xs font-medium">Change</span>
                </div>
                {showDeleteButton && hasAvatar && onDelete && (
                  <button
                    onClick={handleDelete}
                    disabled={isUploading || isDeleting}
                    className="flex flex-col items-center gap-1 text-white cursor-pointer hover:text-red-300 transition-colors"
                  >
                    {isDeleting ? (
                      <LoaderCircle className="h-5 w-5 animate-spin" />
                    ) : (
                      <>
                        <Trash2 className="h-5 w-5" />
                        <span className="text-xs font-medium">Delete</span>
                      </>
                    )}
                  </button>
                )}
              </label>

              <input
                id="avatar-upload"
                type="file"
                accept="image/jpeg,image/jpg,image/png,image/webp"
                onChange={handleFileChange}
                className="hidden"
                disabled={isUploading || isDeleting}
              />
            </div>
          </div>
        </div>

        {error && (
          <div className="text-xs text-red-600 w-full" role="alert">
            {error}
          </div>
        )}
      </div>

      {/* Crop Dialog */}
      <Dialog
        open={showCropModal}
        onOpenChange={open => {
          if (!open) {
            handleCropCancel()
          }
        }}
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Crop Image</DialogTitle>
          </DialogHeader>

          <div
            ref={containerRef}
            className="relative mb-4 overflow-hidden rounded-lg border bg-gray-100 flex items-center justify-center"
            style={{ minHeight: '400px', position: 'relative' }}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
          >
            {cropImage && (
              <img
                ref={imageRef}
                src={cropImage}
                alt="Crop preview"
                className="block"
                onLoad={() => {
                  if (imageRef.current && containerRef.current) {
                    const img = imageRef.current
                    const container = containerRef.current

                    // Calculate maximum size the image can have in the container
                    const maxWidth = container.clientWidth - 40 // padding
                    const maxHeight = 400 // fixed maximum height

                    // Calculate image dimensions maintaining aspect ratio
                    const imgAspectRatio = img.naturalWidth / img.naturalHeight
                    let displayWidth = img.naturalWidth
                    let displayHeight = img.naturalHeight

                    if (displayWidth > maxWidth) {
                      displayWidth = maxWidth
                      displayHeight = maxWidth / imgAspectRatio
                    }

                    if (displayHeight > maxHeight) {
                      displayHeight = maxHeight
                      displayWidth = maxHeight * imgAspectRatio
                    }

                    // Apply calculated dimensions
                    img.style.width = `${displayWidth}px`
                    img.style.height = `${displayHeight}px`

                    // Calculate scale between natural and displayed image
                    const scaleX = img.naturalWidth / displayWidth
                    const scaleY = img.naturalHeight / displayHeight
                    setImageScale({ scaleX, scaleY })

                    // Store image display dimensions
                    setImageDisplaySize({
                      width: displayWidth,
                      height: displayHeight,
                    })

                    // Calculate offset of centered image in container
                    const offsetLeft =
                      (container.clientWidth - displayWidth) / 2
                    const offsetTop =
                      (container.clientHeight - displayHeight) / 2
                    setImageOffset({ left: offsetLeft, top: offsetTop })

                    // Calculate crop area based on displayed image
                    const size = Math.min(displayWidth, displayHeight) * 0.6 // 60% of smaller size to allow more movement
                    const cropArea = {
                      x: (displayWidth - size) / 2,
                      y: (displayHeight - size) / 2,
                      width: size,
                      height: size,
                    }

                    setCropArea(cropArea)
                  }
                }}
                style={{
                  userSelect: 'none',
                  pointerEvents: 'none',
                }}
              />
            )}

            {/* Crop overlay */}
            {cropImage && (
              <div
                className="absolute border-2 border-white shadow-lg bg-white/10"
                style={{
                  left: cropArea.x + imageOffset.left,
                  top: cropArea.y + imageOffset.top,
                  width: cropArea.width,
                  height: cropArea.height,
                  pointerEvents: 'auto',
                  cursor: isDragging ? 'grabbing' : 'grab',
                }}
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
              >
                {/* Resize handles */}
                <div
                  className="absolute -top-2 -left-2 w-6 h-6 bg-white border-2 border-blue-500 rounded-full cursor-nw-resize hover:bg-blue-50 transition-colors"
                  onMouseDown={handleResizeStart}
                  onMouseMove={handleMouseMove}
                  onMouseUp={handleMouseUp}
                  style={{ pointerEvents: 'auto' }}
                />
                <div
                  className="absolute -top-2 -right-2 w-6 h-6 bg-white border-2 border-blue-500 rounded-full cursor-ne-resize hover:bg-blue-50 transition-colors"
                  onMouseDown={handleResizeStart}
                  onMouseMove={handleMouseMove}
                  onMouseUp={handleMouseUp}
                  style={{ pointerEvents: 'auto' }}
                />
                <div
                  className="absolute -bottom-2 -left-2 w-6 h-6 bg-white border-2 border-blue-500 rounded-full cursor-sw-resize hover:bg-blue-50 transition-colors"
                  onMouseDown={handleResizeStart}
                  onMouseMove={handleMouseMove}
                  onMouseUp={handleMouseUp}
                  style={{ pointerEvents: 'auto' }}
                />
                <div
                  className="absolute -bottom-2 -right-2 w-6 h-6 bg-white border-2 border-blue-500 rounded-full cursor-se-resize hover:bg-blue-50 transition-colors"
                  onMouseDown={handleResizeStart}
                  onMouseMove={handleMouseMove}
                  onMouseUp={handleMouseUp}
                  style={{ pointerEvents: 'auto' }}
                />
              </div>
            )}
          </div>

          <p className="text-sm text-gray-600 mb-4">
            Drag the crop area to move it, or use the corner handles to resize.
            The crop area will always remain square.
          </p>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={handleCropCancel}
              disabled={isUploading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCropConfirm}
              disabled={isUploading}
              className="flex items-center gap-2"
            >
              {isUploading ? (
                <>Uploading...</>
              ) : (
                <>
                  <Check className="h-4 w-4" />
                  Confirm
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Hidden canvas for processing */}
      <canvas ref={canvasRef} width={2000} height={2000} className="hidden" />
    </>
  )
}
