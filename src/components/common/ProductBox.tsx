'use client'

import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { Product } from '@/actions/product'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import centsToDollars from '@/utils/cents-to-dollars'
import { PaymentPrice } from '@/actions/price'
import { User } from '@/actions/user'
import c from 'clsx'
import { getPlatform } from '@/utils/get-platform'
import Link from 'next/link'
import PlatformWidget from '@/components/products/PlatformWidget'
import MediaBox, { MediaInABox } from './MediaBox'

type Props = {
  product: Product
  link: string
  hidePrice?: boolean
  hideLink?: boolean
}

export default function ProductBox({
  product,
  link,
  hidePrice,
  hideLink,
}: Props) {
  const router = useRouter()
  const [isHovered, setIsHovered] = useState(false)

  const clickHandler = () => {
    router.push(link)
  }

  const mediaFiles =
    product.fileSet?.files?.filter(
      file =>
        file.mimeType?.startsWith('video/') ||
        file.mimeType?.startsWith('image/'),
    ) || []

  const primaryMedia = mediaFiles[0]
  const secondaryMedia = mediaFiles[1]
  const hasMultipleMedia = mediaFiles.length > 1

  const figma = product.fileSet?.files?.find(file =>
    file.flair?.includes('figma'),
  )

  return (
    <Card
      className={c('group border-0 relative', !hideLink && 'cursor-pointer')}
    >
      <CardContent className="p-0">
        <div
          className="relative rounded-md mb-4 overflow-hidden transition-all w-full aspect-[4/3]"
          onClick={hideLink ? undefined : clickHandler}
          onMouseEnter={() => hasMultipleMedia && setIsHovered(true)}
          onMouseLeave={() => hasMultipleMedia && setIsHovered(false)}
        >
          <div
            className={c(
              'absolute inset-0 transition-opacity z-20',
              hasMultipleMedia && isHovered ? 'opacity-0' : 'opacity-100',
            )}
          >
            <MediaBox
              media={primaryMedia as MediaInABox}
              ratio={4 / 3}
              width={426 * 2}
              height={328 * 2}
            />
          </div>

          {hasMultipleMedia && (
            <div className="absolute inset-0">
              <MediaBox
                media={secondaryMedia as MediaInABox}
                ratio={4 / 3}
                width={426 * 2}
                height={328 * 2}
              />
            </div>
          )}

          {figma && (
            <div
              className={c(
                'absolute top-2 left-2',
                hasMultipleMedia ? 'z-10' : 'z-20',
              )}
            >
              <Badge
                variant="secondary"
                className="bg-white/90 text-gray-700 hover:bg-gray-100/80 text-xs font-medium flex items-center gap-1.5 px-2.5 py-0.5 backdrop-blur border border-gray-200 hover:text-gray-800 transition-all"
              >
                <Image
                  src="/assets/figma-icon.svg"
                  alt="Figma Icon"
                  className="w-2 h-3"
                  loading="lazy"
                  height={12}
                  width={8}
                />
                Figma available
              </Badge>
            </div>
          )}
        </div>

        <div className="grid grid-cols-[auto_1fr_auto] grid-rows-[auto_auto] items-center justify-between gap-x-2 gap-y-0">
          <div className="size-5">
            <PlatformWidget
              size="1.25"
              platform={getPlatform(product.platforms)}
              justIcon
            />
          </div>

          <h3 className="text-sm font-medium truncate">{product.name}</h3>

          {!hidePrice && (
            <div className="text-sm font-medium ml-2 flex-shrink-0">
              {product.price?.amount === 0 ? (
                <Badge
                  variant="secondary"
                  className="bg-green-50 text-green-700 hover:bg-green-50 border-0"
                >
                  Free
                </Badge>
              ) : (
                centsToDollars((product?.price as PaymentPrice)?.amount)
              )}
            </div>
          )}

          {(product?.owner as User)?.handler && (
            <Link
              className="text-xs col-start-2 text-muted-foreground hover:text-primary transition-colors cursor-pointer"
              href={`/user/${(product.owner as User).handler}`}
            >
              @{(product.owner as User).handler}
            </Link>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
