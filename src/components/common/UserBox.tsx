'use client'

import React, { useMemo, useCallback } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { signOut } from 'next-auth/react'
import { LogOut, Settings, User } from 'lucide-react'
import { deleteSession } from '@/actions/auth/delete-session'
import { Avatar } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { DEFAULT_AVATAR, VERCEL_ENV } from '@/config'
import useSessionUser from '@/hooks/useSessionUser'
import useUserHats from '@/hooks/useUserHats'
import useUser from '@/hooks/useUser'

type Props = React.PropsWithChildren<{
  side?: 'left' | 'right' | 'bottom' | 'top'
  avatarWithName?: boolean
}>

const UserBoxSkeleton = () => (
  <div className="gap-2 flex justify-center items-center rounded-md m-0 my-2 p-0 w-full h-auto h-[2.25rem] w-[2.25rem] pl-[0.125rem] pr-[0.125rem]">
    <div className="rounded-full bg-neutral-200 h-[2rem] w-[2rem] animate-pulse" />
  </div>
)

export default function UserBox({ side, avatarWithName = false }: Props) {
  const { data: userSession } = useSessionUser()
  const userId = userSession?.id ? String(userSession.id) : ''
  const { data: user, isLoading } = useUser(userId)
  const { isMember } = useUserHats()
  const router = useRouter()

  const handleLogout = useCallback(async () => {
    try {
      await signOut({
        redirect: false,
      })

      await deleteSession()

      router.replace('/login')
    } catch (error) {
      console.error('Error during logout:', error)

      await signOut({
        redirect: false,
      })

      router.replace('/login')
    }
  }, [router])

  const firstLetterFromFirstName = useMemo(() => {
    if (!user?.profile?.firstName) {
      return user?.profile?.name?.charAt(0).toUpperCase() || ''
    }

    return user.profile.firstName.charAt(0).toUpperCase()
  }, [user?.profile?.firstName, user?.profile?.name])

  const firstLetterFromLastName = useMemo(() => {
    return user?.profile?.lastName
      ? user.profile.lastName.charAt(0).toUpperCase()
      : ''
  }, [user?.profile?.lastName])

  const userDisplayName = useMemo(() => {
    if (!user) return ''
    return `${user.profile?.firstName || user.profile?.name} ${user.profile?.lastName ? user.profile?.lastName : ''}`
  }, [user])

  const userAvatar = useMemo(() => {
    return user?.avatar || DEFAULT_AVATAR
  }, [user?.avatar])

  if (isLoading) {
    return <UserBoxSkeleton />
  }

  return userSession ? (
    avatarWithName ? (
      <Link href={`/user/${user?.handler}`}>
        <div className="flex items-center justify-center">
          {user?.avatar ? (
            <Avatar className="h-[2rem] w-[2rem] mr-3 ">
              <Image
                src={userAvatar}
                alt={user?.name ?? ''}
                width={32}
                height={32}
                className="rounded-full object-cover"
                loading="lazy"
                priority={false}
              />
            </Avatar>
          ) : (
            <div className="rounded-full bg-neutral-200 h-[2rem] w-[2rem] flex justify-center items-center gap-[1px] text-sm mr-3">
              <span>{firstLetterFromFirstName}</span>
              <span>{firstLetterFromLastName}</span>
            </div>
          )}
          <div className="flex flex-col gap-1 text-left">
            <p className="text-base font-medium">{`${user?.profile?.firstName || user?.profile?.name} ${user?.profile?.lastName ? user?.profile?.lastName : ''}`}</p>
          </div>
        </div>
      </Link>
    ) : (
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="flex justify-center items-center rounded-md m-0 my-2 p-0 w-full h-auto h-[2.25rem] w-[2.25rem]"
          >
            {user?.avatar ? (
              <Avatar className="h-[2rem] w-[2rem] bg-zinc-100">
                <Image
                  src={userAvatar}
                  alt={user?.name ?? ''}
                  width={32}
                  height={32}
                  className="rounded-full object-cover"
                  loading="lazy"
                  priority={false}
                />
              </Avatar>
            ) : (
              <div className="rounded-full bg-neutral-200 h-[2rem] w-[2rem] flex justify-center items-center gap-[1px] text-sm">
                <span>{firstLetterFromFirstName}</span>
                <span>{firstLetterFromLastName}</span>
              </div>
            )}
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent className="w-56" side={side} align="end">
          <div className="flex items-center justify-start gap-2 p-2">
            {user?.avatar ? (
              <Avatar className="h-[2rem] w-[2rem] bg-zinc-100">
                <Image
                  src={userAvatar}
                  alt={user?.name ?? ''}
                  width={32}
                  height={32}
                  className="rounded-full object-cover"
                  loading="lazy"
                  priority={false}
                />
              </Avatar>
            ) : (
              <div className="rounded-full bg-neutral-200 h-[2rem] w-[2rem] flex justify-center items-center gap-[1px] text-sm">
                <span>{firstLetterFromFirstName}</span>
                <span>{firstLetterFromLastName}</span>
              </div>
            )}
            <div className="flex flex-col text-left">
              <p className="text-sm font-medium">{userDisplayName}</p>
              <p
                className={`inline-flex items-center rounded border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-secondary/80 text-[10px] px-1.5 py-0 ${isMember ? 'bg-zinc-100 text-zinc-700' : 'bg-green-100 text-green-700'} w-fit`}
              >
                {user?.hats?.map(hat => hat.name).join(', ')}
              </p>
            </div>
          </div>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild className="cursor-pointer">
            <Link href={`/user/${user?.handler}`} className="flex gap-2">
              <User className="mr-2 h-4 w-4" />
              <span>Profile</span>
            </Link>
          </DropdownMenuItem>
          {/* TODO: Add settings page */}
          {VERCEL_ENV !== 'production' && (
            <DropdownMenuItem asChild className="cursor-pointer">
              <Link href={`/user/settings/`} className="flex gap-2">
                <Settings className="mr-2 h-4 w-4" />
                <span>Settings</span>
              </Link>
            </DropdownMenuItem>
          )}
          <DropdownMenuSeparator />
          <DropdownMenuItem className="text-red-600 focus:bg-red-50 focus:text-red-600">
            <Button
              onClick={handleLogout}
              variant={'ghost'}
              className="flex gap-2 p-0 h-fit py-1 w-full text-left justify-start cursor-pointer hover:bg-transparent hover:text-red-600"
            >
              <LogOut className={`mr-2 h-4 w-4`} />
              <span>{'Log out'}</span>
            </Button>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
  ) : null
}
