import { AspectRatio } from '@/components/ui/aspect-ratio'
import { Circle, Files, ChevronUp, ChevronDown } from 'lucide-react'
import { cn } from '@/lib/utils'

type Props = React.PropsWithChildren<{
  name: string
  thumbnail: string
  isBundle: boolean
  isSelected?: boolean
  isTemplateSelection?: boolean
  showBundleDetails?: boolean
  isExpanded?: boolean
  templatesCount?: number
  onClick?: (e: React.MouseEvent) => void
}>

export const TemplateCell = ({
  name,
  thumbnail,
  isBundle,
  isSelected = false,
  isTemplateSelection = false,
  showBundleDetails = false,
  isExpanded = false,
  templatesCount = 0,
  onClick,
}: Props) => {
  return (
    <div
      className={cn(
        'flex items-center gap-3',
        isBundle &&
          showBundleDetails &&
          'cursor-pointer hover:bg-gray-50/50 rounded-md transition-colors',
      )}
      onClick={onClick}
    >
      {isTemplateSelection && (
        <div
          className={cn(
            'w-5 h-5 rounded-full border-2 flex items-center justify-center transition-colors duration-200',
            isSelected
              ? 'border-gray-900 bg-gray-900'
              : 'border-gray-300 hover:border-gray-400',
          )}
        >
          {isSelected && <Circle className="w-3 h-3 text-white fill-current" />}
        </div>
      )}
      <div className="w-16 overflow-hidden rounded relative">
        <AspectRatio ratio={4 / 3} className="bg-gray-100">
          <img
            src={thumbnail}
            alt={name}
            className="object-cover w-full h-full"
          />
        </AspectRatio>
        {isSelected && <div className="absolute inset-0 bg-gray-900/5" />}
      </div>
      <div className="flex items-center gap-3">
        <span className="font-medium">{name}</span>
        {isBundle && showBundleDetails && (
          <div className="flex items-center gap-2 text-gray-600">
            <Files className="w-3.5 h-3.5" />
            <span className="text-xs font-medium">{templatesCount}</span>
            {isExpanded ? (
              <ChevronUp className="w-4 h-4" />
            ) : (
              <ChevronDown className="w-4 h-4" />
            )}
          </div>
        )}
      </div>
    </div>
  )
}
