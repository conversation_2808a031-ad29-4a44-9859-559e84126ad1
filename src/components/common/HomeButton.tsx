import React, { useMemo } from 'react'
import Link from 'next/link'

import trimDouble from '@/utils/trim-double'

type ButtonDesign =
  | 'primary'
  | 'secondary'
  | 'outline'
  | 'invert'
  | 'clean'
  | 'ghost'
  | 'link'
  | 'none'

interface Props
  extends React.ButtonHTMLAttributes<HTMLButtonElement & HTMLAnchorElement> {
  design?: ButtonDesign
  children?: React.ReactNode
  href?: string
  className?: string
  overrideClassName?: boolean
}

export default React.forwardRef<HTMLButtonElement, Props>(function HomeButton(
  {
    design = 'primary',
    children,
    href = '',
    className = '',
    overrideClassName = false,
    ...props
  },
  ref,
) {
  const currentClassName = useMemo(() => {
    if (overrideClassName && className) return className

    let nextClassName = `
      flex items-center justify-center gap-2
      text-sm font-semibold text-center
      py-2.5 px-6
      rounded-[0.25rem]
      ring-1
      h-fit w-fit
      outline-none
      whitespace-nowrap
    `

    if (design === 'secondary') {
      nextClassName = `
        ${nextClassName}
        bg-gray-100 hover:bg-gray-200 active:bg-gray-300
          dark:bg-gray-700 dark:hover:bg-gray-800 dark:active:bg-gray-900
        text-gray-950
          dark:text-gray-100
        ring-gray-100 hover:ring-gray-200 active:ring-gray-300
          dark:ring-gray-700 dark:hover:ring-gray-800 dark:active:ring-gray-900
      `
    } else if (design === 'outline') {
      nextClassName = `
        ${nextClassName}
        bg-background hover:bg-gray-100 active:bg-gray-200
          dark:bg-gray-950 dark:hover:bg-gray-700 dark:active:bg-gray-800
        text-gray-950 hover:text-gray-800 active:text-black
          dark:text-white dark:hover:text-gray-100 dark:active:text-gray-200
        ring-gray-950 hover:ring-gray-800 active:ring-black
          dark:ring-white dark:hover:ring-gray-100 dark:active:ring-gray-200
      `
    } else if (design === 'invert') {
      nextClassName = `
        ${nextClassName}
        dark:bg-gray-950 dark:hover:bg-gray-800 dark:active:bg-black
          bg-gray-100 hover:bg-gray-200 active:bg-background
        dark:text-gray-100 dark:hover:text-gray-200 dark:active:text-white
          text-gray-950 hover:text-gray-800 active:text-black
        dark:ring-gray-950 dark:hover:ring-gray-800 dark:active:ring-black
          ring-gray-100 hover:ring-gray-200 active:ring-white
      `
    } else if (design === 'ghost') {
      nextClassName = `
      ${nextClassName}
      bg-background hover:bg-gray-100 active:bg-gray-200
        dark:bg-gray-950 dark:hover:bg-gray-700 dark:active:bg-gray-800
      text-foreground hover:text-gray-800 active:text-black
        dark:text-white dark:hover:text-gray-100 dark:active:text-gray-200
      ring-transparent hover:ring-gray-800 active:ring-black
        dark:ring-transparent dark:hover:ring-gray-100 dark:active:ring-gray-200
    `
    } else if (design === 'clean') {
      nextClassName = `${nextClassName}
      bg-background hover:bg-gray-100 active:bg-gray-200
        dark:bg-gray-950 dark:hover:bg-gray-700 dark:active:bg-gray-800
      text-foreground hover:text-gray-800 active:text-black
        dark:text-white dark:hover:text-gray-100 dark:active:text-gray-200
      ring-transparent
        dark:ring-transparent
      `
    } else if (design === 'primary') {
      nextClassName = `
        ${nextClassName}
        bg-gray-950 hover:bg-gray-800 active:bg-black
          dark:bg-gray-100 dark:hover:bg-gray-200 dark:active:bg-background
        text-gray-100 hover:text-gray-200 active:text-white
          dark:text-gray-950 dark:hover:text-gray-800 dark:active:text-black
        ring-gray-950 hover:ring-gray-800 active:ring-black
          dark:ring-gray-100 dark:hover:ring-gray-200 dark:active:ring-white
      `
    } else if (design === 'link') {
      nextClassName = `
        relative text-sm font-semibold text-center
        before:absolute before:inset-0 before:h-1 before:bottom-0 before:top-auto 
        before:transparent hover:before:bg-gray-200 active:before:bg-gray-200 before:mix-blend-multiply
          dark:before:bg-transparent dark:hover:before:bg-gray-700 dark:active:before:bg-gray-800 dark:before:mix-blend-screen
        text-foreground hover:text-gray-800 active:text-black
          dark:text-white dark:hover:text-gray-100 dark:active:text-gray-200
        ring-transparent
          dark:ring-transparent
      `
    } else {
      nextClassName = ''
    }

    return trimDouble(`${nextClassName} ${className}`)
  }, [design, className, overrideClassName])

  return href ? (
    <Link
      ref={ref as React.ForwardedRef<HTMLAnchorElement>}
      className={currentClassName}
      href={href}
      {...props}
    >
      {children}
    </Link>
  ) : (
    <button
      ref={ref as React.ForwardedRef<HTMLButtonElement>}
      className={currentClassName}
      {...props}
    >
      {children}
    </button>
  )
})
