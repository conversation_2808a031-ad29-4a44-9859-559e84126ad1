import { formatDistanceToNow } from 'date-fns'
import { LucideIcon } from 'lucide-react'

import { TopCreator, UserRelation, UserRelationString } from '@/actions/user'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Card } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import Link from 'next/link'

type Props = React.PropsWithChildren<{
  title: string
  icon?: LucideIcon
  creators: TopCreator[]
  iconColor?: string
  stats?: ('contributions' | 'joined' | 'relation')[]
}>

export default function CreatorsSection({
  title,
  creators,
  icon: Icon,
  iconColor,
  stats = [],
}: Props) {
  return (
    <Card className="p-6">
      <div className="flex items-center gap-2 mb-4 pl-3">
        {Icon ? (
          <Icon className={cn('w-5 h-5', iconColor ?? 'text-gray-600')} />
        ) : null}
        <h3 className="text-lg font-semibold">{title}</h3>
      </div>

      <div className="flex flex-col gap-2">
        {Array.isArray(creators) &&
          creators?.map(creator => (
            <Link
              key={creator.id.toString()}
              href={`/user/${creator.handler}`}
              className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center w-full justify-between gap-2">
                <Avatar>
                  <AvatarImage src={creator.avatar} alt={creator.name} />
                  <AvatarFallback>{creator.name}</AvatarFallback>
                </Avatar>

                <div className="flex flex-col flex-grow items-start gap-0">
                  <h3 className="font-medium">{`${creator.profile?.firstName} ${creator.profile?.lastName}`}</h3>
                  {stats.includes('contributions') ? (
                    <span className="text-sm text-gray-500">
                      {`${creator.contributions} contributions`}
                    </span>
                  ) : null}
                  {stats.includes('joined') ? (
                    creator.created &&
                    !isNaN(new Date(creator.created).getTime()) ? (
                      <span className="text-sm text-gray-500">
                        {`Joined ${formatDistanceToNow(new Date(creator.created))} ago`}
                      </span>
                    ) : null
                  ) : null}
                </div>

                {stats.includes('relation') ? (
                  <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                    {
                      UserRelationString[
                        creator?.relationStatus ?? UserRelation.FLAT
                      ]
                    }
                  </span>
                ) : null}
              </div>
            </Link>
          ))}
      </div>
    </Card>
  )
}
