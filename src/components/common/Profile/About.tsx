import { User } from '@/actions/user'
import {
  FaBehance,
  FaDribbble,
  FaFacebook,
  FaGithub,
  FaInstagram,
  FaLinkedin,
  FaTwitch,
  FaYoutube,
  FaXTwitter,
} from 'react-icons/fa6'
import { getSkills } from '@/data/skill/get-skills'
import { getSessionUser } from '@/actions/auth'
import { FileText, UserIcon } from 'lucide-react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'

type Props = React.PropsWithChildren<{
  user: User
}>

export default async function About({ user }: Props) {
  const sessionUser = await getSessionUser()
  const allSkills = await getSkills(undefined, true)
  const userSkillIds = user.profile?.skills || []
  const userSkillIdSet = new Set(userSkillIds)
  const userSkillsObjects = allSkills.filter(skill =>
    userSkillIdSet.has(skill.id),
  )
  const hasSocials = Object.values(user.profile?.social || {}).some(
    link => link.active && link.value,
  )
  const isProfileEmpty =
    !user.profile?.bio && userSkillsObjects.length === 0 && !hasSocials

  if (!isProfileEmpty) {
    return (
      <div className="max-w-[600px] mx-auto w-full mt-6">
        <h3 className="text-lg font-semibold mb-3">About</h3>

        <p className="text-gray-600 leading-relaxed">
          {user.profile?.bio ? user.profile.bio : 'No bio available.'}
        </p>

        {userSkillsObjects.length > 0 ? (
          <div className="mt-6">
            <h4 className="text-lg font-semibold mb-3">Skills & Interests</h4>

            <div className="flex flex-wrap gap-2">
              {userSkillsObjects.map(skill => {
                return (
                  <div
                    key={skill.id}
                    className="inline-flex items-center rounded border px-2.5 py-0.5 text-xs transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-gray-100 text-gray-700 hover:bg-gray-200 font-normal"
                  >
                    {skill.name}
                  </div>
                )
              })}
            </div>
          </div>
        ) : null}

        {user.profile?.social && Object.keys(user.profile.social).length > 0 ? (
          <div className="mt-6">
            <h4 className="text-lg font-semibold mb-3">Social Links</h4>

            <div className="flex flex-wrap gap-6">
              {user.profile.social.twitter?.active &&
                user.profile.social.twitter.value && (
                  <a
                    href={`https://twitter.com/${user.profile.social.twitter.value.replace('@', '')}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary"
                  >
                    <FaXTwitter className="h-5 w-5" />
                  </a>
                )}
              {user.profile.social.linkedin?.active &&
                user.profile.social.linkedin.value && (
                  <a
                    href={`https://${user.profile.social.linkedin.value}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary"
                  >
                    <FaLinkedin className="h-5 w-5" />
                  </a>
                )}
              {user.profile.social.instagram?.active &&
                user.profile.social.instagram.value && (
                  <a
                    href={`https://instagram.com/${user.profile.social.instagram.value.replace('@', '')}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary"
                  >
                    <FaInstagram className="h-5 w-5" />
                  </a>
                )}
              {user.profile.social.facebook?.active &&
                user.profile.social.facebook.value && (
                  <a
                    href={`https://${user.profile.social.facebook.value}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary"
                  >
                    <FaFacebook className="h-5 w-5" />
                  </a>
                )}
              {user.profile.social.youtube?.active &&
                user.profile.social.youtube.value && (
                  <a
                    href={`https://${user.profile.social.youtube.value}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary"
                  >
                    <FaYoutube className="h-5 w-5" />
                  </a>
                )}
              {user.profile.social.github?.active &&
                user.profile.social.github.value && (
                  <a
                    href={`https://github.com/${user.profile.social.github.value}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary"
                  >
                    <FaGithub className="h-5 w-5" />
                  </a>
                )}
              {user.profile.social.twitch?.active &&
                user.profile.social.twitch.value && (
                  <a
                    href={`https://twitch.tv/${user.profile.social.twitch.value}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary"
                  >
                    <FaTwitch className="h-5 w-5" />
                  </a>
                )}
              {user.profile.social.dribbble?.active &&
                user.profile.social.dribbble.value && (
                  <a
                    href={`https://dribbble.com/${user.profile.social.dribbble.value}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary"
                  >
                    <FaDribbble className="h-5 w-5" />
                  </a>
                )}
              {user.profile.social.behance?.active &&
                user.profile.social.behance.value && (
                  <a
                    href={`https://behance.net/${user.profile.social.behance.value}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary"
                  >
                    <FaBehance className="h-5 w-5" />
                  </a>
                )}
            </div>
          </div>
        ) : null}
      </div>
    )
  }

  if (user.id === sessionUser?.id) {
    return (
      <div className="flex flex-col max-w-[600px] mx-auto py-20 gap-4 items-center justify-center">
        <div className="bg-gray-100 size-16 rounded-full flex items-center justify-center">
          <UserIcon className="inline-block text-gray-600 size-8" />
        </div>
        <div className="flex flex-col items-center justify-center gap-2 text-center max-w-md">
          <p className="text-lg font-semibold text-gray-900">
            Complete your profile
          </p>
          <p className="text-gray-500 mx-auto">
            Add more details about yourself, your skills, and what makes you
            unique to help others discover your work.
          </p>
        </div>
        <Link href="/user/settings">
          <Button variant="primary" className="w-fit h-10 gap-2">
            Complete Profile
          </Button>
        </Link>
      </div>
    )
  }

  return (
    <div className="flex flex-col max-w-[600px] mx-auto py-20 gap-4 items-center justify-center">
      <div className="bg-gray-100 size-16 rounded-full flex items-center justify-center">
        <FileText className="inline-block text-gray-600 size-8" />
      </div>
      <div className="flex flex-col items-center justify-center gap-2 text-center max-w-md">
        <p className="text-lg font-semibold text-gray-900">No details yet</p>
        <p className="text-gray-500 mx-auto">
          {user.profile?.firstName || 'This user'} has not added any details to
          their about section.
        </p>
      </div>
    </div>
  )
}
