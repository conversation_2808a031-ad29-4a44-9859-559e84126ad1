import Content from './Content'
import Header from './Header'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs'
import { User } from '@/actions/user'
import About from './About'

type Props = React.PropsWithChildren<{
  user: User
}>

export default async function ProfilePage({ user }: Props) {
  const options = ['products', 'about']

  return (
    <>
      <Header user={user} />

      <Tabs defaultValue="products" className="mb-6">
        <TabsList className="flex max-w-[600px] mx-auto items-center bg-transparent rounded-none h-auto pt-4 pb-0 border-y border-gray-200 w-full justify-center">
          {options.map(kind => (
            <TabsTrigger
              key={kind}
              value={kind}
              className="flex capitalize items-center justify-center text-gray-500data-[state=active]:bg-transparent data-[state=active]:text-black data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-x-0 data-[state=active]:border-t-0 data-[state=active]:border-b-2 data-[state=active]:border-black data-[state=active]:hover:bg-transparent data-[state=inactive]:hover:border-gray-300 data-[state=inactive]:hover:border-b-2 data-[state=inactive]:border-x-0 data-[state=inactive]:border-t-0 data-[state=inactive]:text-gray-500 data-[state=inactive]:bg-transparent data-[state=inactive]:hover:bg-transparent data-[state=inactive]:border-b-2 data-[state=inactive]:border-transparent rounded-none px-4 sm:px-6 py-4 font-medium transition-all -mb-px hover:text-black text-sm sm:text-base"
            >
              {`${kind}`}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value="products">
          <Content user={user} />
        </TabsContent>

        <TabsContent value="about">
          <About user={user} />
        </TabsContent>
      </Tabs>
    </>
  )
}
