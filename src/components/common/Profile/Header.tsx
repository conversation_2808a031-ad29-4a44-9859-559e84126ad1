import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { User } from '@/actions/user'
import { getInitials } from '@/utils/get-initials'
import Link from 'next/link'
import { Button } from '../../ui/button'
import { Link as LinkIcon, ShareIcon } from 'lucide-react'
import { getSessionUser } from '@/actions/auth'
import ShareModal from '../ShareModal'
import { HOST } from '@/config'

interface Props {
  user: User
}

export default async function Header({ user }: Props) {
  const name = `${user.profile?.firstName} ${user.profile?.lastName}`
  const sessionUser = await getSessionUser()

  return (
    <>
      <div className="max-w-[600px] mx-auto w-full">
        <div className="flex flex-col md:flex-row items-center justify-center gap-6 mb-6 w-full">
          <Avatar className="h-24 w-24 flex-shrink-0 my-3.5">
            <AvatarImage src={user.avatar} alt={name} />
            <AvatarFallback>
              {getInitials(user.profile?.firstName, user.profile?.lastName)}
            </AvatarFallback>
          </Avatar>

          <div className="w-full flex flex-col">
            <div className="flex flex-col w-full">
              <div className="flex items-center justify-center gap-2 w-full">
                <h1 className="font-bold text-gray-900 text-base m-0">
                  {name}
                </h1>
                <p className="text-gray-500 text-sm m-0">@{user.handler}</p>

                <div className="flex items-center ml-auto justify-center gap-2">
                  <ShareModal
                    text={`Check out ${name}'s profile on Templace!`}
                    url={`${HOST}/user/${user.handler}`}
                  >
                    <Button
                      variant="secondary"
                      className="gap-2 bg-gray-100 text-gray-700 hover:bg-gray-200 text-xs h-10 px-4 py-2"
                    >
                      <ShareIcon size={16} />
                      <span className="sr-only">Share profile</span>
                    </Button>
                  </ShareModal>

                  {user?.id === sessionUser?.id && (
                    <Link href="/user/settings">
                      <Button
                        variant="secondary"
                        className="gap-2 bg-gray-100 text-gray-700 hover:bg-gray-200 text-xs h-10 px-4 py-2"
                      >
                        Edit Profile
                        <span className="sr-only">Edit your profile</span>
                      </Button>
                    </Link>
                  )}
                </div>
              </div>

              {user?.profile?.social?.website?.value && (
                <a
                  href={user.profile.social.website.value}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-1 bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded-full text-xs transition-colors w-fit"
                >
                  <LinkIcon size={12} />
                  {user.profile.social.website.value.replace(
                    /^(https?:\/\/)?(www\.)?([^/]+)(\/.*)?$/,
                    '$3',
                  )}
                </a>
              )}
            </div>

            {user.profile?.headline ? (
              <p className="text-gray-700 font-medium mt-3 line-clamp-2">
                {user.profile.headline}
              </p>
            ) : user.profile?.bio ? (
              <p className="text-gray-700 font-medium mt-3 line-clamp-2">
                {user.profile.bio}
              </p>
            ) : null}
          </div>
        </div>
      </div>
    </>
  )
}
