import { getSessionUser } from '@/actions/auth'
import {
  getUserProducts,
  PlatformKindEnum,
  PlatformsEnum,
  ProductStatus,
  type Product,
} from '@/actions/product'
import { User } from '@/actions/user'
import ProductBox from '@/components/common/ProductBox'
import ProductBoxMini from '@/components/common/ProductBoxMini'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Plus, ShoppingBag } from 'lucide-react'
import Link from 'next/link'

const PLATFORM_CONFIG = {
  template: {
    enum: PlatformKindEnum.TEMPLATE,
    Component: ProductBox,
    label: 'Templates',
  },
  bundle: {
    enum: PlatformsEnum.BUNDLE,
    Component: ProductBoxMini,
    label: 'Bundles',
  },
  // component: { enum: PlatformKindEnum.COMPONENT, Component: ProductBox, label: 'Components' },
  // subscription: { enum: PlatformsEnum.SUBSCRIPTION, Component: ProductBoxMini, label: 'Subscriptions' },
}

function EmptyStateForOwner() {
  return (
    <div className="flex flex-col max-w-[600px] mx-auto py-20 gap-4 items-center justify-center">
      <div className="bg-gray-100 size-16 rounded-full flex items-center justify-center">
        <Plus className="inline-block text-gray-600 size-8" />
      </div>
      <div className="flex flex-col items-center justify-center gap-2 text-center max-w-md">
        <p className="text-lg font-semibold text-gray-900">No products yet</p>
        <p className="text-gray-500 mx-auto">
          Start showcasing your work by adding your first template, component,
          or bundle.
        </p>
      </div>
      <Link href="/dashboard/products/templates/add">
        <Button variant="primary" className="w-fit h-10 gap-2">
          <Plus className="size-4" /> Add Your First Product
        </Button>
      </Link>
    </div>
  )
}

function EmptyStateForVisitor({ user }: { user: User }) {
  return (
    <div className="flex flex-col max-w-[600px] mx-auto py-20 gap-4 items-center justify-center">
      <div className="bg-gray-100 size-16 rounded-full flex items-center justify-center">
        <ShoppingBag className="inline-block text-gray-600 size-8" />
      </div>
      <div className="flex flex-col items-center justify-center gap-2 text-center max-w-md">
        <p className="text-lg font-semibold text-gray-900">No products found</p>
        <p className="text-gray-500 mx-auto">
          {user.profile?.firstName || 'This user'} has not added any products
          yet.
        </p>
      </div>
    </div>
  )
}

export default async function Content({ user }: { user: User }) {
  const sessionUser = await getSessionUser()
  const allProducts: Product[] = await getUserProducts(
    user?.id?.toString(),
    undefined,
    ProductStatus.PUBLISHED,
  )

  const productsByKind = allProducts.reduce<Record<string, Product[]>>(
    (acc, product) => {
      const kind = Object.keys(PLATFORM_CONFIG).find(k =>
        product.flair?.includes(
          PLATFORM_CONFIG[k as keyof typeof PLATFORM_CONFIG].enum,
        ),
      )

      if (kind) {
        if (!acc[kind]) {
          acc[kind] = []
        }
        acc[kind].push(product)
      }
      return acc
    },
    {},
  )

  const availableKinds = Object.keys(productsByKind)

  if (availableKinds.length === 0) {
    return user?.id === sessionUser?.id &&
      user.profile?.permissions?.canAddContent ? (
      <EmptyStateForOwner />
    ) : (
      <EmptyStateForVisitor user={user} />
    )
  }

  return (
    <div className="flex-1 min-w-0">
      <Tabs defaultValue={availableKinds[0]} className="mb-6">
        <TabsList className="flex max-w-[600px] mx-auto my-8 items-center bg-transparent rounded-none h-auto pb-0 w-full justify-center">
          {availableKinds.map(kind => (
            <TabsTrigger
              key={kind}
              value={kind}
              className="flex capitalize items-center justify-center gap-2 h-9 rounded-lg px-6 py-3 text-xs font-medium transition-colors data-[state=active]:bg-gray-900 data-[state=active]:text-white data-[state=inactive]:text-gray-600 data-[state=inactive]:bg-gray-100 data-[state=inactive]:hover:bg-gray-200"
            >
              {PLATFORM_CONFIG[kind as keyof typeof PLATFORM_CONFIG].label}
            </TabsTrigger>
          ))}
        </TabsList>

        {availableKinds.map(kind => {
          const { Component } =
            PLATFORM_CONFIG[kind as keyof typeof PLATFORM_CONFIG]
          return (
            <TabsContent
              key={kind}
              value={kind}
              className="grid grid-cols-3 gap-8"
            >
              {productsByKind[kind].map(product => (
                <Component
                  key={product.id}
                  product={product}
                  link={`/product/${product.slug}`}
                />
              ))}
            </TabsContent>
          )
        })}
      </Tabs>
    </div>
  )
}
