import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'

export default function ButtonSwitcher({ buttons, value }: any) {
  return buttons.length > 0 ? (
    <Tabs value={value}>
      <TabsList>
        {buttons.map((item: any) => (
          <TabsTrigger
            className="inline-flex gap-1"
            key={item.href}
            value={item.href}
            asChild
          >
            <a href={item.href}>
              <span>{item.label}</span>
              {item.totals ? (
                <small className="text-[0.5rem] bg-muted p-1 rounded-full aspect-square text-muted-foreground font-normal">
                  {item.totals}
                </small>
              ) : null}
            </a>
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  ) : null
}
