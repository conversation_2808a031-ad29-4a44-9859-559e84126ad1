import { useFormContext } from 'react-hook-form'
import { Checkbox } from '@/components/ui/checkbox'

export default function FieldCheckbox({
  className = '',
  name,
  label,
  ...options
}) {
  const { register } = useFormContext()
  return (
    <div
      className={`flex flex-row gap-2 justify-center items-center w-full p-2 ${className}`}
    >
      <div className="flex justify-between w-auto text-gray-800 text-sm cursor-pointer">
        <Checkbox id={name} {...register(name, options)} />
      </div>
      <label
        className="flex justify-between w-full text-gray-800 text-sm cursor-pointer"
        htmlFor={name}
      >
        <span className="text-[.8rem] text-gray-500">{label}</span>
      </label>
    </div>
  )
}
