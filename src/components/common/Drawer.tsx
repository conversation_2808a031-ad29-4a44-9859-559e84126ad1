import React from 'react'

import { XMarkIcon } from '@heroicons/react/24/outline'

import Button from './HomeButton'

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode
  open?: boolean
  onClose?: () => void
  onTransitionEnd?: () => void
  title?: string
}

export default React.forwardRef<HTMLDivElement, Props>(function Drawer(
  { title, onClose, onTransitionEnd, open, children, ...props },
  ref,
) {
  return (
    <div
      ref={ref}
      onTransitionEnd={onTransitionEnd}
      className="fixed top-0 bottom-0 right-0 w-min justify-end bg-transparent z-50 grid duration-500"
      style={{ gridTemplateColumns: open ? '100%' : '0' }}
      {...props}
    >
      <div
        className="fixed top-0 right-0 bottom-0 bg-gradient-to-l from-gray-950 to-[transparent] duration-500 w-full"
        onClick={onClose}
        style={{ opacity: open ? '0.3' : '0', left: open ? '0' : '100%' }}
      />
      <div className="relative pt-24 w-max flex flex-col items-start justify-start h-full p-8 bg-background shadow-md overflow-y-auto overflow-x-hidden">
        <h2 className="text-3xl font-bold absolute top-0 left-0 p-8 w-full flex flex-row items-start justify-between">
          {title}
          <Button onClick={onClose} design="none">
            <XMarkIcon className="w-6 h-6 text-gray-300 hover:text-gray-700" />
          </Button>
        </h2>
        {children}
      </div>
    </div>
  )
})
