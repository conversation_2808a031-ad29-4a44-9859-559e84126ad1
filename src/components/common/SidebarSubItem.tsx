import Link from 'next/link'
import { LucideIcon } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'

interface Props {
  path: string
  label: string
  icon?: LucideIcon
  isActive: boolean
  count?: number
  onClose: () => void
}

export default function SidebarSubItem({
  path,
  label,
  icon: Icon,
  isActive,
  count,
  // onClose,
}: Props) {
  return (
    <Link
      href={path}
      // onClick={onClose}
      className={cn(
        'flex items-center justify-between px-3 py-2 rounded-lg transition-colors text-sm relative group',
        isActive
          ? 'bg-gray-100 text-gray-900'
          : 'hover:bg-gray-100 text-gray-600',
      )}
    >
      <div className="flex items-center space-x-2">
        {Icon && (
          <Icon
            className={cn(
              'h-3.5 w-3.5 mr-2',
              isActive ? 'text-gray-900' : 'text-gray-600',
            )}
            strokeWidth={1.25}
          />
        )}
        <span>{label}</span>
      </div>
      {count !== undefined && (
        <Badge
          variant="secondary"
          className="bg-[#F1F1F1] text-gray-400 border-none h-5"
        >
          {count}
        </Badge>
      )}
    </Link>
  )
}
