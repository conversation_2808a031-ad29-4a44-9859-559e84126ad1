'use client'

import { useRouter } from 'next/navigation'

import { FileSet } from '@/actions/file'
import { ImageSet } from '@/actions/image'
import { PaymentPrice } from '@/actions/price'
import { PlatformsEnum, Product } from '@/actions/product'
import MediaBox from '@/components/common/MediaBox'
import PlatformWidget from '@/components/products/PlatformWidget'
import { Card, CardContent } from '@/components/ui/card'
import centsToDollars from '@/utils/cents-to-dollars'
import { getPlatform, getPlatformValue } from '@/utils/get-platform'
import Link from 'next/link'

type Props = React.PropsWithChildren<{
  product: Product
  link: string
}>

export default function ProductBoxMini({ product, link }: Props) {
  const router = useRouter()
  const clickHandler = () => {
    router.push(link)
  }

  const media = (product.fileSet as FileSet)?.files?.find(
    file =>
      file.mimeType?.startsWith('video/') ||
      file.mimeType?.startsWith('image/'),
  ) ?? { ...(product?.imageSet as ImageSet)?.images?.[0], mimeType: 'image/' }

  const isMultiAssets =
    getPlatform(product?.platforms) === PlatformsEnum.BUNDLE ||
    getPlatform(product?.platforms) === PlatformsEnum.SUBSCRIPTION

  // console.log('isMultiAssets', isMultiAssets)

  return (
    <Card className="group border cursor-pointer hover:bg-gray-50">
      <CardContent className="p-6">
        <Link
          href={link}
          onClick={clickHandler}
          className="w-full flex items-center justify-start gap-6"
        >
          <div className="w-16 h-16 flex-shrink-0 rounded-full overflow-hidden bg-gray-100 flex items-center justify-center">
            <MediaBox className="bg-gray-100" media={media} ratio={1 / 1} />
          </div>

          <div className="space-y-2 min-w-0 flex-grow">
            <div key="product-info" className="flex items-center gap-2">
              <div className="w-5 h-5 rounded-full overflow-hidden bg-gray-100 flex items-center justify-center">
                <PlatformWidget
                  size="1"
                  platform={Object.keys(product?.platforms).shift()}
                  justIcon
                />
              </div>

              <h3 className="text-sm flex-grow font-semibold truncate">
                {product.name}
              </h3>

              <div className="text-xs font-medium ml-2 flex-shrink-0">
                {(product.price as PaymentPrice)?.amount === 0
                  ? 'Free'
                  : centsToDollars((product.price as PaymentPrice).amount)}
              </div>
            </div>

            {Boolean(product?.description) && (
              <p className="text-muted-foreground text-xs max-w-48 line-clamp-2">
                {product?.description}
              </p>
            )}

            {isMultiAssets && (
              <div key="multi-assets" className="flex items-center">
                {getPlatformValue(product?.platforms)?.assets?.map(
                  (item: Product) => {
                    // console.log('->', item)

                    return (
                      <PlatformWidget
                        key={item?.id?.toString()}
                        size=".75"
                        className="-ml-1 drop-shadow-sm"
                        justIcon
                        platform={getPlatform(item?.platforms)}
                      />
                    )
                  },
                )}
              </div>
            )}
          </div>
        </Link>
      </CardContent>
    </Card>
  )
}
