'use client'

import { But<PERSON> } from '@/components/ui/button'
import { useDialog } from '@/hooks/use-dialog'
import useSessionUser from '@/hooks/useSessionUser'
import { useRouter } from 'next/navigation'

export default function UserAuth() {
  const { data: user } = useSessionUser()
  const { data: dialog, onOpen, onClose } = useDialog()
  const router = useRouter()

  return (
    <div className="flex items-center gap-2">
      <Button variant="ghost" onClick={() => router.push('/login')}>
        Connect to Templace
      </Button>
      {/* <Button onClick={() => onOpen('singup')}>Sign up</Button> */}
    </div>
  )
}
