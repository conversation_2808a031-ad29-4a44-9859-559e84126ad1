import Image from 'next/image'

import { AspectRatio } from '@/components/ui/aspect-ratio'
import { DEFAULT_BLUR_DATA_URL } from '@/config'
import { cn } from '@/lib/utils'

export type MediaInABox = {
  url?: string
  mimeType?: string
  name?: string
}

type Props = PropsWithClassName<{
  className?: string
  media: MediaInABox
  ratio?: number
  width?: number
  height?: number
}>

export default function MediaBox({
  className,
  media = {},
  ratio = 1 / 1,
  width = 0,
  height = 0,
}: Props) {
  const isVideo = Boolean(media.mimeType?.startsWith('video'))
  const isImage = Boolean(media.mimeType?.startsWith('image'))

  return (
    <AspectRatio ratio={ratio}>
      {media?.url ? (
        <>
          {isVideo && (
            <video
              src={media.url}
              width={width}
              height={height}
              autoPlay
              loop
              muted
              playsInline
              className="object-cover w-full h-full"
              poster={DEFAULT_BLUR_DATA_URL}
            />
          )}

          {isImage && (
            <Image
              src={media.url}
              alt={media.name ?? ''}
              width={width * 2}
              height={height * 2}
              blurDataURL={DEFAULT_BLUR_DATA_URL}
              className="object-cover w-full h-full"
            />
          )}
        </>
      ) : (
        <svg
          width={width}
          height={height}
          xmlns="http://www.w3.org/2000/svg"
          className="block"
        >
          <rect
            width="100%"
            height="100%"
            fill="transparent"
            className="bg-muted-foreground/10"
          />
        </svg>
      )}
    </AspectRatio>
  )
}
