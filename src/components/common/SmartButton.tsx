'use client'

import { ChevronDown, Plus } from 'lucide-react'
import Link from 'next/link'
import { useState, useCallback, useMemo } from 'react'

import { PUBLIC_VERCEL_ENV } from '@/config'

import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Separator } from '@/components/ui/separator'
import useSessionUser from '@/hooks/useSessionUser'
import useUser from '@/hooks/useUser'
import useUserHats from '@/hooks/useUserHats'

const SmartButtonSkeleton = () => (
  <div className="flex items-center gap-2 h-[2.25rem] w-[2.25rem]">
    <div className="h-9 w-20 bg-neutral-200 rounded-md animate-pulse" />
  </div>
)

export default function SmartButton() {
  const { data: userSession } = useSessionUser()
  const userId = userSession?.id ? String(userSession.id) : ''
  const { data: user, isLoading } = useUser(userId)

  const { isAdmin, isAdminSession } = useUserHats()

  const isContentCreator = useMemo(() => {
    return user?.profile?.permissions?.canAddContent ?? false
  }, [user?.profile?.permissions?.canAddContent])

  const [open, setOpen] = useState<string[]>([])

  const clickToggleHandler = useCallback(
    (menuName: string) => () => {
      setOpen(prevOpen => {
        const newOpen = [...prevOpen]
        if (newOpen.includes(menuName)) {
          newOpen.splice(newOpen.indexOf(menuName), 1)
        } else {
          newOpen.push(menuName)
        }
        return newOpen
      })
    },
    [],
  )

  if (isLoading && (isAdminSession || isContentCreator || isAdmin)) {
    return <SmartButtonSkeleton />
  }

  return user ? (
    <>
      {isAdmin && (
        <Link href="/backoffice/users">
          <Button className="h-[2.30rem]">Backoffice</Button>
        </Link>
      )}

      {(isContentCreator || isAdmin) && (
        <DropdownMenu
          open={open.includes('product')}
          onOpenChange={clickToggleHandler('product')}
          modal={false}
        >
          <DropdownMenuTrigger asChild>
            <Button className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-9 w-9 bg-black hover:bg-gray-800 text-white">
              <Plus />
            </Button>
          </DropdownMenuTrigger>

          <DropdownMenuContent side="bottom" align="end">
            <DropdownMenuItem onClick={clickToggleHandler('product')}>
              <Link href="/dashboard/products/templates/add">
                Add a Template
              </Link>
            </DropdownMenuItem>

            {PUBLIC_VERCEL_ENV !== 'production' && (
              <DropdownMenuItem onClick={clickToggleHandler('product')}>
                <Link href="/dashboard/products/components/add">
                  Add a Component
                </Link>
              </DropdownMenuItem>
            )}

            <DropdownMenuItem onClick={clickToggleHandler('product')}>
              <Link href="/dashboard/products/bundles/add">
                Create a Bundle
              </Link>
            </DropdownMenuItem>

            {PUBLIC_VERCEL_ENV !== 'production' && (
              <DropdownMenuItem onClick={clickToggleHandler('product')}>
                <Link href="/dashboard/products/subscriptions/add">
                  Create a Subscription
                </Link>
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </>
  ) : null
}
