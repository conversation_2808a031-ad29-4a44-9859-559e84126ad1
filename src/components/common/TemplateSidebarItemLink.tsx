// import { useEffect, useState } from 'react'
import Link from 'next/link'
import { LucideIcon } from 'lucide-react'

import { Badge } from '@/components/ui/badge'
import {
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuBadge,
} from '@/components/ui/sidebar'
import { cn } from '@/lib/utils'
import SidebarItemActiveMonitor from './SidebarItemActiveMonitor'

type Props = React.PropsWithChildren<{
  icon?: LucideIcon
  label?: string
  count?: number
  url?: string
  pathname?: string
}>

export default function TemplateSidebarItemLink({
  icon: Icon,
  label,
  count,
  url,
  pathname,
}: Props) {
  const isActive = pathname === url

  const iconClassName = cn(
    'h-4 w-4',
    isActive ? 'text-gray-700' : 'text-gray-600',
  )

  return (
    <SidebarMenuItem>
      <SidebarMenuButton
        asChild
        className="flex items-center w-full px-4 py-2 rounded-lg text-gray-600 has-[>.monitor]:bg-gray-300 has-[>.monitor]:text-gray-700 has-[>.monitor]:hover:bg-gray-400/80"
      >
        {url ? (
          <Link href={url}>
            {Icon ? <Icon className={iconClassName} /> : null}
            <span className="text-sm font-medium flex-1 text-left">
              {label}
            </span>
            <SidebarItemActiveMonitor url={url} />
          </Link>
        ) : (
          <span>
            {Icon ? <Icon className={iconClassName} /> : null}
            <span className="text-sm font-medium flex-1 text-left">
              {label}
            </span>
          </span>
        )}
      </SidebarMenuButton>
      {count !== undefined && (
        <SidebarMenuBadge>
          <Badge
            variant="secondary"
            className="bg-gray-50 font-light text-gray-600 border-none rounded-full"
          >
            {count}
          </Badge>
        </SidebarMenuBadge>
      )}
    </SidebarMenuItem>
  )
}
