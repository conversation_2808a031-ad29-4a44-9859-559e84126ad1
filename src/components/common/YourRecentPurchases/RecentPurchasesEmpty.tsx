'use client'

import { ShoppingBag } from 'lucide-react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'

export default function RecentPurchasesEmpty() {
  return (
    <div className="flex size-full flex-col items-center text-center justify-center gap-4">
      <div className="size-12 rounded-full bg-gray-100 flex items-center justify-center">
        <ShoppingBag className="size-6 text-gray-600" />
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-medium text-gray-900">No purchases yet</h3>
        <p className="text-xs text-gray-600 max-w-xs">
          Start exploring our marketplace to find amazing templates and
          resources for your projects.
        </p>
      </div>

      <Link href="/explore">
        <Button
          variant="secondary"
          className="rounded-md font-medium transition-colors bg-gray-100 text-gray-700 hover:bg-gray-200 text-xs h-10 px-4 py-2"
        >
          Browse Marketplace
        </Button>
      </Link>
    </div>
  )
}
