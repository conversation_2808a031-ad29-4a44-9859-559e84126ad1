'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/button'
import RecentPurschedTemplates from './RecentPurschedTemplates'
import RecentPurchasesEmpty from './RecentPurchasesEmpty'
import usePurchaseList from '@/hooks/use-purchase-list'
import useSessionUser from '@/hooks/useSessionUser'
import Skeleton from './RecentPurchasesSkeleton'

export default function YourRecentPurchases() {
  const { data: user } = useSessionUser()
  const {
    data: purchases,
    isLoading,
    isFetching,
    isPending,
  } = usePurchaseList(user?.id?.toString(), 'template')

  // Check if we have actual data (not just empty array from initialData)
  const hasRealData = purchases?.pages?.[0] !== undefined
  const isLoadingState = isLoading || isPending || isFetching || !hasRealData
  const hasPurchases =
    !isLoadingState && purchases?.pages?.[0] && purchases.pages[0].length > 0
  const maxItems = 2

  return (
    <div className="flex flex-col mt-6 gap-6">
      <div className="flex items-center justify-between h-10">
        <h2 className="text-lg font-semibold">Your Recent Purchases</h2>

        {hasPurchases && (
          <Link href="/dashboard/purchases">
            <Button
              variant="secondary"
              className="gap-2 bg-gray-100 text-gray-700 hover:bg-gray-200 text-xs h-10 px-4 py-2"
            >
              View All Purchases
            </Button>
          </Link>
        )}
      </div>

      <div className="w-full" style={{ height: 85 * maxItems + 41 }}>
        {isLoadingState ? (
          <Skeleton maximum={maxItems} />
        ) : hasPurchases ? (
          <RecentPurschedTemplates maximum={maxItems} purchases={purchases} />
        ) : (
          <RecentPurchasesEmpty />
        )}
      </div>
    </div>
  )
}
