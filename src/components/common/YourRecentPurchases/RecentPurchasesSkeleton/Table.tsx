'use client'

import { Table, TableBody, TableRow } from '@/components/ui/table'
import { TableCellWrapper, TblHeader } from '../TableWrapper'

export type TableSkeletonProps = {
  maximum: number
}

export default function TableSkeleton({ maximum }: TableSkeletonProps) {
  return (
    <div className="overflow-x-auto">
      <Table className="w-full text-xs min-w-[600px]">
        <TblHeader />
        <TableBody className="border-b border-gray-200">
          {Array.from({ length: maximum }).map((_, index) => (
            <TableRow key={index} className="border-b border-gray-200">
              {/* Name Column */}
              <TableCellWrapper columnKey="name">
                <div className="flex items-center gap-2">
                  <div className="w-20 h-[60px] flex-shrink-0 rounded bg-gray-200 animate-pulse" />
                  <div className="min-w-0">
                    <div className="h-4 bg-gray-200 rounded animate-pulse mb-1 w-3/4" />
                    <div className="h-3 bg-gray-200 rounded animate-pulse w-16" />
                  </div>
                </div>
              </TableCellWrapper>

              {/* Price Column */}
              <TableCellWrapper columnKey="price">
                <div className="h-4 bg-gray-200 rounded animate-pulse w-12 mx-auto" />
              </TableCellWrapper>

              {/* Platform Column */}
              <TableCellWrapper columnKey="platform">
                <div className="size-4 bg-gray-200 rounded animate-pulse mx-auto" />
              </TableCellWrapper>

              {/* Creator Column */}
              <TableCellWrapper columnKey="creator">
                <div className="h-4 bg-gray-200 rounded animate-pulse w-20" />
              </TableCellWrapper>

              {/* Date Column */}
              <TableCellWrapper columnKey="date">
                <div className="h-4 bg-gray-200 rounded animate-pulse w-16" />
              </TableCellWrapper>

              {/* Extra Column */}
              <TableCellWrapper columnKey="extra">
                <div className="h-8 bg-gray-200 rounded animate-pulse w-24 mx-auto" />
              </TableCellWrapper>

              {/* Actions Column */}
              <TableCellWrapper columnKey="actions">
                <div className="h-8 bg-gray-200 rounded animate-pulse w-16 ml-auto" />
              </TableCellWrapper>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
