'use client'

import centsToDollars from '@/utils/cents-to-dollars'
import c from 'clsx'
import { Badge } from '@/components/ui/badge'

// Table columns configuration
export const TABLE_COLUMNS = {
  name: { width: 'w-[40%]', minWidth: 'min-w-[200px]', align: 'left' },
  price: { width: 'w-[8%]', minWidth: 'min-w-[80px]', align: 'center' },
  platform: { width: 'w-[8%]', minWidth: 'min-w-[80px]', align: 'center' },
  creator: { width: 'w-[12%]', minWidth: 'min-w-[120px]', align: 'left' },
  date: { width: 'w-[10%]', minWidth: 'min-w-[100px]', align: 'left' },
  extra: { width: 'w-[8%]', minWidth: 'min-w-[100px]', align: 'center' },
  actions: { width: 'w-[6%]', minWidth: 'min-w-[80px]', align: 'right' },
} as const

// Centralized styles
export const STYLES = {
  tableCell: {
    base: 'h-auto p-3',
    alignments: {
      left: 'text-left',
      center: 'text-center',
      right: 'text-right',
    },
  },
  button: {
    base: 'rounded-md text-xs bg-white border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-gray-900 h-8 px-3 font-medium shadow-sm',
  },
  text: {
    xs: 'text-xs',
    xsGray500: 'text-xs text-gray-500',
    xsGray600: 'text-xs text-gray-600',
    xsGray700: 'text-xs text-gray-700',
    xsGray900: 'text-xs text-gray-900',
    fontMedium: 'font-medium',
    fontSemibold: 'font-semibold',
  },
  badge: {
    free: 'bg-green-50 text-green-700 text-xs border-0 hover:bg-green-50',
  },
  common: {
    hoverRow:
      'border-b border-gray-200 hover:bg-gray-50 transition-colors cursor-pointer',
    truncate: 'truncate',
    flexCenter: 'flex items-center gap-2',
    centerSize: 'size-4 mx-auto',
    mediaBox: 'w-20 h-[60px] flex-shrink-0 rounded overflow-hidden',
    mediaBoxInner: 'bg-gray-100 rounded',
  },
}

// Helper function to get cell classes
export const getCellClasses = (
  columnKey: keyof typeof TABLE_COLUMNS,
  additionalClasses?: string,
) => {
  const column = TABLE_COLUMNS[columnKey]
  return c(
    STYLES.tableCell.base,
    column.width,
    column.minWidth,
    STYLES.tableCell.alignments[column.align],
    additionalClasses,
  )
}

// Helper function to get header classes
export const getHeaderClasses = (columnKey: keyof typeof TABLE_COLUMNS) => {
  const column = TABLE_COLUMNS[columnKey]
  return c(
    column.width,
    column.minWidth,
    STYLES.tableCell.alignments[column.align],
    'p-3 font-medium h-auto text-gray-700 text-xs',
  )
}

// Helper function to render price
export const renderPrice = (amount: number) => {
  if (amount === 0) {
    return (
      <Badge variant="secondary" className={STYLES.badge.free}>
        Free
      </Badge>
    )
  }
  return centsToDollars(amount)
}
