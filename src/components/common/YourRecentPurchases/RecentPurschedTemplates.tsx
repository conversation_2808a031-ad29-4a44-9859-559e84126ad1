'use client'

import { format } from 'date-fns'
import Link from 'next/link'
import PlatformWidget from '@/components/products/PlatformWidget'
import { Table, TableBody, TableRow } from '@/components/ui/table'
import c from 'clsx'
import { Button } from '@/components/ui/button'
import MediaBox from '@/components/common/MediaBox'
import { getPlatform } from '@/utils/get-platform'
import { Purchase } from '@/actions/payment'
import DownloadFigmaFile from '@/components/purchases/DownloadFigmaFile'
import { InfiniteData } from '@tanstack/react-query'
import { renderPrice, STYLES, TABLE_COLUMNS } from './helpers'
import { TableCellWrapper, TblHeader } from './TableWrapper'

type RecentPurschedTemplatesProps = {
  maximum: number
  purchases: InfiniteData<Purchase[] | undefined, unknown>
}

export default function RecentPurschedTemplates({
  maximum,
  purchases,
}: RecentPurschedTemplatesProps) {
  const recentPurchases = purchases?.pages?.[0]?.slice(0, maximum) || []

  return (
    <div className="overflow-x-auto">
      <Table className="w-full text-xs min-w-[600px]">
        <TblHeader />
        <TableBody className="border-b border-gray-200">
          {recentPurchases.map((purchase: Purchase) => {
            const media =
              purchase?.product?.fileSet?.files?.find(
                file =>
                  file.mimeType?.startsWith('video/') ||
                  file.mimeType?.startsWith('image/'),
              ) ?? {}

            return (
              <TableRow
                key={purchase.id.toString()}
                className={STYLES.common.hoverRow}
              >
                <TableCellWrapper columnKey="name">
                  <div className={STYLES.common.flexCenter}>
                    <div className={STYLES.common.mediaBox}>
                      <MediaBox
                        className={STYLES.common.mediaBoxInner}
                        media={media}
                        width={80}
                        height={60}
                        ratio={80 / 60}
                      />
                    </div>
                    <div className="min-w-0">
                      <div
                        className={c(
                          STYLES.text.fontMedium,
                          STYLES.text.xsGray900,
                          STYLES.common.truncate,
                        )}
                      >
                        {purchase?.product?.name}
                      </div>
                      <div className={STYLES.text.xsGray500}>Template</div>
                    </div>
                  </div>
                </TableCellWrapper>

                <TableCellWrapper
                  columnKey="price"
                  className={c(STYLES.text.fontSemibold, STYLES.text.xsGray900)}
                >
                  {renderPrice(purchase?.product?.price?.amount)}
                </TableCellWrapper>

                <TableCellWrapper
                  columnKey="platform"
                  className={c(STYLES.text.fontMedium, STYLES.text.xsGray700)}
                >
                  <div className={STYLES.common.centerSize}>
                    {getPlatform(purchase?.product?.platforms) ? (
                      <PlatformWidget
                        size="1"
                        platform={getPlatform(purchase?.product?.platforms)}
                        justIcon
                      />
                    ) : (
                      <div className="size-4" />
                    )}
                  </div>
                </TableCellWrapper>

                <TableCellWrapper
                  columnKey="creator"
                  className={STYLES.text.xsGray600}
                >
                  {purchase?.product?.owner?.handler ? (
                    <Link href={`/@${purchase.product.owner.handler}`}>
                      @{purchase.product.owner.handler}
                    </Link>
                  ) : (
                    '-'
                  )}
                </TableCellWrapper>

                <TableCellWrapper
                  columnKey="date"
                  className={STYLES.text.xsGray600}
                >
                  {format(purchase?.created, 'PP')}
                </TableCellWrapper>

                <TableCellWrapper
                  columnKey="extra"
                  className="font-medium text-xs text-gray-600"
                >
                  <DownloadFigmaFile
                    className={STYLES.button.base}
                    product={purchase.product}
                    noIcon={false}
                  >
                    Download Figma
                  </DownloadFigmaFile>
                </TableCellWrapper>

                <TableCellWrapper columnKey="actions">
                  <Button variant="outline" className={STYLES.button.base}>
                    Transfer
                  </Button>
                </TableCellWrapper>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
    </div>
  )
}
