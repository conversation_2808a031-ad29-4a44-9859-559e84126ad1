'use client'

import {
  <PERSON><PERSON>ell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { getCellClasses, getHeaderClasses, TABLE_COLUMNS } from './helpers'

export const TableCellWrapper = ({
  columnKey,
  className,
  children,
}: {
  columnKey: keyof typeof TABLE_COLUMNS
  className?: string
  children: React.ReactNode
}) => (
  <TableCell className={getCellClasses(columnKey, className)}>
    {children}
  </TableCell>
)

export function TblHeader() {
  const headers = [
    { key: 'name', label: 'Name' },
    { key: 'price', label: 'Price' },
    { key: 'platform', label: 'Platform' },
    { key: 'creator', label: 'Creator' },
    { key: 'date', label: 'Date' },
    { key: 'extra', label: 'Extra' },
    { key: 'actions', label: 'Actions' },
  ] as const

  return (
    <TableHeader>
      <TableRow className="bg-gray-50 border-b border-gray-200">
        {headers.map(({ key, label }) => (
          <TableHead key={key} className={getHeaderClasses(key)}>
            {label}
          </TableHead>
        ))}
      </TableRow>
    </TableHeader>
  )
}
