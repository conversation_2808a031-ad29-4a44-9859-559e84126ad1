'use client'

import { usePathname } from 'next/navigation'
import { memo, useMemo } from 'react'
import { getMenuItems, MenuItem } from '@/actions/get-menu-items'
import useSessionUser from '@/hooks/useSessionUser'
import Link from 'next/link'
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON>ip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { Menu } from 'lucide-react'
import {
  <PERSON><PERSON>,
  <PERSON>et<PERSON>ontent,
  SheetHeader,
  SheetTit<PERSON>,
  SheetTrigger,
} from '@/components/ui/sheet'
import { Button } from '../ui/button'
import UserBox from './UserBox'
import { useRouter } from 'next/navigation'

const NAV_CLASSES = {
  container: 'md:h-full md:flex hidden',
  nav: 'flex-1 space-y-2 p-2',
  link: 'flex h-12 w-12 items-center justify-center rounded-lg transition-colors hover:bg-gray-100',
  linkActive: 'bg-gray-100 hover:bg-gray-200',
  icon: 'w-5',
  iconDefault: 'text-gray-500',
  iconActive: 'text-gray-700',
} as const

const isItemActive = (item: MenuItem, pathname: string): boolean => {
  if (item.url === '/') {
    return pathname === '/'
  }
  return item.url
    ? pathname === item.url || pathname.startsWith(item.url)
    : false
}

const MenuItemComponent = memo(
  ({ item, isActive }: { item: MenuItem; isActive: boolean }) => {
    if (!item.url) return null

    return (
      <Tooltip delayDuration={100}>
        <TooltipTrigger asChild>
          <Link
            href={item.url}
            className={`${NAV_CLASSES.link} ${
              isActive ? NAV_CLASSES.linkActive : ''
            }`}
            aria-label={item.label}
          >
            {item.icon && (
              <item.icon
                className={`${NAV_CLASSES.icon} ${
                  isActive ? NAV_CLASSES.iconActive : NAV_CLASSES.iconDefault
                }`}
                aria-hidden="true"
              />
            )}
          </Link>
        </TooltipTrigger>
        <TooltipContent side="right">
          <p>{item.label}</p>
        </TooltipContent>
      </Tooltip>
    )
  },
)

MenuItemComponent.displayName = 'MenuItemComponent'

export default function AppSidebarClient() {
  const { data: user } = useSessionUser()
  const pathname = usePathname()
  const router = useRouter()

  const menuItems = useMemo(() => {
    return getMenuItems(user)
  }, [user])

  const activeItems = useMemo(() => {
    return menuItems.map(item => ({
      ...item,
      isActive: isItemActive(item, pathname),
    }))
  }, [menuItems, pathname])

  const mobileMenuItems = useMemo(() => {
    if (menuItems.length < 3) return []

    const home = menuItems[0]
    const explore = menuItems[1]
    const subscriptions = menuItems[2]

    if (!home || !explore || !subscriptions) return []

    return [home, explore, subscriptions]
  }, [menuItems])

  return (
    <>
      <div className="md:hidden pr-4 h-[4rem] flex items-center justify-center">
        <Sheet>
          <SheetTrigger asChild>
            <Button
              variant="ghost"
              className="flex items-center gap-2 h-[2.25rem] w-[2.25rem]"
            >
              <Menu />
            </Button>
          </SheetTrigger>
          <SheetContent className="w-[80vw] max-w-[18rem] flex flex-col p-0">
            <SheetHeader>
              <SheetTitle />
            </SheetHeader>
            <nav className="mt-6 space-y-2 h-full p-5">
              {menuItems.map(item => {
                const isActive = isItemActive(item, pathname)
                return (
                  <Link
                    key={item.label || item.url}
                    href={item.url || '#'}
                    className={`flex items-center gap-3 px-3 py-2 rounded-lg transition-colors h-[3.125rem] ${
                      isActive
                        ? 'bg-gray-100 text-gray-900'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    {item.icon && (
                      <item.icon
                        className={`w-5 h-5 ${
                          isActive ? 'text-gray-900' : 'text-gray-600'
                        }`}
                      />
                    )}
                    <span className="font-medium">{item.label}</span>
                  </Link>
                )
              })}
            </nav>
            <div className="flex border-t py-6 px-5">
              {user ? (
                <UserBox side="bottom" avatarWithName />
              ) : (
                <Button
                  variant="ghost"
                  className="h-[2.25rem]"
                  onClick={() => router.push('/login')}
                >
                  Connect to Template
                </Button>
              )}
            </div>
          </SheetContent>
        </Sheet>
      </div>
      <div className={`${NAV_CLASSES.container} ${user ? 'md:border-b' : ''}`}>
        <TooltipProvider>
          <nav
            className={NAV_CLASSES.nav}
            role="navigation"
            aria-label="Main navigation"
          >
            {activeItems.map(item => (
              <MenuItemComponent
                key={item.label || item.url}
                item={item}
                isActive={item.isActive}
              />
            ))}
          </nav>
        </TooltipProvider>
      </div>
      {user && (
        <div className="md:hidden fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-gray-200">
          <nav className="grid grid-cols-3 h-16">
            {mobileMenuItems.map(item => {
              const isActive = isItemActive(item, pathname)
              return (
                <Link
                  key={item.label || item.url}
                  href={item.url || '#'}
                  className={`flex flex-col items-center justify-center py-3 px-3 transition-colors text-gray-600 ${
                    isActive ? 'text-gray-900' : ''
                  }`}
                >
                  {item.icon && (
                    <item.icon
                      className={isActive ? 'text-gray-900' : 'text-gray-600'}
                    />
                  )}
                </Link>
              )
            })}
          </nav>
        </div>
      )}
    </>
  )
}
