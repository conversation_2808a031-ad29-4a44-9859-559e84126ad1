import { Users } from 'lucide-react'

import { getRecentCreators } from '@/actions'
import CreatorsSection from '@/components/common/CreatorsSection'

type Props = React.PropsWithChildren<{
  total?: number
}>

export default async function RecentCreatorsSection({ total = 2 }: Props) {
  const creators = await getRecentCreators(total)

  return (
    <CreatorsSection
      title="Recent Creators"
      icon={Users}
      iconColor="text-blue-500"
      creators={creators ?? []}
      stats={['joined']}
    />
  )
}
