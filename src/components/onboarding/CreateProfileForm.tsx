'use client'

import { <PERSON><PERSON><PERSON><PERSON>, useForm } from 'react-hook-form'
import { InputField } from '../form/InputField'

import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { updateUserAddingCreateProfileData } from '@/data/user/update-user-adding-create-profile-data'
import { Button } from '../ui/button'
import { useRouter } from 'next/navigation'
import { AvatarUploadWithCrop } from '../common/AvatarUploadWithCrop'
import { useState } from 'react'
import { checkIfHandleExists } from '@/data/user/check-if-handle-exists'
import { User } from '@/actions/user'

const formSchema = z.object({
  name: z
    .string({ required_error: 'Name is required' })
    .min(3, 'Name is required and must be at least 3 characters long'),
  handler: z
    .string({ required_error: 'Handler is required' })
    .min(3, 'Handler is required and must be at least 3 characters long'),
})

type FormValues = z.infer<typeof formSchema>

export default function CreateProfileForm({
  userId,
  userData,
}: {
  userId: string
  userData: User | null
}) {
  const router = useRouter()
  const [avatarUrl, setAvatarUrl] = useState<string | null>(
    userData?.avatar || null,
  )

  const methods = useForm<FormValues>({
    mode: 'onBlur',
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: userData?.profile?.name || '',
      handler: '',
    },
  })

  const onSubmit = async (data: FormValues) => {
    // Only check for handle conflicts if the handle is different from the current user's handle
    if (data.handler !== userData?.handler) {
      const userWithHandle = await checkIfHandleExists({
        handler: data.handler,
      })

      if (userWithHandle) {
        methods.setError('handler', {
          type: 'manual',
          message: 'This handle is already taken.',
        })

        return
      }
    }

    const response = await updateUserAddingCreateProfileData({
      userId,
      name: data.name,
      handler: data.handler,
      headline: '',
      avatar: avatarUrl || undefined,
    })

    if (response) {
      router.push('/onboarding/step/2')
    } else {
      methods.setError('root', {
        type: 'manual',
        message: 'Failed to update profile. Please try again.',
      })
    }
  }

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-6">
        <div className="flex flex-col items-center gap-2">
          <AvatarUploadWithCrop
            onUpload={setAvatarUrl}
            currentAvatar={userData?.avatar}
          />
          <span className="text-sm text-gray-600">Upload your avatar</span>
        </div>

        <div className="space-y-4">
          <InputField
            label="Name"
            name="name"
            required
            placeholder="John Doe"
          />
          <InputField
            label="Handle"
            name="handler"
            required
            placeholder="johndoe"
          />
        </div>

        <Button
          type="submit"
          disabled={
            !methods.formState.isValid || methods.formState.isSubmitting
          }
          className="w-full"
        >
          {methods.formState.isSubmitting ? 'Saving...' : 'Continue'}
        </Button>
      </form>
    </FormProvider>
  )
}
