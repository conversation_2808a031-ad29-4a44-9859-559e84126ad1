'use client'

import { useState, forwardRef, useImperative<PERSON>andle, useCallback } from 'react'
import { CardOption } from './CardOption'
import { setUserOnboardWithHat } from '@/actions/user/set-user-onboard-with-hat'
import { useRouter } from 'next/navigation'

const cardOptions = [
  {
    id: 'member',
    title: 'Get inspired & build faster',
    description:
      'Access ready-to-use templates to accelerate your workflow and explore exclusive projects for Webflow, Framer, and beyond.',
    icon: '/assets/heart.png',
  },
  {
    id: 'creator',
    title: 'Make money from my designs',
    description:
      'Sell templates, monetize your skills, and build recurring revenue. Go PRO to unlock bundles, subscriptions, and keep 100% of what you earn.',
    icon: '/assets/rocket.png',
  },
]

export interface WrapperWhatBringsYouHereRef {
  continue: () => Promise<void>
  isLoading: boolean
  selectedId: string | null
  hasSelection: boolean
}

const WrapperWhatBringsYouHere = forwardRef<
  WrapperWhatBringsYouHereRef,
  {
    userId?: string
    onContinue?: (selectedId: string) => void
    onSelectionChange?: (selectedId: string | null) => void
  }
>(({ userId, onContinue, onSelectionChange }, ref) => {
  const router = useRouter()
  const [activeId, setActiveId] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const handleContinue = useCallback(async () => {
    if (!userId || userId.trim() === '') {
      console.error('User ID is required to set onboard status')
      return
    }

    if (!activeId) {
      console.error('No option selected')
      return
    }

    try {
      setIsLoading(true)

      const user = await setUserOnboardWithHat(
        userId,
        activeId as 'creator' | 'member',
      )

      if (!user) {
        console.error('Failed to update user')
        return
      }

      if (onContinue) {
        onContinue(activeId)
      } else {
        router.push('/')
      }
    } catch (error) {
      console.error('Error updating user:', error)
    } finally {
      setIsLoading(false)
    }
  }, [userId, activeId, onContinue, router])

  // Expose the continue function and state to parent
  useImperativeHandle(
    ref,
    () => ({
      continue: handleContinue,
      isLoading,
      selectedId: activeId,
      hasSelection: activeId !== null,
    }),
    [handleContinue, isLoading, activeId],
  )

  return (
    <div>
      <h1 className="text-2xl font-semibold text-gray-900 mb-8 text-center">
        What brings you to Templace?
      </h1>
      <div className="max-w-4xl w-full mt-8">
        <div className="grid md:grid-cols-2 gap-6">
          {cardOptions.map(option => (
            <CardOption
              key={option.id}
              id={option.id}
              title={option.title}
              description={option.description}
              active={activeId === option.id}
              onClick={() => {
                setActiveId(option.id)
                onSelectionChange?.(option.id)
              }}
              isLoading={isLoading}
              icon={option.icon}
            />
          ))}
        </div>
      </div>
    </div>
  )
})

WrapperWhatBringsYouHere.displayName = 'WrapperWhatBringsYouHere'

export default WrapperWhatBringsYouHere
