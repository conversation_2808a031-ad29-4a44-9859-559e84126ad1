'use client'

import { usePathname } from 'next/navigation'

export default function StepIndicator() {
  const pathname = usePathname()

  let step = 1

  if (pathname === '/onboarding/step/2') {
    step = 2
  } else if (pathname === '/onboarding/step/1') {
    step = 1
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <span className="text-sm text-gray-500">Step {step}</span>
        <span className="text-sm text-gray-500">{step} of 2</span>
      </div>
      <div className="flex gap-2">
        <div className={`h-1 flex-1 rounded bg-black`}></div>
        <div
          className={`h-1 flex-1 rounded ${step === 2 ? 'bg-black' : 'bg-gray-200'}`}
        ></div>
      </div>
    </div>
  )
}
