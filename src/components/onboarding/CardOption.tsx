'use client'

import c from 'clsx'
import Image from 'next/image'

interface CardOptionProps {
  id: string
  title: string
  description: string
  icon: string
  active: boolean
  onClick: () => void
  isLoading?: boolean
}

export function CardOption({
  id,
  title,
  description,
  icon,
  active,
  isLoading,
  onClick,
}: CardOptionProps) {
  return (
    <div
      onClick={onClick}
      className={c(
        'cursor-pointer border-2 rounded-xl p-8 transition-colors bg-white',
        active ? 'border-gray-700' : 'border-gray-200 hover:border-gray-300',
      )}
    >
      <div className="flex items-start gap-4 mb-4">
        <Image
          src={icon}
          alt={title}
          width={64}
          height={64}
          className="size-16"
          loading="lazy"
          priority={false}
        />

        <div className="space-y-3">
          <h3 className="text-xl font-semibold text-gray-900">{title}</h3>
          <p className="text-gray-600 leading-relaxed text-sm">{description}</p>
        </div>
      </div>
    </div>
  )
}
