'use client'

import { startTransition, useState } from 'react'

import { sendTestEmail } from '@/actions/mailing'
import { type TemplateContent } from '@mailchimp/mailchimp_transactional'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'

import CodeBox from '@/components/common/CodeBox'

export default function SendTestEmail() {
  const [response, setResponse] = useState<any>()
  const [loading, setLoading] = useState(false)
  const [dest, setDest] = useState('<EMAIL>')
  const [templateId, setTemplateId] = useState('transfer-initiated')
  const [vars, setVars] = useState(`
[
  {
    "name": "NAME",
    "content": "Nome do Usuário"
  },
  {
    "name": "PRODUCT_NAME",
    "content": "Terracota color [RGB Pattern]"
  }
]`)

  const onClickHandler = () => {
    setLoading(true)
    startTransition(async () => {
      const respose = await sendTestEmail(dest, templateId, [
        ...JSON.parse(vars),
      ] as unknown as TemplateContent[])
      setLoading(false)
      setResponse(respose)
    })
  }

  return (
    <>
      <div className="flex flex-col place-items-start gap-4 w-1/2">
        <Input
          className="w-full"
          value={dest}
          onChange={e => setDest(e.target.value)}
          disabled={loading}
        />
        <Input
          className="w-full"
          value={templateId}
          onChange={e => setTemplateId(e.target.value)}
          disabled={loading}
        />

        <Textarea
          value={vars}
          rows={14}
          disabled={loading}
          className="font-mono"
          onChange={e => setVars(e.target.value)}
        />

        <Button className="mb-4" onClick={onClickHandler} disabled={loading}>
          Send test email
        </Button>
      </div>

      {response ? (
        <CodeBox className="w-1/2">{JSON.stringify(response, null, 2)}</CodeBox>
      ) : null}
    </>
  )
}
