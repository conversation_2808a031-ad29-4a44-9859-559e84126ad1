'use client'

import { useRouter, useSearchParams } from 'next/navigation'
import { format } from 'date-fns'
import { ChevronDown, ChevronUp } from 'lucide-react'

import { TransactionLogEntry } from '@/actions/payment/get-all-transaction-logs'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { getIdFromRecord } from '@/utils'
import centsToDollars from '@/utils/cents-to-dollars'

interface TransactionLogsTableProps {
  logs: TransactionLogEntry[]
  currentSort: {
    sortBy: string
    sortOrder: 'asc' | 'desc'
  }
}

export default function TransactionLogsTable({
  logs,
  currentSort,
}: TransactionLogsTableProps) {
  const router = useRouter()
  const searchParams = useSearchParams()

  const handleSort = (column: string) => {
    const params = new URLSearchParams(searchParams.toString())

    // If clicking the same column, toggle the order
    if (currentSort.sortBy === column) {
      params.set('sortOrder', currentSort.sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      // If clicking a new column, set it as sortBy with desc order
      params.set('sortBy', column)
      params.set('sortOrder', 'desc')
    }

    // Reset to first page when sorting changes
    params.set('page', '1')

    router.push(`?${params.toString()}`)
  }

  const SortIcon = ({ column }: { column: string }) => {
    if (currentSort.sortBy !== column) return null
    return currentSort.sortOrder === 'asc' ? (
      <ChevronUp className="w-4 h-4 ml-1 inline" />
    ) : (
      <ChevronDown className="w-4 h-4 ml-1 inline" />
    )
  }

  const sortableColumns = [
    'id',
    'buyer',
    'seller',
    'product',
    'amount',
    'date',
    'status',
  ]

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead
            className={
              sortableColumns.includes('id')
                ? 'cursor-pointer hover:bg-muted/50'
                : ''
            }
            onClick={() => handleSort('id')}
          >
            ID
            <SortIcon column="id" />
          </TableHead>

          <TableHead
            className={
              sortableColumns.includes('buyer')
                ? 'cursor-pointer hover:bg-muted/50'
                : ''
            }
            onClick={() => handleSort('buyer')}
          >
            Buyer
            <SortIcon column="buyer" />
          </TableHead>

          <TableHead
            className={
              sortableColumns.includes('seller')
                ? 'cursor-pointer hover:bg-muted/50'
                : ''
            }
            onClick={() => handleSort('seller')}
          >
            Seller
            <SortIcon column="seller" />
          </TableHead>

          <TableHead
            className={
              sortableColumns.includes('product')
                ? 'cursor-pointer hover:bg-muted/50'
                : ''
            }
            onClick={() => handleSort('product')}
          >
            Product
            <SortIcon column="product" />
          </TableHead>

          <TableHead
            className={
              sortableColumns.includes('amount')
                ? 'cursor-pointer hover:bg-muted/50'
                : ''
            }
            onClick={() => handleSort('amount')}
          >
            Amount
            <SortIcon column="amount" />
          </TableHead>

          <TableHead
            className={
              sortableColumns.includes('status')
                ? 'cursor-pointer hover:bg-muted/50'
                : ''
            }
            onClick={() => handleSort('status')}
          >
            Status
            <SortIcon column="status" />
          </TableHead>
          <TableHead
            className={
              sortableColumns.includes('date')
                ? 'cursor-pointer hover:bg-muted/50'
                : ''
            }
            onClick={() => handleSort('date')}
          >
            Date
            <SortIcon column="date" />
          </TableHead>
          <TableHead>Complete</TableHead>
        </TableRow>
      </TableHeader>

      <TableBody className="divide-y">
        {logs.map((log: TransactionLogEntry) => (
          <TableRow key={getIdFromRecord(log.id)}>
            <TableCell className="text-xs min-w-fit">
              <span className="font-mono text-xs">
                {getIdFromRecord(log.id).slice(-8)}
              </span>
            </TableCell>

            <TableCell className="max-w-[150px] truncate">
              <div className="flex flex-col">
                <span className="font-medium">{log.buyerName}</span>
                <span className="text-xs text-muted-foreground">
                  {log.buyerEmail}
                </span>
              </div>
            </TableCell>

            <TableCell className="max-w-[150px] truncate">
              <div className="flex flex-col">
                <span className="font-medium">{log.sellerName}</span>
                <span className="text-xs text-muted-foreground">
                  {log.sellerEmail}
                </span>
              </div>
            </TableCell>

            <TableCell className="max-w-[200px] truncate">
              <span title={log.productName}>{log.productName}</span>
            </TableCell>

            <TableCell>
              <span className="font-medium">
                {centsToDollars(log.amount)}{' '}
                {log.currency?.toUpperCase() || 'USD'}
              </span>
            </TableCell>

            <TableCell>
              {log.paymentStatus === 'complete' ? (
                <Badge size="sm" variant="green">
                  Complete
                </Badge>
              ) : log.paymentStatus === 'pending' ? (
                <Badge size="sm" variant="yellow">
                  Pending
                </Badge>
              ) : (
                <Badge size="sm" variant="destructive">
                  Failed
                </Badge>
              )}
            </TableCell>
            <TableCell className="text-xs min-w-fit w-[10%] whitespace-nowrap">
              {format(new Date(log.created), 'dd/MM/yyyy - HH:mm')}
            </TableCell>
            <TableCell className="w-[1%]">
              {log.paymentStatus === 'complete' ? (
                <Badge size="sm" variant="blue">
                  Yes
                </Badge>
              ) : (
                <Badge size="sm" variant="default">
                  No
                </Badge>
              )}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
