import Link from 'next/link'

import { getSends } from '@/actions/transfer'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

import { getIdFromRecord } from '@/utils'
import { ExternalLink } from 'lucide-react'
import ListSendsStatus from './ListSendsStatus'
import ManualIOrAutomatic from './ManualIOrAutomatic'
import { format } from 'date-fns'

export default async function ListSends() {
  const sends = await getSends()
  return (
    <div className="w-full mx-autos bg-background p-6">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>id</TableHead>
            <TableHead>created</TableHead>
            <TableHead>manual</TableHead>
            <TableHead>product</TableHead>
            <TableHead>form</TableHead>
            <TableHead>to</TableHead>
            <TableHead>status</TableHead>
          </TableRow>
        </TableHeader>

        <TableBody>
          {sends?.map(item => {
            return (
              <TableRow key={getIdFromRecord(item.id)}>
                <TableCell className="w-[12%] whitespace-nowrap">
                  <Link href={`/backoffice/sends/${getIdFromRecord(item.id)}`}>
                    {getIdFromRecord(item.id)}
                  </Link>
                </TableCell>

                <TableCell className="w-[8%] whitespace-nowrap">
                  {format(item.created, 'yyyy-MM-dd@HH:mm')}
                </TableCell>

                <TableCell className="w-[12%] whitespace-nowrap">
                  <ManualIOrAutomatic manual={item?.manual} />
                </TableCell>

                <TableCell className="w-auto whitespace-nowrap">
                  {item?.product?.slug ? (
                    <Link
                      href={`/product/${item?.product?.slug}`}
                      className="flex gap-1 items-center hover:text-primary"
                      target="_blank"
                    >
                      <ExternalLink className="w-4 h-4" />
                      {item?.product?.name}
                    </Link>
                  ) : (
                    <span className="text-muted-foreground">
                      not found or disabled
                    </span>
                  )}
                </TableCell>

                <TableCell>{item.fromUser?.profile?.webflow?.email}</TableCell>

                <TableCell>{item.toUser?.profile?.webflow?.email}</TableCell>

                <TableCell>
                  <ListSendsStatus status={item.status} />
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
    </div>
  )
}
