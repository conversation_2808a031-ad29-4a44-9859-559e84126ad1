import {
  getAllTransactionLogs,
  TransactionLogEntry,
} from '@/actions/payment/get-all-transaction-logs'
import TransactionLogsTable from './TransactionLogsTable'
import TransactionLogsPagination from './TransactionLogsPagination'

interface BackofficeTransactionLogsProps {
  page: number
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

const ITEMS_PER_PAGE = 50

export default async function BackofficeTransactionLogs({
  page,
  sortBy,
  sortOrder,
}: BackofficeTransactionLogsProps) {
  const offset = (page - 1) * ITEMS_PER_PAGE
  const { logs, total } = await getAllTransactionLogs(
    ITEMS_PER_PAGE,
    offset,
    sortBy,
    sortOrder,
  )

  const totalPages = Math.ceil(total / ITEMS_PER_PAGE)

  return (
    <div className="flex flex-col w-full gap-8">
      <section className="flex flex-col w-full min-h-[22dvh] border-foreground/10 border-[1px] rounded-md">
        {logs.length > 0 ? (
          <>
            <TransactionLogsTable
              logs={logs}
              currentSort={{ sortBy, sortOrder }}
            />
            <TransactionLogsPagination
              currentPage={page}
              totalPages={totalPages}
              totalItems={total}
              itemsPerPage={ITEMS_PER_PAGE}
            />
          </>
        ) : (
          <div className="w-full h-full grid place-items-center">
            <span className="text-1xl text-muted-foreground py-4">
              No transaction logs found.
            </span>
          </div>
        )}
      </section>
    </div>
  )
}
