import { <PERSON>Lightning, MousePointerClick } from 'lucide-react'

import { Badge } from '@/components/ui/badge'

type Props = React.PropsWithChildren<{
  manual?: boolean
}>

export default function ManualIOrAutomatic({ manual }: Props) {
  return manual ? (
    <Badge variant="yellow">
      <MousePointerClick className="w-4 h-4" />
      Manual
    </Badge>
  ) : (
    <Badge variant="green">
      <CloudLightning className="w-4 h-4" />
      Automatic
    </Badge>
  )
}
