'use client'

import { ChevronsUpDown } from 'lucide-react'
import { startTransition, useState } from 'react'

import { UserRelationString } from '@/actions/user'
import { adminUpdateUserRelationStatus } from '@/actions/user/admin-update-user-relation-status'
import { Button } from '@/components/ui/button'
import {
  Command,
  CommandGroup,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { cn } from '@/lib/utils'

type Props = {
  userStatus?: string
  userId: string
}

/**
 *
 */
export default function UpdateUserStatus({ userStatus, userId }: Props) {
  const [status, setStatus] = useState<string>(userStatus ?? '')
  const [open, setOpen] = useState(false)

  const UserRelationValues = [
    'UserRelation/FLAT',
    'UserRelation/ELITE_CREATOR',
    'UserRelation/TOP_CONTRIBUTOR',
    'UserRelation/RISING_STAR',
  ]

  const handleStatusChange = (newStatus: string) => {
    startTransition(async () => {
      await adminUpdateUserRelationStatus(userId, newStatus)
      setStatus(newStatus)
      setOpen(false)
    })
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" className="w-[200px] justify-between">
          {UserRelationString[status as keyof typeof UserRelationString] ||
            'Selecione'}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandList>
            <CommandGroup>
              {UserRelationValues.map(relation => (
                <CommandItem
                  key={relation}
                  value={relation}
                  onSelect={() => handleStatusChange(relation)}
                  className={cn(
                    'cursor-pointer',
                    status === relation && 'bg-foreground/40 text-muted',
                  )}
                >
                  {
                    UserRelationString[
                      relation as keyof typeof UserRelationString
                    ]
                  }
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
