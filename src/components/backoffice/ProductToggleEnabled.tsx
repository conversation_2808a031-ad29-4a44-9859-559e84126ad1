'use client'

import { startTransition, useState } from 'react'

import { toggleProductEnabled } from '@/actions/product'
import { Switch } from '@/components/ui/switch'
import { useToast } from '@/hooks/use-toast'

type Props = {
  productId: string
  initialEnabled?: boolean
}

export default function ProductToggleEnabled({
  productId,
  initialEnabled = true,
}: Props) {
  const [enabled, setEnabled] = useState<boolean>(initialEnabled)
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const handleToggle = (checked: boolean) => {
    setIsLoading(true)

    startTransition(async () => {
      try {
        const newStatus = await toggleProductEnabled(productId)
        setEnabled(newStatus)

        toast({
          title: 'Success',
          description: newStatus
            ? 'Product enabled successfully'
            : 'Product disabled and unpublished successfully',
        })
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Failed to update product status',
          variant: 'destructive',
        })
        console.error('Error toggling product:', error)
        setEnabled(enabled)
      } finally {
        setIsLoading(false)
      }
    })
  }

  return (
    <Switch
      checked={enabled}
      onCheckedChange={handleToggle}
      disabled={isLoading}
      aria-label={`${enabled ? 'Disable' : 'Enable'} product`}
    />
  )
}
