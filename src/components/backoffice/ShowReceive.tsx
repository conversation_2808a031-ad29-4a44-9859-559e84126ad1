import { nanoid } from 'nanoid'
import { Timeline, TimelineStep } from '@/components/ui/timeline'
import { Badge } from '@/components/ui/badge'
import { getReceiveById } from '@/actions/transfer/get-receive-by-id'

import ShowTransferImage from './ShowTransferImage'

const mapLevelToBadgeColor: any = {
  error: 'red',
  warn: 'yellow',
  info: 'blue',
  verbose: 'green',
  silly: 'purple',
}

function createImageTimestamp(item: any) {
  const timeExtracted =
    item?.split('screenshots')?.pop()?.slice(1)?.split('-')?.shift() || '0'
  return new Date(parseInt(timeExtracted, 10))
}

function mapImageToLogLine(item: any) {
  return {
    level: 'verbose',
    image: item,
    timestamp: createImageTimestamp(item),
  }
}

function generateTimeline(receive: any) {
  const images = receive?.stake
    ?.filter((item: any) => !item.endsWith('.log'))
    .map(mapImageToLogLine)

  const logs = receive?.logs?.map((item: any) => {
    return { ...item, timestamp: new Date(item.timestamp) }
  })

  const timeline = (receive?.stake && receive?.logs ? [...images, ...logs] : [])
    .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())
    .map(item => {
      return {
        ...item,
        lineId: nanoid(),
      }
    })

  return timeline
}

export default async function ShowReceive({ id }: any) {
  const receive = await getReceiveById(id)
  const timeline: any[] = generateTimeline(receive)

  return (
    <div className="w-full bg-background p-6">
      <div className="w-[100%] mx-auto p-6">
        <Timeline>
          {timeline?.map(item => {
            return (
              <TimelineStep key={item.lineId}>
                {item.image ? <ShowTransferImage image={item.image} /> : null}
                {item.message ? (
                  <div>
                    <Badge
                      className="inline-block mr-2"
                      variant={mapLevelToBadgeColor?.[item?.level] ?? 'default'}
                    >
                      {item?.level}
                    </Badge>
                    <pre className="inline whitespace-pre-wrap text-xs">
                      {String(item.message).trim()}
                    </pre>
                  </div>
                ) : null}
              </TimelineStep>
            )
          })}
        </Timeline>
      </div>
    </div>
  )
}
