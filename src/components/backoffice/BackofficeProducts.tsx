import { getAllProducts } from '@/actions/product'
import ProductsTable from '@/components/backoffice/ProductsTable'
import ProductsPagination from '@/components/backoffice/ProductsPagination'

interface BackofficeProductsProps {
  page: number
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

const ITEMS_PER_PAGE = 20

export default async function BackofficeProducts({
  page,
  sortBy,
  sortOrder,
}: BackofficeProductsProps) {
  const offset = (page - 1) * ITEMS_PER_PAGE
  const { products, total, totalPages } = await getAllProducts(
    ITEMS_PER_PAGE,
    offset,
    sortBy,
    sortOrder,
  )

  return (
    <div className="w-full mx-auto bg-background p-6">
      <section className="flex flex-col w-full min-h-[22dvh] border-foreground/10 border-[1px] rounded-md">
        {products && products.length > 0 ? (
          <>
            <ProductsTable
              products={products}
              currentSort={{ sortBy, sortOrder }}
            />
            <ProductsPagination
              currentPage={page}
              totalPages={totalPages}
              totalItems={total}
              itemsPerPage={ITEMS_PER_PAGE}
            />
          </>
        ) : (
          <div className="w-full h-full grid place-items-center">
            <span className="text-1xl text-muted-foreground py-4">
              No products found.
            </span>
          </div>
        )}
      </section>
    </div>
  )
}
