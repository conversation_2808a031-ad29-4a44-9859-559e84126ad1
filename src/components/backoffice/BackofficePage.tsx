'use client'

import { usePathname } from 'next/navigation'
import { useBreadcrumbsOverrideStore } from '@/store/breadcrumbs'
import { useEffect } from 'react'

interface BreadcrumbItem {
  label: string
  href?: string
}

interface BackofficePageProps {
  breadcrumbs?: BreadcrumbItem[]
  children: React.ReactNode
}

/**
 * Hook to define breadcrumbs declaratively
 *
 * Use this hook in components that need to define specific breadcrumbs.
 * It's cleaner and more explicit than automatic synchronization.
 *
 * @param breadcrumbs - Array of breadcrumbs for the current path
 */
export function useBreadcrumbs(breadcrumbs: BreadcrumbItem[]) {
  const pathname = usePathname()
  const { setOverrideForPath, clearOverrideForPath } =
    useBreadcrumbsOverrideStore()

  useEffect(() => {
    if (breadcrumbs.length > 0) {
      setOverrideForPath(pathname, breadcrumbs)
    } else {
      clearOverrideForPath(pathname)
    }

    // Cleanup: remove breadcrumbs when component unmounts
    return () => {
      clearOverrideForPath(pathname)
    }
  }, [pathname, breadcrumbs, setOverrideForPath, clearOverrideForPath])
}

/**
 * BackofficePage - Purely declarative component
 *
 * This component doesn't manage breadcrumbs automatically.
 * Use the `useBreadcrumbs` hook in child components when you need
 * to define specific breadcrumbs.
 *
 * Example usage:
 * ```tsx
 * function UsersPage() {
 *   useBreadcrumbs([
 *     { label: 'Users', href: '/backoffice/users' }
 *   ])
 *
 *   return <div>Users content</div>
 * }
 *
 * function SomePage() {
 *   return (
 *     <BackofficePage>
 *       <UsersPage />
 *     </BackofficePage>
 *   )
 * }
 * ```
 */
export default function BackofficePage({
  children,
}: Omit<BackofficePageProps, 'breadcrumbs'>) {
  return <>{children}</>
}
