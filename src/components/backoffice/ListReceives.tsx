import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { ExternalLink } from 'lucide-react'
import Link from 'next/link'

import { getReceives } from '@/actions/transfer/get-receives'
import ListReceivesStatus from '@/components/backoffice/ListReceivesStatus'
import ManualIOrAutomatic from '@/components/backoffice/ManualIOrAutomatic'
import { getIdFromRecord } from '@/utils'

export default async function ListReceives() {
  const receives = await getReceives()
  return (
    <div className="w-full mx-autos bg-background p-6">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>id</TableHead>
            <TableHead>manual</TableHead>
            <TableHead>product</TableHead>
            <TableHead>incomingId</TableHead>
            <TableHead>status</TableHead>
          </TableRow>
        </TableHeader>

        <TableBody>
          {receives?.map(item => (
            <TableRow key={getIdFromRecord(item.id)}>
              <TableCell className="w-[12%] whitespace-nowrap">
                <Link href={`/backoffice/receives/${getIdFromRecord(item.id)}`}>
                  {getIdFromRecord(item.id)}
                </Link>
              </TableCell>

              <TableCell className="w-[12%] whitespace-nowrap">
                <ManualIOrAutomatic manual={item?.manual} />
              </TableCell>

              <TableCell className="w-auto whitespace-nowrap">
                <Link
                  href={`/product/${item?.product?.slug}`}
                  className="flex gap-1 items-center hover:text-primary"
                  target="_blank"
                >
                  <ExternalLink className="w-4 h-4" />
                  {item?.product?.name}
                </Link>
              </TableCell>

              <TableCell className="w-[1%] whitespace-nowrap">
                {item?.incomingId}
              </TableCell>

              <TableCell className="w-[1%]">
                <ListReceivesStatus status={item?.status} />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
