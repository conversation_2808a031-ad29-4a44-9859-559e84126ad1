'use client'

import React from 'react'
import {
  <PERSON><PERSON>crumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { ArrowLeft } from 'lucide-react'
import useSessionUser from '@/hooks/useSessionUser'
import useUser from '@/hooks/useUser'

export default function BackofficeBreadcrumbs({ label, list }: any) {
  const { data: userSession } = useSessionUser()
  const userId = userSession?.id ? String(userSession.id) : ''
  const { data: user } = useUser(userId)

  const profileHref = user?.handler ? `/user/${user.handler}` : '/dashboard'

  return (
    <Breadcrumb aria-label={label || 'Navigation breadcrumb'}>
      <BreadcrumbList>
        <Button variant="pill" asChild className="mr-4 h-[2rem]">
          <Link href={profileHref}>
            <ArrowLeft />
            Perfil
          </Link>
        </Button>

        {list.map((item: any, index: number) => (
          <React.Fragment key={item.label}>
            <BreadcrumbItem>
              {item.href ? (
                <BreadcrumbLink href={item.href}>{item.label}</BreadcrumbLink>
              ) : (
                <BreadcrumbPage>{item.label}</BreadcrumbPage>
              )}
            </BreadcrumbItem>
            {index !== list.length - 1 && <BreadcrumbSeparator />}
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  )
}
