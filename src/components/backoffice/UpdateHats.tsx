'use client'

import { startTransition, useState } from 'react'

import { UserHat } from '@/actions/user'
import TagSelector from '@/components/common/TagSelector'
import { adminUpdateUserHats } from '@/actions/user/admin-update-user-hats'

type Props = {
  userId: string
  userHats: UserHat[]
  systemHats: UserHat[]
}

export default function UpdateHats({ userId, userHats, systemHats }: Props) {
  const filteredUserHats = userHats.filter(hat => hat.slug !== 'admin')
  const filteredSystemHats = systemHats.filter(hat => hat.slug !== 'admin')

  // Only one hat can be selected - get the first non-admin hat from user
  const [selectedHat, setSelectedHat] = useState<string>(
    filteredUserHats[0]?.id.toString() || '',
  )

  const handleHatToggle = (hatId: string) =>
    startTransition(async () => {
      // If hat is already selected, do nothing (no deselection allowed)
      // If it's a different hat, replace the current one
      if (hatId !== selectedHat) {
        await adminUpdateUserHats(userId, [hatId])
        setSelectedHat(hatId)
      }
    })

  return (
    <TagSelector
      tags={filteredSystemHats.map(hat => ({
        id: hat.id.toString(),
        name: hat?.name ?? '',
      }))}
      selectedTags={selectedHat ? [selectedHat] : []}
      onTagToggle={handleHatToggle}
      className="w-max"
    />
  )
}
