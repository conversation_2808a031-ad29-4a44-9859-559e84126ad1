import { format } from 'date-fns'

import { getCompleteTransfers } from '@/actions/transfer'
import ChangeTransferStatus from '@/components/backoffice/ChangeTransferStatus'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { getIdFromRecord } from '@/utils'

export default async function BackofficeCompleteTransfers() {
  const transfers = await getCompleteTransfers()
  return (
    <div className="flex flex-col w-full gap-8">
      <h2 className="text-3xl text-foreground">Complete Transfers</h2>
      <section className="flex flex-col w-full min-h-[22dvh] border-foreground/10 border-[1px] rounded-md">
        {transfers.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="p-4">
                  <Checkbox />
                </TableHead>
                <TableHead>created</TableHead>
                <TableHead>updated</TableHead>
                <TableHead>from</TableHead>
                <TableHead>to</TableHead>
                <TableHead>product</TableHead>
                <TableHead>delivery</TableHead>
                <TableHead>status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="divide-y">
              {transfers.map(transfer => (
                <TableRow key={getIdFromRecord(transfer.id)}>
                  <TableCell className="p-4">
                    <Checkbox />
                  </TableCell>

                  <TableCell className="text-xs min-w-fit w-[1%] whitespace-nowrap">
                    {format(transfer.created, 'MM/dd/yyyy HH:mm')}
                  </TableCell>

                  <TableCell className="text-xs min-w-fit w-[1%] whitespace-nowrap">
                    {format(transfer.updated, 'MM/dd/yyyy HH:mm')}
                  </TableCell>

                  <TableCell>
                    {transfer.fromUser?.profile?.webflow?.email}
                  </TableCell>
                  <TableCell>
                    {transfer.toUser?.profile?.webflow?.email}
                  </TableCell>
                  <TableCell>{transfer?.product?.name}</TableCell>
                  <TableCell>
                    {transfer.manual ? (
                      <Badge size="sm" variant="blue">
                        Manual
                      </Badge>
                    ) : (
                      <Badge size="sm" variant="green">
                        Automatic
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell className="min-w-[200px]">
                    <ChangeTransferStatus transfer={transfer} />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <div className="w-full h-full grid place-items-center">
            <span className="text-1xl text-muted-foreground py-4">
              You completed no transfers yet.
            </span>
          </div>
        )}
      </section>
    </div>
  )
}
