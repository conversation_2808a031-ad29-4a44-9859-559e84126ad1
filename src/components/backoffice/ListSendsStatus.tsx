import { TransferReceiveStatus, TransferSendStatus } from '@/actions/transfer'
import { Badge } from '@/components/ui/badge'

interface Props {
  status?: TransferSendStatus
}

export default function ListSendsStatus({ status }: Props) {
  if (!status) {
    return '-'
  }

  const label = status.split('/').pop()

  if (status === TransferSendStatus.COMPLETE) {
    return <Badge variant="green">{label}</Badge>
  }

  if (status === TransferSendStatus.INITIAL) {
    return <Badge variant="blue">{label}</Badge>
  }

  if (status === TransferSendStatus.PENDING) {
    return <Badge variant="yellow">{label}</Badge>
  }

  return <Badge variant="default">{label}</Badge>
}
