import { getSendById } from '@/actions/transfer/get-send-by-id'
import { Badge } from '@/components/ui/badge'
import { Timeline, TimelineStep } from '@/components/ui/timeline'
import { nanoid } from 'nanoid'
import ShowTransferImage from './ShowTransferImage'

const mapLevelToBadgeColor: any = {
  error: 'destructive',
  warn: 'yellow',
  info: 'blue',
  verbose: 'light',
}

export default async function ShowSend({ id }: any) {
  const receive = await getSendById(id)
  const timeline: any[] = (
    receive?.stake && receive?.logs
      ? [
          ...(Array.isArray(receive?.stake)
            ? receive.stake
                .filter(item => !item.endsWith('.log'))
                .map(item => {
                  const splitted =
                    item
                      ?.split(`${id.toString() || ''}/screenshots`)
                      ?.pop()
                      ?.slice(1)
                      ?.split('-')
                      ?.shift() || '0'
                  const timestamp = new Date(parseInt(splitted, 10))

                  return {
                    lineId: nanoid(),
                    level: 'verbose',
                    image: item,
                    timestamp,
                  }
                })
            : []),
          ...(Array.isArray(receive?.logs)
            ? receive.logs.map(item => {
                return {
                  ...item,
                  timestamp: new Date(item.timestamp),
                  lineId: nanoid(),
                }
              })
            : []),
        ]
      : []
  ).sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())

  return (
    <div className="w-full bg-background p-6">
      <div className="w-[100%] mx-auto p-6">
        <Timeline>
          {timeline?.map(item => {
            if (item?.message && typeof item?.message !== 'string') {
              console.log('message', typeof item?.message, item?.message)
            }
            return (
              <TimelineStep key={item.lineId}>
                <div className="relative flex flex-col gap-2">
                  <div className="flex flex-row-reverse items-center gap-2 absolute top-0 right-[calc(100%+3rem)]">
                    <Badge
                      size="sm"
                      variant={mapLevelToBadgeColor?.[item?.level] ?? 'default'}
                    >
                      {item?.level}
                    </Badge>
                    <span className="text-xs whitespace-nowrap">
                      {item?.timestamp.toLocaleString()}
                    </span>
                  </div>

                  {item?.image ? (
                    <ShowTransferImage image={item.image} />
                  ) : null}

                  {item?.message ? (
                    <pre className="whitespace-pre-wrap text-xs">
                      {item?.message}
                    </pre>
                  ) : null}
                </div>
              </TimelineStep>
            )
          })}
        </Timeline>
      </div>
    </div>
  )
}
