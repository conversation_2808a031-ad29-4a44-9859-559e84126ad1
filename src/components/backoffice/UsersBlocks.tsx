import { countOpenTransfers } from '@/actions/transfer/count-open-transfers'
import { countCompleteTransfers } from '@/actions/transfer/countCompleteTransfers'
import { countNonAdminUsers } from '@/actions/user/count-non-admin-users'
import { cn } from '@/lib/utils'

export default async function UsersBlocks({
  className,
}: {
  className?: string
}) {
  const openTransfers = await countOpenTransfers()
  const nonAdminUsers = await countNonAdminUsers()
  // const completeTransfers = await countCompleteTransfers()

  return (
    <div
      className={cn(
        'grid grid-cols-4 gap-4',
        className,
        '[&>*:nth-child(1)]:col-start-4 [&>*:nth-child(1)]:order-4',
        '[&>*:nth-child(2)]:col-start-3 [&>*:nth-child(2)]:order-3',
        '[&>*:nth-child(3)]:col-start-2 [&>*:nth-child(3)]:order-2',
        '[&>*:nth-child(4)]:col-start-1 [&>*:nth-child(4)]:order-1',
      )}
    >
      {/* <div className="p-4 bg-background text-foreground rounded-lg ring-1 ring-green-600">
        <div className="text-md font-medium mb-2">Complete transfers</div>
        <div className="text-6xl font-bold">{String(completeTransfers)}</div>
      </div> */}

      <div className="text-md font-medium mb-2">Non-admin users</div>
      <div className="text-4xl font-bold">{String(nonAdminUsers)}</div>
    </div>
  )
}
