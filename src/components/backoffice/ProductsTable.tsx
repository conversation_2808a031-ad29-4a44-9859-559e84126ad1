'use client'

import { Ch<PERSON><PERSON>Up, ChevronDown, ExternalLink } from 'lucide-react'
import { format } from 'date-fns'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'

import { Product } from '@/actions/product'
import ProductToggleEnabled from '@/components/backoffice/ProductToggleEnabled'
import ProductStatusBadge from '@/components/products/ProductStatusBadge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { HOST } from '@/config'
import { getIdFromRecord } from '@/utils'
import centsToDollars from '@/utils/cents-to-dollars'
import { PaymentPrice } from '@/actions/price'

interface ProductsTableProps {
  products: Product[]
  currentSort: {
    sortBy: string
    sortOrder: 'asc' | 'desc'
  }
}

export default function ProductsTable({
  products,
  currentSort,
}: ProductsTableProps) {
  const router = useRouter()
  const searchParams = useSearchParams()

  const handleSort = (column: string) => {
    const params = new URLSearchParams(searchParams.toString())

    // If clicking the same column, toggle the order
    if (currentSort.sortBy === column) {
      params.set('sortOrder', currentSort.sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      // If clicking a new column, set it as sortBy with desc order
      params.set('sortBy', column)
      params.set('sortOrder', 'desc')
    }

    // Reset to first page when sorting changes
    params.set('page', '1')

    router.push(`?${params.toString()}`)
  }

  const SortIcon = ({ column }: { column: string }) => {
    if (currentSort.sortBy !== column) return null
    return currentSort.sortOrder === 'asc' ? (
      <ChevronUp className="w-4 h-4 ml-1 inline" />
    ) : (
      <ChevronDown className="w-4 h-4 ml-1 inline" />
    )
  }

  const sortableColumns = [
    'id',
    'name',
    'owner',
    'price',
    'status',
    'enabled',
    'created',
  ]

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead
            className={`cursor-pointer select-none ${
              sortableColumns.includes('id') ? 'hover:bg-muted/50' : ''
            }`}
            onClick={() => sortableColumns.includes('id') && handleSort('id')}
          >
            ID
            {sortableColumns.includes('id') && <SortIcon column="id" />}
          </TableHead>
          <TableHead
            className={`cursor-pointer select-none ${
              sortableColumns.includes('name') ? 'hover:bg-muted/50' : ''
            }`}
            onClick={() =>
              sortableColumns.includes('name') && handleSort('name')
            }
          >
            Name
            {sortableColumns.includes('name') && <SortIcon column="name" />}
          </TableHead>
          <TableHead
            className={`cursor-pointer select-none ${
              sortableColumns.includes('owner') ? 'hover:bg-muted/50' : ''
            }`}
            onClick={() =>
              sortableColumns.includes('owner') && handleSort('owner')
            }
          >
            Owner
            {sortableColumns.includes('owner') && <SortIcon column="owner" />}
          </TableHead>
          <TableHead
            className={`cursor-pointer select-none ${
              sortableColumns.includes('price') ? 'hover:bg-muted/50' : ''
            }`}
            onClick={() =>
              sortableColumns.includes('price') && handleSort('price')
            }
          >
            Price
            {sortableColumns.includes('price') && <SortIcon column="price" />}
          </TableHead>
          <TableHead
            className={`cursor-pointer select-none ${
              sortableColumns.includes('status') ? 'hover:bg-muted/50' : ''
            }`}
            onClick={() =>
              sortableColumns.includes('status') && handleSort('status')
            }
          >
            Status
            {sortableColumns.includes('status') && <SortIcon column="status" />}
          </TableHead>
          <TableHead>Link</TableHead>
          <TableHead
            className={`cursor-pointer select-none ${
              sortableColumns.includes('created') ? 'hover:bg-muted/50' : ''
            }`}
            onClick={() =>
              sortableColumns.includes('created') && handleSort('created')
            }
          >
            Created
            {sortableColumns.includes('created') && (
              <SortIcon column="created" />
            )}
          </TableHead>
          <TableHead
            className={`cursor-pointer select-none ${
              sortableColumns.includes('enabled') ? 'hover:bg-muted/50' : ''
            }`}
            onClick={() =>
              sortableColumns.includes('enabled') && handleSort('enabled')
            }
          >
            Active
            {sortableColumns.includes('enabled') && (
              <SortIcon column="enabled" />
            )}
          </TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>

      <TableBody>
        {products?.map(product => (
          <TableRow key={getIdFromRecord(product.id)}>
            <TableCell className="w-[10%] whitespace-nowrap">
              {getIdFromRecord(product.id)}
            </TableCell>

            <TableCell className="w-[20%]">
              <div className="flex flex-col">
                <span className="font-medium">{product.name}</span>
                <span className="text-sm text-muted-foreground">
                  {product.slug}
                </span>
              </div>
            </TableCell>

            <TableCell className="w-[15%]">
              <Link
                href={`${HOST ?? window.location.origin}/user/${product.owner?.handler}`}
                target="_blank"
                className="inline-flex gap-1 items-center border-b-[1px] border-transparent hover:border-primary"
              >
                @{product.owner?.handler}
                <ExternalLink className="w-3 h-3" />
              </Link>
            </TableCell>

            <TableCell className="w-[10%]">
              {centsToDollars((product?.price as PaymentPrice)?.amount)}
            </TableCell>

            <TableCell className="w-[12%]">
              <ProductStatusBadge status={product.status} />
            </TableCell>

            <TableCell className="w-[10%]">
              <Link
                href={`${HOST ?? window.location.origin}/product/${product.slug}`}
                target="_blank"
                className="inline-flex gap-1 items-center border-b-[1px] border-transparent hover:border-primary"
              >
                View Product
                <ExternalLink className="w-3 h-3" />
              </Link>
            </TableCell>

            <TableCell className="w-[12%]">
              {product?.created
                ? format(new Date(product.created), 'dd/MM/yyyy - HH:mm')
                : '-'}
            </TableCell>

            <TableCell className="w-[10%]">
              <Badge variant={product.enabled ? 'default' : 'secondary'}>
                {product.enabled ? 'Active' : 'Disabled'}
              </Badge>
            </TableCell>

            <TableCell className="w-[10%]">
              <ProductToggleEnabled
                productId={getIdFromRecord(product.id)}
                initialEnabled={product.enabled}
              />
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
