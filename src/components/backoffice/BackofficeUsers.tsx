import { getNonAdminUsersPaginated } from '@/actions/user/get-non-admin-users-paginated'
import { getSystemUserHats } from '@/actions/user/get-system-user-hats'
import UsersTable from '@/components/backoffice/UsersTable'
import UsersPagination from '@/components/backoffice/UsersPagination'

interface BackofficeUsersProps {
  page: number
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

const ITEMS_PER_PAGE = 10

export default async function BackofficeUsers({
  page,
  sortBy,
  sortOrder,
}: BackofficeUsersProps) {
  const offset = (page - 1) * ITEMS_PER_PAGE
  const { users, total } = await getNonAdminUsersPaginated(
    ITEMS_PER_PAGE,
    offset,
    sortBy,
    sortOrder,
  )
  const systemHats = await getSystemUserHats()

  const totalPages = Math.ceil(total / ITEMS_PER_PAGE)

  return (
    <div className="w-full mx-auto bg-background p-6 mb-10">
      <section className="flex flex-col w-full min-h-[22dvh] border-foreground/10 border-[1px] rounded-md">
        {users && users.length > 0 ? (
          <>
            <UsersTable
              users={users}
              systemHats={systemHats ?? []}
              currentSort={{ sortBy, sortOrder }}
            />
            <UsersPagination
              currentPage={page}
              totalPages={totalPages}
              totalItems={total}
              itemsPerPage={ITEMS_PER_PAGE}
            />
          </>
        ) : (
          <div className="w-full h-full grid place-items-center">
            <span className="text-1xl text-muted-foreground py-4">
              No users found.
            </span>
          </div>
        )}
      </section>
    </div>
  )
}
