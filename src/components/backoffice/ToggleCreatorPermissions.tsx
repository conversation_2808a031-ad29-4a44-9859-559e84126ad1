'use client'

import { startTransition, useState } from 'react'
import { Switch } from '@/components/ui/switch'
import { UserHat } from '@/actions/user'
import { updateCreatorPermissions } from '@/actions/user/update-creator-permissions'
import { useToast } from '@/hooks/use-toast'

type Props = {
  userId: string
  userHats: UserHat[]
  userProfile?: any
}

export default function ToggleCreatorPermissions({
  userId,
  userHats,
  userProfile,
}: Props) {
  const { toast } = useToast()

  // Check if user is creator
  const isCreator = userHats.some(hat => hat.slug === 'creator')
  const isMember = userHats.some(hat => hat.slug === 'member')

  // Get initial state from user profile, default to false for new users
  const initialCanAddContent = userProfile?.permissions?.canAddContent ?? false
  const [canAddContent, setCanAddContent] = useState(initialCanAddContent)
  const [isLoading, setIsLoading] = useState(false)

  const handleToggle = (checked: boolean) => {
    if (isLoading) return

    setIsLoading(true)
    startTransition(async () => {
      try {
        await updateCreatorPermissions(userId, checked)
        setCanAddContent(checked)
        toast({
          title: 'Permissions updated',
          description: `User ${checked ? 'can' : 'cannot'} add templates/components.`,
        })
      } catch (error) {
        console.error('Error updating creator permissions:', error)
        toast({
          title: 'Error',
          description: 'Unable to update permissions.',
          variant: 'destructive',
        })
      } finally {
        setIsLoading(false)
      }
    })
  }

  // Only show switch for creators, disable for members
  if (isMember && !isCreator) {
    return <Switch checked={false} disabled={true} className="opacity-50" />
  }

  if (!isCreator) {
    return <span className="text-xs text-gray-400">-</span>
  }

  return (
    <Switch
      checked={canAddContent}
      onCheckedChange={handleToggle}
      disabled={isLoading}
    />
  )
}
