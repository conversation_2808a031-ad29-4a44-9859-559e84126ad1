'use client'

import React, { startTransition } from 'react'
import { ChevronsUpDown } from 'lucide-react'
import {
  Command,
  CommandGroup,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'

import { setTransferStatus } from '@/actions/transfer/setTransferStatus'
import { TransferSendStatus } from '@/actions/transfer'
import { Button } from '@/components/ui/button'

export default function ChangeTransferStatus({ transfer }: any) {
  const [open, setOpen] = React.useState(false)
  const [status, setStatus] = React.useState(transfer.status)

  const statusList = [
    TransferSendStatus.INITIAL,
    TransferSendStatus.PENDING,
    TransferSendStatus.PROGRESS,
    TransferSendStatus.COMPLETE,
    TransferSendStatus.CANCELED,
    TransferSendStatus.UNKNOWN,
  ]

  const statusColors = {
    [TransferSendStatus.INITIAL]: 'bg-black',
    [TransferSendStatus.PENDING]: 'bg-yellow-600',
    [TransferSendStatus.PROGRESS]: 'bg-blue-600',
    [TransferSendStatus.COMPLETE]: 'bg-green-600',
    [TransferSendStatus.MULTIPLE]: 'bg-orange-600',
    [TransferSendStatus.CANCELED]: 'bg-red-600',
    [TransferSendStatus.UNKNOWN]: 'bg-gray-600',
  }

  const onClickStatusHandler = (nextStatus: TransferSendStatus) => () => {
    startTransition(async () => {
      await setTransferStatus(transfer.id, nextStatus, true)
      setStatus(nextStatus)
      window.location.reload()
    })
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button className={statusColors[transfer.status as TransferSendStatus]}>
          {transfer.status}
          <ChevronsUpDown className="opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandList>
            <CommandGroup>
              {statusList.map(item => {
                return (
                  <CommandItem
                    key={item}
                    onSelect={onClickStatusHandler(item)}
                    className="cursor-pointer"
                  >
                    {item}
                  </CommandItem>
                )
              })}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
