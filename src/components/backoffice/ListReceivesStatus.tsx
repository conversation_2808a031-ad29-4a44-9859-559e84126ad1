import { TransferReceiveStatus } from '@/actions/transfer'
import { Badge } from '@/components/ui/badge'

interface Props {
  status?: TransferReceiveStatus
}

export default function ListReceivesStatus({ status }: Props) {
  if (!status) {
    return '-'
  }

  const label = status.split('/').pop()

  if (status === TransferReceiveStatus.PENDING) {
    return <Badge variant="green">{label}</Badge>
  }

  if (status === TransferReceiveStatus.COMPLETE) {
    return <Badge variant="blue">{label}</Badge>
  }

  if (status === TransferReceiveStatus.CANCELED) {
    return <Badge variant="purple">{label}</Badge>
  }

  return <Badge variant="default">{label}</Badge>
}
