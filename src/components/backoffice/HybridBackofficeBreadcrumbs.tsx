'use client'

import { usePathname } from 'next/navigation'
import { useSyncExternalStore, useCallback, useMemo } from 'react'
import { useBreadcrumbsOverrideStore } from '@/store/breadcrumbs'
import { combineBreadcrumbs, getBreadcrumbsFromPath } from '@/utils/breadcrumbs'
import BackofficeBreadcrumbs from './BackofficeBreadcrumbs'

interface BreadcrumbItem {
  label: string
  href?: string
}

interface HybridBackofficeBreadcrumbsProps {
  // Server-derived breadcrumbs (fallback)
  serverBreadcrumbs: BreadcrumbItem[]
}

/**
 * Hybrid component that:
 * 1. Uses server-derived breadcrumbs by default (SSR)
 * 2. Applies client overrides when they exist (CSR)
 * 3. No hydration issues using useSyncExternalStore
 */
export default function HybridBackofficeBreadcrumbs({
  serverBreadcrumbs,
}: HybridBackofficeBreadcrumbsProps) {
  const pathname = usePathname()

  // Use Zustand selector to get overrides for current path
  const overrides = useBreadcrumbsOverrideStore(state => state.overrides)
  const clientOverrides = overrides[pathname] || []

  // Always generate breadcrumbs from current pathname (client-side)
  // This ensures SPA navigation updates the breadcrumbs correctly
  const finalBreadcrumbs =
    clientOverrides.length > 0
      ? combineBreadcrumbs(pathname, clientOverrides)
      : getBreadcrumbsFromPath(pathname)

  return <BackofficeBreadcrumbs list={finalBreadcrumbs} />
}
