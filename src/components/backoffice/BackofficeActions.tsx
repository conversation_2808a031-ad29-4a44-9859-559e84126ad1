'use client'

import { useMemo } from 'react'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { ChevronDown } from 'lucide-react'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'

export default function BackofficeActions() {
  return (
    <div className="flex items-center gap-2">
      {/* Transfers Dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="flex items-center gap-2 hover:bg-transparent px-3 py-0 h-auto"
          >
            Transfers
            <ChevronDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-64">
          <DropdownMenuItem asChild className="cursor-pointer">
            <Link href="/backoffice/sends" className="flex items-start">
              Automated Transfers
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild className="cursor-pointer">
            <Link href="/backoffice/transfers" className="flex items-start">
              Manual Transfers
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild className="cursor-pointer">
            <Link href="/backoffice/receives" className="flex items-start">
              Receives
            </Link>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Purchases Direct Link */}
      {/* <Button
        variant="ghost"
        className="hover:bg-transparent px-3 py-0 h-auto"
        asChild
      >
        <Link href="/backoffice/purchases">Purchases</Link>
      </Button> */}

      {/* Users Direct Link */}
      <Button
        variant="ghost"
        className="hover:bg-transparent px-3 py-0 h-auto"
        asChild
      >
        <Link href="/backoffice/users">Users</Link>
      </Button>

      {/* Products Direct Link */}
      <Button
        variant="ghost"
        className="hover:bg-transparent px-3 py-0 h-auto"
        asChild
      >
        <Link href="/backoffice/products">Products</Link>
      </Button>

      {/* Logs & Reports Direct Link */}
      <Button
        variant="ghost"
        className="hover:bg-transparent px-3 py-0 h-auto"
        asChild
      >
        <Link href="/backoffice/logs">Logs & Reports</Link>
      </Button>

      {/* Emails Direct Link */}
      {/* <Button
        variant="ghost"
        className="hover:bg-transparent px-3 py-0 h-auto"
        asChild
      >
        <Link href="/backoffice/emails">Emails</Link>
      </Button> */}
    </div>
  )
}
