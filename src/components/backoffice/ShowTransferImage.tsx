'use client'
import Image from 'next/image'

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alog<PERSON><PERSON>le,
  DialogTrigger,
} from '@/components/ui/dialog'

export default function ShowTransferImage({ image }: any) {
  return image ? (
    <Dialog>
      <DialogTrigger>
        <Image
          className="cursor-pointer ring-1 ring-gray-100 hover:ring-gray-300 rounded-md"
          src={image}
          alt={image}
          width={256}
          height={256}
        />
      </DialogTrigger>

      <DialogContent className="overflow-hidden w-[64rem] max-w-[64rem] h-[36rem] max-h-[36rem]">
        <DialogTitle>Image</DialogTitle>
        <DialogHeader>
          <div className="text-xs text-ellipsis whitespace-nowrap overflow-hidden w-full">
            {image}
          </div>
        </DialogHeader>
        <Image
          className="w-full h-auto cursor-pointer"
          src={image}
          alt={image}
          width={1440}
          height={1440}
          onClick={() => window.open(image, '_blank')}
        />
      </DialogContent>
    </Dialog>
  ) : null
}
