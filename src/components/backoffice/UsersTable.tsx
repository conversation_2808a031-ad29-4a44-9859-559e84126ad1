'use client'

import { Ch<PERSON><PERSON>Up, ChevronDown, ExternalLink } from 'lucide-react'
import { format } from 'date-fns'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'

import { User } from '@/actions/user'
import { UserHat } from '@/actions/user'
import UpdateHats from '@/components/backoffice/UpdateHats'
import UpdateUserStatus from '@/components/backoffice/UpdateUserStatus'
import ToggleCreatorPermissions from '@/components/backoffice/ToggleCreatorPermissions'
import DeleteUserButton from '@/components/backoffice/DeleteUserButton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { HOST } from '@/config'
import { getIdFromRecord } from '@/utils'

interface UsersTableProps {
  users: User[]
  systemHats: UserHat[]
  currentSort: {
    sortBy: string
    sortOrder: 'asc' | 'desc'
  }
}

export default function UsersTable({
  users,
  systemHats,
  currentSort,
}: UsersTableProps) {
  const router = useRouter()
  const searchParams = useSearchParams()

  const handleSort = (column: string) => {
    const params = new URLSearchParams(searchParams.toString())

    // If clicking the same column, toggle the order
    if (currentSort.sortBy === column) {
      params.set('sortOrder', currentSort.sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      // If clicking a new column, set it as sortBy with desc order
      params.set('sortBy', column)
      params.set('sortOrder', 'desc')
    }

    // Reset to first page when sorting changes
    params.set('page', '1')

    router.push(`?${params.toString()}`)
  }

  const SortIcon = ({ column }: { column: string }) => {
    if (currentSort.sortBy !== column) return null
    return currentSort.sortOrder === 'asc' ? (
      <ChevronUp className="w-4 h-4 ml-1 inline" />
    ) : (
      <ChevronDown className="w-4 h-4 ml-1 inline" />
    )
  }

  const sortableColumns = [
    'id',
    'name',
    'handler',
    'email',
    'status',
    'created',
  ]

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead
            className={`cursor-pointer select-none ${
              sortableColumns.includes('id') ? 'hover:bg-muted/50' : ''
            }`}
            onClick={() => sortableColumns.includes('id') && handleSort('id')}
          >
            id
            {sortableColumns.includes('id') && <SortIcon column="id" />}
          </TableHead>
          <TableHead
            className={`cursor-pointer select-none ${
              sortableColumns.includes('name') ? 'hover:bg-muted/50' : ''
            }`}
            onClick={() =>
              sortableColumns.includes('name') && handleSort('name')
            }
          >
            name
            {sortableColumns.includes('name') && <SortIcon column="name" />}
          </TableHead>
          <TableHead>hats</TableHead>
          <TableHead
            className={`cursor-pointer select-none ${
              sortableColumns.includes('handler') ? 'hover:bg-muted/50' : ''
            }`}
            onClick={() =>
              sortableColumns.includes('handler') && handleSort('handler')
            }
          >
            handler
            {sortableColumns.includes('handler') && (
              <SortIcon column="handler" />
            )}
          </TableHead>
          <TableHead
            className={`cursor-pointer select-none ${
              sortableColumns.includes('email') ? 'hover:bg-muted/50' : ''
            }`}
            onClick={() =>
              sortableColumns.includes('email') && handleSort('email')
            }
          >
            email
            {sortableColumns.includes('email') && <SortIcon column="email" />}
          </TableHead>
          <TableHead>add content</TableHead>
          <TableHead
            className={`cursor-pointer select-none ${
              sortableColumns.includes('created') ? 'hover:bg-muted/50' : ''
            }`}
            onClick={() =>
              sortableColumns.includes('created') && handleSort('created')
            }
          >
            created
            {sortableColumns.includes('created') && (
              <SortIcon column="created" />
            )}
          </TableHead>
          <TableHead>actions</TableHead>
        </TableRow>
      </TableHeader>

      <TableBody>
        {users?.map(item => (
          <TableRow key={getIdFromRecord(item.id)}>
            <TableCell className="w-[12%] whitespace-nowrap">
              <Link href={`/backoffice/receives/${getIdFromRecord(item.id)}`}>
                {getIdFromRecord(item.id)}
              </Link>
            </TableCell>
            <TableCell className="w-[12%] whitespace-nowrap">
              {item?.profile?.name ?? '-'}
            </TableCell>

            <TableCell className="w-[12%] whitespace-nowrap">
              <UpdateHats
                userId={getIdFromRecord(item.id)}
                userHats={item?.hats ?? []}
                systemHats={systemHats ?? []}
              />
            </TableCell>

            <TableCell className="w-[1%] whitespace-nowrap">
              <Link
                href={`${HOST ?? window.location.origin}/user/${item?.handler}`}
                target="_blank"
                className="inline-flex gap-1 items-center border-b-[1px] border-transparent hover:border-primary"
              >
                {`@${item?.handler}`}
                <ExternalLink className="w-4 h-4" />
              </Link>
            </TableCell>

            <TableCell className="w-auto whitespace-nowrap">
              {item?.email}
            </TableCell>

            <TableCell className="w-[10%]">
              <ToggleCreatorPermissions
                userId={getIdFromRecord(item.id)}
                userHats={item?.hats ?? []}
                userProfile={item?.profile}
              />
            </TableCell>

            <TableCell className="w-[15%]">
              {item?.created ? format(item.created, 'dd/MM/yyyy - HH:mm') : '-'}
            </TableCell>

            <TableCell className="w-[1%]">
              <DeleteUserButton
                userId={getIdFromRecord(item.id)}
                userHandler={item?.handler || 'unknown'}
              />
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
