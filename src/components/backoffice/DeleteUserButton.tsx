'use client'

import { useState } from 'react'
import { Trash2 } from 'lucide-react'
import { useRouter } from 'next/navigation'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { useToast } from '@/hooks/use-toast'
import { deleteUser } from '@/actions/user/deleteUser'

interface DeleteUserButtonProps {
  userId: string
  userHandler: string
}

export default function DeleteUserButton({
  userId,
  userHandler,
}: DeleteUserButtonProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()
  const router = useRouter()

  const handleDeleteUser = async () => {
    try {
      setIsLoading(true)

      await deleteUser(userId)

      toast({
        title: 'User deleted',
        description: `User @${userHandler} has been successfully deleted.`,
        variant: 'default',
      })

      setIsDeleteDialogOpen(false)
      router.refresh() // Refresh the page to remove the user from the list
    } catch (error) {
      toast({
        title: 'Error deleting user',
        description:
          error instanceof Error
            ? error.message
            : 'An error occurred while trying to delete the user. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
      <button
        onClick={() => setIsDeleteDialogOpen(true)}
        className="p-1 hover:bg-red-100 hover:text-red-600 rounded-sm transition-colors"
        title={`Delete user @${userHandler}`}
      >
        <Trash2 className="w-4 h-4" />
      </button>

      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="tracking-wide">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete user{' '}
              <strong>@{userHandler}</strong>? This action cannot be undone and
              will permanently remove:
              <br />
              <br />
              • All user data
              <br />
              • Associated products and templates
              <br />
              • Transaction history
              <br />• Profile settings
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isLoading}
              className="h-10 px-4 py-2"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteUser}
              disabled={isLoading}
              className="h-10 px-4 py-2"
            >
              {isLoading ? 'Deleting...' : 'Delete user'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
