import ReactMarkdown from "react-markdown";
interface Props {
  content?: string;
}

export default function StyledMarkdown({ content }: Props) {
  return content ? (
    <ReactMarkdown
      components={{
        p: ({ node, ...props }) => <p className="text-sm pb-4" {...props} />,
        h1: ({ node, ...props }) => (
          <h1 className="text-2xl font-bold pb-4" {...props} />
        ),
        h2: ({ node, ...props }) => (
          <h2 className="text-1xl font-bold pb-2 pt-4" {...props} />
        ),
        ul: ({ node, ...props }) => <ul className="text-sm pb-8" {...props} />,
        li: ({ node, ...props }) => (
          <li className="list-disc list-inside" {...props} />
        ),
      }}
    >
      {content}
    </ReactMarkdown>
  ) : null;
}
