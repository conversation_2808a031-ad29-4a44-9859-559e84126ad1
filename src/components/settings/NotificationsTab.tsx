'use client'

import * as React from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'

interface NotificationsTabProps {
  className?: string
}

const NotificationsTab: React.FC<NotificationsTabProps> = ({ className }) => {
  return (
    <div className={cn('space-y-6', className)}>
      <Card>
        <CardContent className="p-0">
          <p className="text-muted-foreground text-center py-10 text-zinc-500 tracking-wide">
            Notification settings coming soon
          </p>
        </CardContent>
      </Card>
    </div>
  )
}

export default NotificationsTab
