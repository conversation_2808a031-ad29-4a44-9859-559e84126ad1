'use client'

import * as React from 'react'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs'
import useSessionUser from '@/hooks/useSessionUser'
import useUser from '@/hooks/useUser'

import AccountTab from './account-tab/AccountTab'
import NotificationsTab from './NotificationsTab'
import IntegrationsTab from './IntegrationsTab'
import SubscriptionsTab from './SubscriptionsTab'

const UserSettingsForm = () => {
  const { data: sessionUser } = useSessionUser()
  const userId = sessionUser?.id ? String(sessionUser.id) : ''
  const { data: user, isLoading } = useUser(userId)

  if (isLoading || !user) {
    return (
      <div className="w-full max-w-2xl mx-auto py-12 mt-4">
        <div className="h-5 w-2/3 bg-neutral-200 rounded-md animate-pulse mb-4 ml-3" />
        <div className="space-y-4">
          <div className="h-32 bg-neutral-200 rounded-md animate-pulse" />
          <div className="h-20 bg-neutral-200 rounded-md animate-pulse" />
          <div className="h-20 bg-neutral-200 rounded-md animate-pulse" />
        </div>
      </div>
    )
  }

  return (
    <div className="w-full max-w-2xl mx-auto py-12">
      <Tabs defaultValue="account" className="w-full">
        <TabsList className="flex w-full gap-4 bg-transparent border-none px-3">
          <TabsTrigger
            value="account"
            className="p-2 bg-transparent border-none text-sm font-medium text-zinc-500 hover:bg-transparent focus:bg-transparent focus-visible:bg-transparent data-[state=active]:text-foreground data-[state=active]:bg-transparent data-[state=active]:hover:bg-transparent data-[state=active]:shadow-none"
          >
            Account
          </TabsTrigger>
          <TabsTrigger
            value="notifications"
            className="p-2 bg-transparent border-none text-sm font-medium text-zinc-500 hover:bg-transparent focus:bg-transparent focus-visible:bg-transparent data-[state=active]:text-foreground data-[state=active]:bg-transparent data-[state=active]:hover:bg-transparent data-[state=active]:shadow-none"
          >
            Notifications
          </TabsTrigger>
          <TabsTrigger
            value="integrations"
            className="p-2 bg-transparent border-none text-sm font-medium text-zinc-500 hover:bg-transparent focus:bg-transparent focus-visible:bg-transparent data-[state=active]:text-foreground data-[state=active]:bg-transparent data-[state=active]:hover:bg-transparent data-[state=active]:shadow-none"
          >
            Integrations
          </TabsTrigger>
          <TabsTrigger
            value="subscriptions"
            className="p-2 bg-transparent border-none text-sm font-medium text-zinc-500 hover:bg-transparent focus:bg-transparent focus-visible:bg-transparent data-[state=active]:text-foreground data-[state=active]:bg-transparent data-[state=active]:hover:bg-transparent data-[state=active]:shadow-none"
          >
            Subscriptions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="account">
          <AccountTab />
        </TabsContent>

        <TabsContent value="notifications">
          <NotificationsTab />
        </TabsContent>

        <TabsContent value="integrations">
          <IntegrationsTab />
        </TabsContent>

        <TabsContent value="subscriptions">
          <SubscriptionsTab />
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default UserSettingsForm
