'use client'

import { useState, useMemo } from 'react'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import useSessionUser from '@/hooks/useSessionUser'
import useUser from '@/hooks/useUser'
import { useToast } from '@/hooks/use-toast'

const workTypes = [
  { id: 'template-customization', label: 'Template customization' },
  { id: 'project-from-scratch', label: 'Project from scratch' },
  { id: 'consulting', label: 'Consulting' },
  { id: 'training-workshops', label: 'Training & workshops' },
]

const hourlyRates = [
  { value: '50-100', label: '$50 - $100/hour' },
  { value: '100-200', label: '$100 - $200/hour' },
  { value: '200-300', label: '$200 - $300/hour' },
  { value: '300+', label: '$300+/hour' },
]

const AvailableWork = () => {
  const { data: sessionUser } = useSessionUser()
  const userId = sessionUser?.id ? String(sessionUser.id) : ''
  const { data: user, updateUserProfile } = useUser(userId)
  const { toast } = useToast()

  // Calculate initial state and validation based on user data
  const {
    initialIsAvailable,
    initialWorkTypes,
    initialHourlyRate,
    initialValidationErrors,
  } = useMemo(() => {
    const availableData = user?.profile?.available || {
      enabled: false,
      workTypes: [],
      hourlyRate: '',
    }

    const isAvailable = availableData.enabled
    const workTypes = availableData.workTypes || []
    const hourlyRate = availableData.hourlyRate || ''

    // Calculate validation errors if available is enabled
    const errors: string[] = []
    if (isAvailable) {
      if (workTypes.length === 0) {
        errors.push('Please select at least one type of work')
      }
      if (!hourlyRate) {
        errors.push('Please select an hourly rate')
      }
    }

    return {
      initialIsAvailable: isAvailable,
      initialWorkTypes: workTypes,
      initialHourlyRate: hourlyRate,
      initialValidationErrors: errors,
    }
  }, [user?.profile?.available])

  const [isAvailable, setIsAvailable] = useState(initialIsAvailable)
  const [selectedWorkTypes, setSelectedWorkTypes] =
    useState<string[]>(initialWorkTypes)
  const [hourlyRate, setHourlyRate] = useState(initialHourlyRate)
  const [validationErrors, setValidationErrors] = useState<string[]>(
    initialValidationErrors,
  )

  // Update state when initial values change (when user data loads)
  const currentIsAvailable = isAvailable || initialIsAvailable
  const currentWorkTypes =
    selectedWorkTypes.length > 0 ? selectedWorkTypes : initialWorkTypes
  const currentHourlyRate = hourlyRate || initialHourlyRate
  const currentValidationErrors =
    validationErrors.length > 0 ? validationErrors : initialValidationErrors

  const validateForm = () => {
    const errors: string[] = []

    if (currentIsAvailable) {
      if (currentWorkTypes.length === 0) {
        errors.push('Please select at least one type of work')
      }
      if (!currentHourlyRate) {
        errors.push('Please select an hourly rate')
      }
    }

    setValidationErrors(errors)
    return errors.length === 0
  }

  const saveAvailableData = async (data: any) => {
    try {
      await updateUserProfile('available', data)
      toast({
        title: 'Success!',
        description: 'Availability settings updated successfully',
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update availability settings',
        variant: 'destructive',
      })
      throw error
    }
  }

  const handleAvailableToggle = async (checked: boolean) => {
    setIsAvailable(checked)

    if (checked) {
      // When enabling, validate immediately to show required fields
      const errors: string[] = []
      if (currentWorkTypes.length === 0) {
        errors.push('Please select at least one type of work')
      }
      if (!currentHourlyRate) {
        errors.push('Please select an hourly rate')
      }
      setValidationErrors(errors)
    } else {
      // When disabling, clear all data and validation errors
      setSelectedWorkTypes([])
      setHourlyRate('')
      setValidationErrors([])

      const availableData = {
        enabled: false,
        workTypes: [],
        hourlyRate: '',
      }

      await saveAvailableData(availableData)
      return
    }

    const availableData = {
      enabled: checked,
      workTypes: currentWorkTypes,
      hourlyRate: currentHourlyRate,
    }

    await saveAvailableData(availableData)
  }

  const handleWorkTypeChange = async (workTypeId: string, checked: boolean) => {
    const newSelectedWorkTypes = checked
      ? [...currentWorkTypes, workTypeId]
      : currentWorkTypes.filter(id => id !== workTypeId)

    setSelectedWorkTypes(newSelectedWorkTypes)

    // Validate form after change with updated work types
    if (currentIsAvailable) {
      const errors: string[] = []
      if (newSelectedWorkTypes.length === 0) {
        errors.push('Please select at least one type of work')
      }
      if (!currentHourlyRate) {
        errors.push('Please select an hourly rate')
      }
      setValidationErrors(errors)
    }

    const availableData = {
      enabled: currentIsAvailable,
      workTypes: newSelectedWorkTypes,
      hourlyRate: currentHourlyRate,
    }

    await saveAvailableData(availableData)
  }

  const handleHourlyRateChange = async (value: string) => {
    setHourlyRate(value)

    // Validate form after change with updated value
    if (currentIsAvailable) {
      const errors: string[] = []
      if (currentWorkTypes.length === 0) {
        errors.push('Please select at least one type of work')
      }
      if (!value) {
        errors.push('Please select an hourly rate')
      }
      setValidationErrors(errors)
    }

    const availableData = {
      enabled: currentIsAvailable,
      workTypes: currentWorkTypes,
      hourlyRate: value,
    }

    await saveAvailableData(availableData)
  }

  return (
    <>
      <div className="p-6 tracking-wide">
        <div>
          <div className="flex items-center space-x-2 mb-2">
            <h2 className="text-lg font-semibold">Available for Work</h2>
            <Switch
              checked={currentIsAvailable}
              onCheckedChange={handleAvailableToggle}
              className="scale-90"
            />
          </div>
          <p className="text-sm text-zinc-500 tracking-wide m-0 p-0">
            When enabled, other users can contact you for freelance projects and
            work opportunities
          </p>
        </div>

        {currentIsAvailable && (
          <div className="space-y-5 pt-6">
            <Separator className="my-0" />

            {/* Validation Errors */}
            {currentValidationErrors.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <div className="text-sm text-red-800">
                  <ul className="list-disc list-inside space-y-1">
                    {currentValidationErrors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </div>
              </div>
            )}

            <div className="space-y-4">
              <Label className="text-sm font-medium">Types of work</Label>
              <div className="grid grid-cols-1 gap-3">
                {workTypes.map(workType => (
                  <div
                    key={workType.id}
                    className="flex items-center space-x-2"
                  >
                    <Checkbox
                      id={workType.id}
                      checked={currentWorkTypes.includes(workType.id)}
                      onCheckedChange={checked =>
                        handleWorkTypeChange(workType.id, checked as boolean)
                      }
                    />
                    <Label
                      htmlFor={workType.id}
                      className="text-sm font-normal cursor-pointer"
                    >
                      {workType.label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="hourly-rate" className="text-sm font-medium">
                Hourly rate
              </Label>
              <Select
                value={currentHourlyRate}
                onValueChange={handleHourlyRateChange}
              >
                <SelectTrigger className="w-full rounded-md border px-3 py-2 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-none focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm shadow-sm text-foreground h-12 bg-gray-50 border-gray-200 text-base focus-visible:ring-2 focus-visible:ring-black [&>span]:text-gray-600">
                  <SelectValue
                    placeholder="Select your hourly rate"
                    className="text-zinc-500"
                  />
                </SelectTrigger>
                <SelectContent>
                  {hourlyRates.map(rate => (
                    <SelectItem key={rate.value} value={rate.value}>
                      {rate.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        )}
      </div>

      <Separator className="my-0" />
    </>
  )
}

export default AvailableWork
