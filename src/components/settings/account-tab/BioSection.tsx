'use client'

import * as React from 'react'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { UseFormReturn } from 'react-hook-form'
import { Separator } from '@/components/ui/separator'

interface BioSectionProps {
  form: UseFormReturn<any>
  onFieldChange?: (
    fieldName: 'handler' | 'fullName' | 'headline' | 'bio',
  ) => (value: string) => void
  successFields?: Set<string>
}

const BioSection: React.FC<BioSectionProps> = ({
  form,
  onFieldChange,
  successFields = new Set(),
}) => {
  // Function to calculate character counter color
  const getCharacterCountColor = (current: number, max: number) => {
    const percentage = (current / max) * 100
    if (percentage >= 100) return 'text-red-600 font-medium'
    if (percentage >= 90) return 'text-red-500'
    if (percentage >= 75) return 'text-yellow-600'
    return 'text-zinc-500'
  }

  return (
    <>
      <div className="space-y-5 p-6 tracking-wide">
        <h2 className="text-lg font-semibold">About</h2>
        <FormField
          control={form.control}
          name="headline"
          render={({ field }) => (
            <FormItem>
              <div className="flex justify-between items-center mb-3">
                <FormLabel>Headline</FormLabel>
                <div className="flex justify-between items-center">
                  <span
                    className={`text-xs tracking-wide ${getCharacterCountColor(field.value?.length || 0, 80)}`}
                  >
                    {field.value?.length || 0}/80 characters remaining
                  </span>
                </div>
              </div>
              <FormControl>
                <Input
                  placeholder="Software Engineer"
                  className={`w-full rounded-md border px-3 py-2 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-none focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm shadow-sm text-foreground h-12 text-base placeholder:text-gray-400 ${
                    successFields.has('headline')
                      ? 'bg-green-50 border-gray-200 focus-visible:ring-2 focus-visible:ring-black'
                      : 'bg-gray-50 border-gray-200 focus-visible:ring-2 focus-visible:ring-black'
                  }`}
                  maxLength={80}
                  {...field}
                  onChange={e => {
                    field.onChange(e)
                    onFieldChange?.('headline')(e.target.value)
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="bio"
          render={({ field }) => (
            <FormItem>
              <div className="flex justify-between items-center mb-3">
                <FormLabel>Bio</FormLabel>
                <div className="flex justify-end">
                  <span
                    className={`text-xs tracking-wide ${getCharacterCountColor(field.value?.length || 0, 240)}`}
                  >
                    {field.value?.length || 0}/240 characters remaining
                  </span>
                </div>
              </div>
              <FormControl>
                <Textarea
                  placeholder="Write a short bio about yourself"
                  className={`w-full min-h-[120px] rounded-md border px-3 py-2 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-none focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm shadow-sm text-foreground h-12 text-base placeholder:text-gray-400 ${
                    successFields.has('bio')
                      ? 'bg-green-50 border-gray-200 focus-visible:ring-2 focus-visible:ring-black'
                      : 'bg-gray-50 border-gray-200 focus-visible:ring-2 focus-visible:ring-black'
                  }`}
                  maxLength={240}
                  {...field}
                  onChange={e => {
                    field.onChange(e)
                    onFieldChange?.('bio')(e.target.value)
                  }}
                />
              </FormControl>

              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      <Separator className="my-0" />
    </>
  )
}

export default BioSection
