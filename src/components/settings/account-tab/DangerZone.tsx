'use client'

import * as React from 'react'
import { Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { useRouter } from 'next/navigation'
import { signOut } from 'next-auth/react'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { deleteUser } from '@/actions/user'

interface DangerZoneProps {
  className?: string
  userId: string
}

const DangerZone: React.FC<DangerZoneProps> = ({ className, userId }) => {
  const { toast } = useToast()
  const router = useRouter()
  const [isLoading, setIsLoading] = React.useState<boolean>(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] =
    React.useState<boolean>(false)

  const handleDeleteAccount = async () => {
    if (!userId) return

    setIsLoading(true)
    try {
      await deleteUser(userId)
      toast({
        title: 'Success!',
        description: 'Account deleted successfully!',
      })
      setIsDeleteDialogOpen(false)
      signOut({ redirect: false })
      router.push('/')
    } catch (error) {
      console.error('Error deleting account:', error)
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to delete account. Please try again.'
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
      <div className={cn('p-6', className)}>
        <h2 className="text-lg font-semibold mb-5 text-red-500">Danger Zone</h2>

        <Button
          variant="destructive"
          size="lg"
          disabled={isLoading}
          onClick={() => setIsDeleteDialogOpen(true)}
          className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 bg-red-500 text-red-500-foreground hover:bg-red-500/90 text-white h-10 px-4 py-2"
        >
          {isLoading ? <>Deleting account...</> : <>Delete account</>}
        </Button>
      </div>
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="tracking-wide">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete your account? This action cannot
              be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isLoading}
              className="h-10 px-4 py-2"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteAccount}
              disabled={isLoading}
              className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 bg-red-500 text-red-500-foreground hover:bg-red-500/90 text-white h-10 px-4 py-2"
            >
              {isLoading ? <>Deleting account...</> : <>Delete account</>}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default DangerZone
