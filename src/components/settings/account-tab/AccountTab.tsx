'use client'

import * as React from 'react'
import { useToast } from '@/hooks/use-toast'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'

import { cn } from '@/lib/utils'
import { Card, CardContent } from '@/components/ui/card'
import { Form } from '@/components/ui/form'

import useSessionUser from '@/hooks/useSessionUser'
import useUser from '@/hooks/useUser'
import { checkIfHandleExists } from '@/data/user/check-if-handle-exists'
import DangerZone from './DangerZone'
import AvatarSection from './AvatarSection'
import BasicInfoSection from './BasicInfoSection'
import BioSection from './BioSection'
import SocialLinksSection from './social-links/SocialLinksSection'
import SkillsInterests from './SkillsInterests'
import AvailableWork from './AvailableWork'

// Validation schema with Zod
const accountFormSchema = z.object({
  handler: z
    .string()
    .min(1, 'Handle is required')
    .min(3, 'Handle must be at least 3 characters')
    .max(30, 'Handle must be at most 30 characters')
    .regex(
      /^[a-zA-Z0-9_-]+$/,
      'Handle can only contain letters, numbers, hyphens and underscores',
    ),
  fullName: z
    .string()
    .min(1, 'Full name is required')
    .max(100, 'Full name must be at most 100 characters'),
  headline: z
    .string()
    .max(80, 'Headline must be at most 80 characters')
    .optional(),
  bio: z.string().max(240, 'Bio must be at most 240 characters').optional(),
})

type AccountFormValues = z.infer<typeof accountFormSchema>

interface AccountTabProps {
  className?: string
}

const AccountTab: React.FC<AccountTabProps> = ({ className }) => {
  const { data: sessionUser } = useSessionUser()
  const userId = sessionUser?.id ? String(sessionUser.id) : ''
  const { data: user, updateUserProfile, updateUserHandler } = useUser(userId)
  const { toast } = useToast()

  // Debounce timers for auto-save
  const debounceTimers = React.useRef<{
    handler: NodeJS.Timeout | null
    fullName: NodeJS.Timeout | null
    headline: NodeJS.Timeout | null
    bio: NodeJS.Timeout | null
  }>({
    handler: null,
    fullName: null,
    headline: null,
    bio: null,
  })

  // Success feedback timers for green border
  const successTimers = React.useRef<{
    handler: NodeJS.Timeout | null
    fullName: NodeJS.Timeout | null
    headline: NodeJS.Timeout | null
    bio: NodeJS.Timeout | null
  }>({
    handler: null,
    fullName: null,
    headline: null,
    bio: null,
  })

  // Success state for green border feedback
  const [successFields, setSuccessFields] = React.useState<Set<string>>(
    new Set(),
  )

  // Function to split full name into first name and last name
  const splitFullName = (fullName: string) => {
    const parts = fullName.trim().split(' ')
    const firstName = parts[0] || ''
    const lastName = parts.slice(1).join(' ') || ''
    return { firstName, lastName }
  }

  // Function to combine first name and last name into full name
  const combineFullName = (firstName?: string, lastName?: string) => {
    return [firstName, lastName].filter(Boolean).join(' ')
  }

  // Function to show success feedback with green border
  const showSuccessFeedback = (fieldName: string) => {
    // Clear existing timer for this field
    if (
      successTimers.current[fieldName as keyof typeof successTimers.current]
    ) {
      clearTimeout(
        successTimers.current[fieldName as keyof typeof successTimers.current]!,
      )
    }

    // Add field to success state
    setSuccessFields(prev => new Set(prev).add(fieldName))

    // Set timer to remove success state after 2 seconds
    successTimers.current[fieldName as keyof typeof successTimers.current] =
      setTimeout(() => {
        setSuccessFields(prev => {
          const newSet = new Set(prev)
          newSet.delete(fieldName)
          return newSet
        })
        successTimers.current[fieldName as keyof typeof successTimers.current] =
          null
      }, 2000)
  }

  // React Hook Form configuration
  const form = useForm<AccountFormValues>({
    resolver: zodResolver(accountFormSchema),
    defaultValues: {
      handler: user?.handler || '',
      fullName: combineFullName(
        user?.profile?.firstName,
        user?.profile?.lastName,
      ),
      headline: user?.profile?.headline || '',
      bio: user?.profile?.bio || '',
    },
    mode: 'onChange',
  })

  // Auto-save handlers for each field
  const handleFieldChange = React.useCallback(
    (fieldName: 'handler' | 'fullName' | 'headline' | 'bio') => {
      return async (value: string) => {
        if (!userId || !user) return

        // Clear existing timer for this field
        if (debounceTimers.current[fieldName]) {
          clearTimeout(debounceTimers.current[fieldName]!)
          debounceTimers.current[fieldName] = null
        }

        // Set new timer
        debounceTimers.current[fieldName] = setTimeout(async () => {
          // Validate the field before saving
          try {
            const currentValues = form.getValues()
            const fieldToValidate = { ...currentValues, [fieldName]: value }

            // Validate the specific field using Zod
            const validationResult =
              accountFormSchema.safeParse(fieldToValidate)

            if (!validationResult.success) {
              // Check if the error is related to the field we're trying to save
              const fieldErrors = validationResult.error.errors.filter(error =>
                error.path.includes(fieldName),
              )

              if (fieldErrors.length > 0) {
                // Don't save if there are validation errors for this field
                console.log(`Validation failed for ${fieldName}:`, fieldErrors)
                return
              }
            }

            switch (fieldName) {
              case 'handler':
                // Check if handle already exists
                if (value !== user.handler) {
                  const existingUser = await checkIfHandleExists({
                    handler: value,
                  })
                  if (existingUser && existingUser.id !== userId) {
                    toast({
                      title: 'Error',
                      description:
                        'This handle is already taken. Please choose another one.',
                      variant: 'destructive',
                    })
                    return
                  }
                  await updateUserHandler(value)
                }
                break

              case 'fullName':
                if (
                  value !==
                  combineFullName(
                    user.profile?.firstName,
                    user.profile?.lastName,
                  )
                ) {
                  const { firstName, lastName } = splitFullName(value)

                  // Update firstName if it changed
                  if (firstName !== user.profile?.firstName) {
                    await updateUserProfile('firstName', firstName)
                  }

                  // Update lastName if it changed
                  if (lastName !== user.profile?.lastName) {
                    await updateUserProfile('lastName', lastName)
                  }

                  // Update name field with the full name
                  await updateUserProfile('name', value)
                }
                break

              case 'headline':
                if (value !== user.profile?.headline) {
                  await updateUserProfile('headline', value || '')
                }
                break

              case 'bio':
                if (value !== user.profile?.bio) {
                  await updateUserProfile('bio', value || '')
                }
                break
            }

            // Show success feedback
            showSuccessFeedback(fieldName)

            // Show success toast
            const fieldDisplayName = {
              handler: 'Handle',
              fullName: 'Name',
              headline: 'Headline',
              bio: 'Bio',
            }[fieldName]

            toast({
              title: 'Saved!',
              description: `${fieldDisplayName} updated successfully.`,
            })
          } catch (error) {
            console.error(`Error auto-saving ${fieldName}:`, error)
            toast({
              title: 'Error',
              description: `Failed to save ${fieldName}. Please try again.`,
              variant: 'destructive',
            })
          }
        }, 1000)
      }
    },
    [userId, user, updateUserProfile, updateUserHandler, toast, form],
  )

  if (!user) {
    return null
  }

  return (
    <div className={cn('space-y-6', className)}>
      <Card>
        <CardContent className="pb-0">
          <AvatarSection userId={userId} />

          <Form {...form}>
            <BasicInfoSection
              form={form}
              userId={userId}
              onFieldChange={handleFieldChange}
              successFields={successFields}
            />

            <BioSection
              form={form}
              onFieldChange={handleFieldChange}
              successFields={successFields}
            />

            <SocialLinksSection userId={userId} />

            <SkillsInterests
              userId={userId}
              user={user}
              onUserUpdate={updatedUser => {
                console.log('User updated:', updatedUser)
              }}
            />

            <AvailableWork />
          </Form>
          <DangerZone userId={userId} />
        </CardContent>
      </Card>
    </div>
  )
}

export default AccountTab
