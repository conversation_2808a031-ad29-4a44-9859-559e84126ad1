import { cleanUrl } from '@/utils/cleanUrl'
import { z } from 'zod'
import {
  FaXTwitter,
  FaLinkedin,
  FaInstagram,
  FaFacebook,
  FaYoutube,
  FaGithub,
  FaTwitch,
  FaDribbble,
  FaBehance,
  FaGlobe,
} from 'react-icons/fa6'

const createUrlValidator = (pattern: RegExp, errorMessage: string) => {
  return z
    .string()
    .min(1, 'Field is required')
    .refine(value => {
      const cleanedValue = cleanUrl(value)
      return pattern.test(cleanedValue)
    }, errorMessage)
}

const createUrlMask = () => {
  return (value: string) => cleanUrl(value)
}

const createUsernameValidator = (pattern: RegExp, errorMessage: string) => {
  return z.string().min(1, 'Username is required').regex(pattern, errorMessage)
}

const createUsernameMask = () => {
  return (value: string) => {
    if (!value) return value
    return value.startsWith('@') ? value : `@${value}`
  }
}

const createUsernameDisplay = () => {
  return (value: string) => (value.startsWith('@') ? value : `@${value}`)
}

const createSimpleUsernameValidator = (
  pattern: RegExp,
  errorMessage: string,
) => {
  return z.string().min(1, 'Username is required').regex(pattern, errorMessage)
}

const createSimpleUsernameMask = () => {
  return (value: string) => value
}

const createSimpleUsernameDisplay = () => {
  return (value: string) => value
}

const createWebsiteMask = () => {
  return (value: string) => {
    if (!value) return value
    let cleanedValue = value
    if (!value.startsWith('http://') && !value.startsWith('https://')) {
      cleanedValue = `https://${value}`
    }
    // Remove trailing slash if present
    cleanedValue = cleanedValue.replace(/\/$/, '')
    return cleanedValue
  }
}

// Social links configuration with validation schemas and masks
export const SOCIAL_LINKS_CONFIG = [
  {
    id: 'website',
    name: 'Site/Portfolio',
    icon: FaGlobe,
    color: 'bg-gray-100',
    iconColor: 'text-gray-900',
    placeholder: 'https://example.com',
    validation: z.string().url('Please enter a valid URL'),
    mask: createWebsiteMask(),
  },
  {
    id: 'twitter',
    name: 'X (Twitter)',
    icon: FaXTwitter,
    color: 'bg-gray-100',
    iconColor: 'text-gray-900',
    placeholder: '@username',
    validation: createUsernameValidator(
      /^@?[a-zA-Z0-9_]{1,15}$/,
      'Invalid Twitter username',
    ),
    mask: createUsernameMask(),
    formatForDisplay: createUsernameDisplay(),
  },
  {
    id: 'linkedin',
    name: 'LinkedIn',
    icon: FaLinkedin,
    color: 'bg-gray-100',
    iconColor: 'text-gray-900',
    placeholder: 'linkedin.com/in/username',
    validation: createUrlValidator(
      /^linkedin\.com\/in\/[a-zA-Z0-9-]+$/,
      'Invalid LinkedIn profile URL',
    ),
    mask: createUrlMask(),
    formatForDisplay: (value: string) => value,
  },
  {
    id: 'instagram',
    name: 'Instagram',
    icon: FaInstagram,
    color: 'bg-gray-100',
    iconColor: 'text-gray-900',
    placeholder: '@username',
    validation: createUsernameValidator(
      /^@?[a-zA-Z0-9._]{1,30}$/,
      'Invalid Instagram username',
    ),
    mask: createUsernameMask(),
    formatForDisplay: createUsernameDisplay(),
  },
  // {
  //   id: 'facebook',
  //   name: 'Facebook',
  //   icon: FaFacebook,
  //   color: 'bg-gray-100',
  //   iconColor: 'text-gray-900',
  //   placeholder: 'facebook.com/username',
  //   validation: createUrlValidator(
  //     /^facebook\.com\/[a-zA-Z0-9._]+$/,
  //     'Invalid Facebook profile URL',
  //   ),
  //   mask: createUrlMask(),
  //   formatForDisplay: (value: string) => value,
  // },
  // {
  //   id: 'youtube',
  //   name: 'YouTube',
  //   icon: FaYoutube,
  //   color: 'bg-gray-100',
  //   iconColor: 'text-gray-900',
  //   placeholder: 'youtube.com/@username',
  //   validation: createUrlValidator(
  //     /^youtube\.com\/@[a-zA-Z0-9._-]+$/,
  //     'Invalid YouTube channel URL',
  //   ),
  //   mask: createUrlMask(),
  //   formatForDisplay: (value: string) => value,
  // },
  // {
  //   id: 'github',
  //   name: 'GitHub',
  //   icon: FaGithub,
  //   color: 'bg-gray-100',
  //   iconColor: 'text-gray-900',
  //   placeholder: 'username',
  //   validation: createSimpleUsernameValidator(
  //     /^[a-zA-Z0-9-]+$/,
  //     'Invalid GitHub username',
  //   ),
  //   mask: createSimpleUsernameMask(),
  //   formatForDisplay: createSimpleUsernameDisplay(),
  // },
  // {
  //   id: 'twitch',
  //   name: 'Twitch',
  //   icon: FaTwitch,
  //   color: 'bg-gray-100',
  //   iconColor: 'text-gray-900',
  //   placeholder: 'username',
  //   validation: createSimpleUsernameValidator(
  //     /^[a-zA-Z0-9_]{4,25}$/,
  //     'Invalid Twitch username',
  //   ),
  //   mask: createSimpleUsernameMask(),
  //   formatForDisplay: createSimpleUsernameDisplay(),
  // },
  // {
  //   id: 'dribbble',
  //   name: 'Dribbble',
  //   icon: FaDribbble,
  //   color: 'bg-gray-100',
  //   iconColor: 'text-gray-900',
  //   placeholder: 'username',
  //   validation: createSimpleUsernameValidator(
  //     /^[a-zA-Z0-9_-]+$/,
  //     'Invalid Dribbble username',
  //   ),
  //   mask: createSimpleUsernameMask(),
  //   formatForDisplay: createSimpleUsernameDisplay(),
  // },
  // {
  //   id: 'behance',
  //   name: 'Behance',
  //   icon: FaBehance,
  //   color: 'bg-gray-100',
  //   iconColor: 'text-gray-900',
  //   placeholder: 'username',
  //   validation: createSimpleUsernameValidator(
  //     /^[a-zA-Z0-9_-]+$/,
  //     'Invalid Behance username',
  //   ),
  //   mask: createSimpleUsernameMask(),
  //   formatForDisplay: createSimpleUsernameDisplay(),
  // },
]
