'use client'

import * as React from 'react'
import { useState } from 'react'
import { Separator } from '@/components/ui/separator'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Switch } from '@/components/ui/switch'
import { useToast } from '@/hooks/use-toast'
import { X } from 'lucide-react'
import useUser from '@/hooks/useUser'
import useSessionUser from '@/hooks/useSessionUser'
import { z } from 'zod'
import { SOCIAL_LINKS_CONFIG } from './SocialLinksConfig'

interface SocialLinksSectionProps {
  userId?: string
}

const SocialLinksSection: React.FC<SocialLinksSectionProps> = ({ userId }) => {
  const { data: sessionUser } = useSessionUser()
  const currentUserId =
    userId || (sessionUser?.id ? String(sessionUser.id) : '')
  const { data: user, updateUserProfile, refetch } = useUser(currentUserId)
  const { toast } = useToast()

  const [connectingSocial, setConnectingSocial] = useState<string | null>(null)
  const [socialInputValue, setSocialInputValue] = useState('')
  const [socialInputError, setSocialInputError] = useState('')
  const [isConnecting, setIsConnecting] = useState(false)

  // Get current social links from user profile
  const currentSocialLinks = (user?.profile?.social || {}) as Record<
    string,
    { value: string; active: boolean }
  >

  const handleConnectClick = (socialId: string) => {
    setConnectingSocial(socialId)
    setSocialInputValue(currentSocialLinks[socialId]?.value || '')
    setSocialInputError('')
  }

  const handleCancelConnect = () => {
    setConnectingSocial(null)
    setSocialInputValue('')
    setSocialInputError('')
  }

  const handleDisconnect = async (socialId: string) => {
    if (!currentUserId) return

    setIsConnecting(true)
    try {
      const updatedSocial = { ...currentSocialLinks }
      delete updatedSocial[socialId]

      await updateUserProfile('social', updatedSocial)

      toast({
        title: 'Success!',
        description: 'Social link removed successfully!',
      })

      refetch()
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to remove social link. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsConnecting(false)
    }
  }

  const handleSaveSocial = async () => {
    if (!connectingSocial || !currentUserId) return

    const socialConfig = SOCIAL_LINKS_CONFIG.find(
      config => config.id === connectingSocial,
    )
    if (!socialConfig) return

    // Validate input
    try {
      const validatedValue = socialConfig.validation.parse(socialInputValue)
      const maskedValue = socialConfig.mask(validatedValue)

      setIsConnecting(true)

      const updatedSocial = {
        ...currentSocialLinks,
        [connectingSocial]: { value: maskedValue, active: true },
      }

      await updateUserProfile('social', updatedSocial)

      toast({
        title: 'Success!',
        description: 'Social link connected successfully!',
      })

      setConnectingSocial(null)
      setSocialInputValue('')
      refetch()
    } catch (error) {
      if (error instanceof z.ZodError) {
        setSocialInputError(error.errors[0].message)
      } else {
        toast({
          title: 'Error',
          description: 'Failed to connect social link. Please try again.',
          variant: 'destructive',
        })
      }
    } finally {
      setIsConnecting(false)
    }
  }

  const handleToggleActive = async (socialId: string, newActive: boolean) => {
    if (!currentUserId) return

    try {
      const currentSocial = currentSocialLinks[socialId]
      if (!currentSocial) return

      const updatedSocial = {
        ...currentSocialLinks,
        [socialId]: { ...currentSocial, active: newActive },
      }

      await updateUserProfile('social', updatedSocial)

      toast({
        title: 'Success!',
        description: `Social link ${newActive ? 'activated' : 'deactivated'} successfully!`,
      })

      refetch()
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update social link status. Please try again.',
        variant: 'destructive',
      })
    }
  }

  const handleInputChange = (value: string) => {
    setSocialInputValue(value)
    setSocialInputError('')
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSaveSocial()
    } else if (e.key === 'Escape') {
      handleCancelConnect()
    }
  }

  const isConnected = (socialId: string) => {
    return !!currentSocialLinks[socialId]?.value
  }

  const getDisplayValue = (socialId: string) => {
    const socialData = currentSocialLinks[socialId]
    if (!socialData?.value) return ''

    const socialConfig = SOCIAL_LINKS_CONFIG.find(
      config => config.id === socialId,
    )
    return socialConfig?.formatForDisplay
      ? socialConfig.formatForDisplay(socialData.value)
      : socialData.value
  }

  return (
    <>
      <div className="space-y-5 p-6">
        <div>
          <h2 className="text-lg font-semibold mb-2">Social links</h2>
          <p className="text-sm text-muted-foreground mb-6 text-zinc-500 tracking-wide">
            Add your social accounts to display them on your public profile and
            your community profile. Once you&apos;ve added an account, you can
            delete it or stop displaying it at any time.
          </p>
        </div>
        <div className="space-y-3">
          {SOCIAL_LINKS_CONFIG.map(socialLink => {
            const IconComponent = socialLink.icon
            const connected = isConnected(socialLink.id)
            const displayValue = getDisplayValue(socialLink.id)
            const isConnectingThis = connectingSocial === socialLink.id
            const isActive = currentSocialLinks[socialLink.id]?.active ?? false

            return (
              <div
                key={socialLink.id}
                className="flex items-center justify-between p-4 rounded-lg transition-colors px-0 bg-transparent py-0"
              >
                <div className="flex items-center gap-3">
                  <div
                    className={`w-8 h-8 ${socialLink.color} rounded flex items-center justify-center`}
                  >
                    <IconComponent
                      className={`w-4 h-4 ${socialLink.iconColor}`}
                    />
                  </div>
                  <div className="flex-1">
                    <span className="font-medium text-sm">
                      {socialLink.name}
                    </span>
                    {connected && (
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs text-zinc-500 flex items-center gap-1 tracking-wide">
                          Connected: {displayValue}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  {isConnectingThis ? (
                    <div className="flex items-center gap-2">
                      <div className="w-48">
                        <Input
                          placeholder={socialLink.placeholder}
                          value={socialInputValue}
                          onChange={e => handleInputChange(e.target.value)}
                          onKeyDown={handleKeyPress}
                          className={`w-full rounded-md border px-3 py-2 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-none focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm shadow-sm text-foreground h-[2.2rem] bg-gray-50 border-gray-200 text-base placeholder:text-gray-400 focus-visible:ring-2 focus-visible:ring-black ${socialInputError ? 'border-red-500' : ''}`}
                          size={1}
                        />
                        {socialInputError && (
                          <p className="text-xs text-red-500 mt-1 absolute">
                            {socialInputError}
                          </p>
                        )}
                      </div>
                      <Button
                        size="sm"
                        onClick={handleSaveSocial}
                        disabled={isConnecting || !socialInputValue.trim()}
                        className="text-xs h-[2.1rem]"
                      >
                        {isConnecting ? 'Saving...' : 'Save'}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleCancelConnect}
                        disabled={isConnecting}
                        className="text-xs"
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </div>
                  ) : connected ? (
                    <div className="flex items-center gap-3">
                      <Switch
                        checked={isActive}
                        onCheckedChange={checked =>
                          handleToggleActive(socialLink.id, checked)
                        }
                        className="scale-90"
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDisconnect(socialLink.id)}
                        disabled={isConnecting}
                        className="text-xs"
                      >
                        Disconnect
                      </Button>
                    </div>
                  ) : (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleConnectClick(socialLink.id)}
                      className="text-xs"
                    >
                      Connect
                    </Button>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </div>
      <Separator className="my-0" />
    </>
  )
}

export default SocialLinksSection
