'use client'

import * as React from 'react'
import { Separator } from '@/components/ui/separator'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useToast } from '@/hooks/use-toast'

import { useSkills, useUserSkills, useUserSkillsData } from '@/hooks/use-skills'
import { SimpleSkillsCombobox } from '@/components/ui/simple-skills-combobox'
import { type User } from '@/actions/user'

interface SkillsInterestsProps {
  userId?: string
  user?: User
  onUserUpdate?: (user: User) => void
}

const MAX_SKILLS = 6

const SkillsInterests: React.FC<SkillsInterestsProps> = ({
  userId,
  user,
  onUserUpdate,
}) => {
  const { toast } = useToast()

  // Get all skills using React Query
  const {
    data: skills = [],
    isLoading: skillsLoading,
    error: skillsError,
  } = useSkills(undefined, true)

  // Get user skills data using the new hook
  const { selectedSkills = [], selectedSkillsData = [] } =
    useUserSkillsData(user)

  // Get user skills mutation
  const { updateSkills, updateSkillsMutation } = useUserSkills(userId || '')

  // Handle skills change
  const handleSkillsChange = async (newSelectedSkills: string[]) => {
    if (!userId || userId === '' || userId === 'no-user-id') {
      return
    }

    // Check if trying to add more than the maximum allowed skills
    if (newSelectedSkills.length > MAX_SKILLS) {
      toast({
        title: 'Limit Reached',
        description: `You can select a maximum of ${MAX_SKILLS} skills.`,
        variant: 'destructive',
      })
      return
    }

    try {
      const updatedUser = await updateSkills(newSelectedSkills)

      if (updatedUser && onUserUpdate) {
        onUserUpdate(updatedUser)
      }

      toast({
        title: 'Success',
        description: 'Skills updated successfully',
      })
    } catch (error) {
      console.error('Error updating skills:', error)
      toast({
        title: 'Error',
        description: 'Failed to update skills',
        variant: 'destructive',
      })
    }
  }

  // Show error if skills failed to load
  if (skillsError) {
    return (
      <div className="space-y-5 p-6 tracking-wide">
        <h2 className="text-lg font-semibold">Skills & Interests</h2>
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-destructive">
              Failed to load skills. Please try again later.
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  const isLoading = skillsLoading || updateSkillsMutation.isPending
  const isMaxSkillsReached = selectedSkills.length >= MAX_SKILLS

  return (
    <>
      <div className="p-6 tracking-wide">
        <h2 className="text-lg font-semibold mb-3">Skills & Interests</h2>
        <Card className="border-none">
          <CardContent className="pb-0">
            {skillsLoading ? (
              <div className="flex justify-center py-8">
                <p className="text-muted-foreground">Loading skills...</p>
              </div>
            ) : (
              <SimpleSkillsCombobox
                skills={skills}
                selectedSkills={selectedSkills}
                onSkillsChange={handleSkillsChange}
                placeholder={
                  isMaxSkillsReached
                    ? `Maximum ${MAX_SKILLS} skills reached`
                    : 'Search and add skills...'
                }
                maxSkills={MAX_SKILLS}
                searchPlaceholder="Search skills..."
                emptyText="No skills found."
                disabled={isLoading || isMaxSkillsReached}
                className="w-full"
                classNames={{
                  input:
                    'w-full rounded-md border px-3 py-2 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-none focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm shadow-sm text-foreground h-12 bg-gray-50 border-gray-200 text-base placeholder:text-gray-400 focus-visible:ring-2 focus-visible:ring-black pl-10',
                  button: 'rounded-lg text-sm',
                }}
              />
            )}
          </CardContent>
        </Card>
      </div>
      <Separator className="my-0" />
    </>
  )
}

export default SkillsInterests
