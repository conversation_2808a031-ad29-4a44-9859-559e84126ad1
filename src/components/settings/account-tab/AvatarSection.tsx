'use client'

import * as React from 'react'
import { Separator } from '@/components/ui/separator'
import { AvatarUploadWithCrop } from '@/components/common/AvatarUploadWithCrop'
import useUser from '@/hooks/useUser'
import { updateUserAvatar, deleteUserAvatar } from '@/actions/user'
import { useToast } from '@/hooks/use-toast'

interface AvatarSectionProps {
  userId: string
}

const AvatarSection: React.FC<AvatarSectionProps> = ({ userId }) => {
  const { data: user, refetch } = useUser(userId)
  const { toast } = useToast()

  const handleAvatarUpload = async (avatarUrl: string) => {
    if (!userId) return

    try {
      await updateUserAvatar(userId, avatarUrl)
      // Revalidate user data to show the new avatar automatically
      await refetch()
      toast({
        title: 'Success!',
        description: 'Avatar updated successfully!',
      })
    } catch (error) {
      console.error('Error updating avatar:', error)
      toast({
        title: 'Error',
        description: 'Failed to update avatar. Please try again.',
        variant: 'destructive',
      })
    }
  }

  const handleAvatarDelete = async () => {
    if (!userId) return

    try {
      await deleteUserAvatar(userId)
      // Revalidate user data to show the removed avatar automatically
      await refetch()
      toast({
        title: 'Success!',
        description: 'Avatar removed successfully!',
      })
    } catch (error) {
      console.error('Error deleting avatar:', error)
      toast({
        title: 'Error',
        description: 'Failed to remove avatar. Please try again.',
        variant: 'destructive',
      })
    }
  }

  if (!user) {
    return null
  }

  return (
    <>
      <div className="flex flex-col sm:flex-row gap-6 items-start sm:items-center p-6">
        <div className="space-y-2">
          <AvatarUploadWithCrop
            onUpload={handleAvatarUpload}
            onDelete={handleAvatarDelete}
            currentAvatar={user.avatar}
            showDeleteButton={!!user.avatar}
          />
        </div>
      </div>

      <Separator className="my-0" />
    </>
  )
}

export default AvatarSection
