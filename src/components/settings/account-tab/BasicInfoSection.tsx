'use client'
import { FC, useRef, useState } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { UseFormReturn } from 'react-hook-form'
import useUser from '@/hooks/useUser'
import { Separator } from '@/components/ui/separator'
import { Button } from '@/components/ui/button'
import { useToast } from '@/hooks/use-toast'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'

interface BasicInfoSectionProps {
  form: UseFormReturn<any>
  userId: string
  onFieldChange?: (
    fieldName: 'handler' | 'fullName' | 'headline' | 'bio',
  ) => (value: string) => void
  successFields?: Set<string>
}

// Zod schema for email validation
const webflowEmailSchema = z.object({
  webflowEmail: z.string().email('Please enter a valid email'),
})

type WebflowEmailFormValues = z.infer<typeof webflowEmailSchema>

const BasicInfoSection: FC<BasicInfoSectionProps> = ({
  form,
  userId,
  onFieldChange,
  successFields = new Set(),
}) => {
  const { data: user, updateUserProfile } = useUser(userId)
  const { toast } = useToast()
  const [isUpdatingWebflowEmail, setIsUpdatingWebflowEmail] = useState(false)
  const [showEmailInput, setShowEmailInput] = useState(false)
  const [showConfirmationMessage, setShowConfirmationMessage] = useState(false)
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  // Form for Webflow email
  const webflowEmailForm = useForm<WebflowEmailFormValues>({
    resolver: zodResolver(webflowEmailSchema),
    defaultValues: { webflowEmail: '' },
  })

  // Auto-hide confirmation message function
  const startConfirmationTimer = () => {
    if (timerRef.current) {
      clearTimeout(timerRef.current)
    }
    timerRef.current = setTimeout(() => {
      setShowConfirmationMessage(false)
      timerRef.current = null
    }, 3000)
  }

  if (!user) {
    return null
  }

  const updateWebflowEmail = async (email: string) => {
    await updateUserProfile('accounts.webflow', email)
  }

  const handleWebflowEmailConfirmation = async (confirmed: boolean) => {
    setIsUpdatingWebflowEmail(true)
    try {
      if (confirmed) {
        await updateWebflowEmail(user.email)
        setShowEmailInput(false)
        setShowConfirmationMessage(true)
        startConfirmationTimer()
        toast({
          title: 'Success!',
          description: 'Webflow email has been confirmed and saved.',
        })
      } else {
        setShowEmailInput(true)
      }
    } catch (error) {
      console.error('Error updating webflow email:', error)
      toast({
        title: 'Error',
        description: 'Failed to update Webflow email. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsUpdatingWebflowEmail(false)
    }
  }

  const handleCustomEmailSubmit = async (data: WebflowEmailFormValues) => {
    setIsUpdatingWebflowEmail(true)
    try {
      await updateWebflowEmail(data.webflowEmail)
      setShowEmailInput(false)
      setShowConfirmationMessage(true)
      startConfirmationTimer()
      webflowEmailForm.reset()
      toast({
        title: 'Success!',
        description: 'Webflow email has been saved.',
      })
    } catch (error) {
      console.error('Error updating webflow email:', error)
      toast({
        title: 'Error',
        description: 'Failed to update Webflow email. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsUpdatingWebflowEmail(false)
    }
  }

  const handleCancelCustomEmail = () => {
    setShowEmailInput(false)
    webflowEmailForm.reset()
  }

  return (
    <>
      <div className="space-y-5 p-6 tracking-wide">
        <FormField
          control={form.control}
          name="fullName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input
                  placeholder="John Doe"
                  className={`w-full rounded-md border px-3 py-2 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-none focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm shadow-sm text-foreground h-12 text-base placeholder:text-gray-400 ${
                    successFields.has('fullName')
                      ? 'bg-green-50 border-gray-200 focus-visible:ring-2 focus-visible:ring-black'
                      : 'bg-gray-50 border-gray-200 focus-visible:ring-2 focus-visible:ring-black'
                  }`}
                  {...field}
                  onChange={e => {
                    field.onChange(e)
                    onFieldChange?.('fullName')(e.target.value)
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="handler"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Handle</FormLabel>
              <div className="flex items-center">
                <FormControl>
                  <Input
                    placeholder="your-handle"
                    className={`w-full rounded-md border px-3 py-2 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-none focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm shadow-sm text-foreground h-12 text-base placeholder:text-gray-400 ${
                      successFields.has('handler')
                        ? 'bg-green-50 border-gray-200 focus-visible:ring-2 focus-visible:ring-black'
                        : 'bg-gray-50 border-gray-200 focus-visible:ring-2 focus-visible:ring-black'
                    }`}
                    {...field}
                    onChange={e => {
                      field.onChange(e)
                      onFieldChange?.('handler')(e.target.value)
                    }}
                  />
                </FormControl>
              </div>
              <p className="text-xs text-muted-foreground">
                This is your unique username that appears in your profile URL.
              </p>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <div className="flex items-center gap-2">
            <Input
              id="email"
              type="email"
              value={user.email}
              disabled
              className="w-full rounded-md border px-3 py-2 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-none focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm shadow-sm text-foreground h-12 bg-gray-50 border-gray-200 text-base placeholder:text-gray-400 focus-visible:ring-2 focus-visible:ring-black"
            />
          </div>
        </div>

        <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-zinc-200">
          <div className="space-y-3">
            {!showEmailInput ? (
              <>
                <div className="flex items-center gap-3">
                  <img
                    src="/assets/webflow-logo.svg"
                    alt="Webflow"
                    className="h-4 w-4"
                  />
                  <p className="text-xs text-zinc-500 flex-1">
                    {user.profile?.accounts?.webflow ? (
                      <>
                        Webflow email:{' '}
                        <span className="font-bold">
                          {user.profile.accounts.webflow}
                        </span>
                      </>
                    ) : (
                      <>
                        Is your email (
                        <span className="font-bold">{user.email}</span>)
                        associated with your Webflow account?
                      </>
                    )}
                  </p>
                  <div className="flex gap-2">
                    {user.profile?.accounts?.webflow ? (
                      <Button
                        variant="outline"
                        className="text-xs h-[1.7rem] px-3 border-zinc-200"
                        onClick={() => setShowEmailInput(true)}
                        disabled={isUpdatingWebflowEmail}
                      >
                        Change
                      </Button>
                    ) : (
                      <>
                        <Button
                          variant="outline"
                          className="text-xs h-[1.7rem] w-[2.5rem] border-zinc-200"
                          onClick={() => handleWebflowEmailConfirmation(true)}
                          disabled={isUpdatingWebflowEmail}
                        >
                          Yes
                        </Button>
                        <Button
                          variant="outline"
                          className="text-xs h-[1.7rem] w-[2.5rem] border-zinc-200"
                          onClick={() => handleWebflowEmailConfirmation(false)}
                          disabled={isUpdatingWebflowEmail}
                        >
                          No
                        </Button>
                      </>
                    )}
                  </div>
                </div>
                {showConfirmationMessage && user.profile?.accounts?.webflow && (
                  <p className="text-xs text-green-600">
                    ✓ Webflow email confirmed: {user.profile.accounts.webflow}
                  </p>
                )}
              </>
            ) : (
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <img
                    src="/assets/webflow-logo.svg"
                    alt="Webflow"
                    className="h-4 w-4"
                  />
                  <p className="text-xs text-zinc-500 flex-1">
                    Please enter your Webflow account email:
                  </p>
                </div>
                <div className="space-y-3">
                  <FormField
                    control={webflowEmailForm.control}
                    name="webflowEmail"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            placeholder="<EMAIL>"
                            className="w-full rounded-md border px-3 py-2 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-none focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm shadow-sm text-foreground h-12 bg-white border-gray-200 text-base placeholder:text-gray-400 focus-visible:ring-2 focus-visible:ring-black"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      className="text-xs h-8 px-3"
                      disabled={
                        isUpdatingWebflowEmail ||
                        !webflowEmailForm.formState.isValid
                      }
                      onClick={webflowEmailForm.handleSubmit(
                        handleCustomEmailSubmit,
                      )}
                    >
                      {isUpdatingWebflowEmail ? 'Saving...' : 'Save Email'}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      className="text-xs h-8 px-3"
                      onClick={handleCancelCustomEmail}
                      disabled={isUpdatingWebflowEmail}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <Separator className="my-0" />
    </>
  )
}

export default BasicInfoSection
