import Link from 'next/link'
import Image from 'next/image'
import { Plus } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { getSessionUser } from '@/actions/auth'
import { getUserBundleProducts } from '@/actions/product'
import EmptyList from './EmptyList'
import ProfileListItem from './ProfileListItem'

export default async function ProfileListBundles() {
  const user = await getSessionUser()
  const products = await getUserBundleProducts(user.id as string)

  return products?.length !== 0 ? (
    <section>
      <h2 className="text-2xl font-bold">My Bundles</h2>
      <div className="grid grid-cols-3 gap-8">
        {products?.length === 0 ? (
          <div className="col-span-3">No assets found</div>
        ) : null}
        {products?.map(item => <ProfileListItem key={item.id} item={item} />)}
      </div>
    </section>
  ) : (
    <EmptyList>
      <div className="flex flex-col gap-6 items-center">
        <h2 className="text-xl text-gray-500">
          You can creating a bundle from your assets
        </h2>
        <Button
          asChild
          variant="default"
          className="w-fit flex-nowrap"
          size="xl"
        >
          <Link href="/dashboard/products/bundles/add">
            <Plus /> Create Bundle
          </Link>
        </Button>
      </div>
    </EmptyList>
  )
}
