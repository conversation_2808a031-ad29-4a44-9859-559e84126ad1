import Link from 'next/link'
import Image from 'next/image'
import { Plus } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { getSessionUser } from '@/actions/auth'
import { getUserSubscriptionProducts } from '@/actions/product'
import centsToDollars from '@/utils/cents-to-dollars'
import EmptyList from './EmptyList'
import { ImageSet } from '@/actions/image'
import { PaymentPrice } from '@/actions/price'

export default async function ProfileListSubscriptions() {
  const user = await getSessionUser()
  const products = await getUserSubscriptionProducts(
    user?.id?.toString() as string,
  )

  return products?.length !== 0 ? (
    <section>
      <h2 className="text-2xl font-bold">My Subscriptions</h2>
      <div className="grid grid-cols-3 gap-8">
        {products?.length === 0 ? (
          <div className="col-span-3">No assets found</div>
        ) : null}
        {products?.map(item => (
          <Link
            key={item.id}
            className="flex flex-col gap-4 p-4 rounded-md hover:bg-gray-200"
            href={`/dashboard/products/subscriptions/${item.id.toString().split(':').pop()}`}
          >
            {(item.imageSet as ImageSet)?.images?.[0]?.url ? (
              <Image
                src={(item.imageSet as ImageSet).images[0].url!}
                alt={(item.imageSet as ImageSet).images[0].name}
                width={426}
                height={256}
                blurDataURL="data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="
                className="rounded-md w-full h-64 object-cover ring-1 ring-gray-300"
              />
            ) : null}
            <hgroup className="flex flex-row justify-between text-sm">
              <h6 className="text-md text-gray-800">{item.name}</h6>
              <div>
                {(item.price as PaymentPrice)?.amount &&
                  (item.price as PaymentPrice)?.currencyCode &&
                  centsToDollars(
                    (item.price as PaymentPrice)?.amount,
                    (item.price as PaymentPrice)?.currencyCode,
                  )}
              </div>
            </hgroup>
          </Link>
        ))}
      </div>
    </section>
  ) : (
    <EmptyList>
      <div className="flex flex-col gap-6 items-center">
        <h2 className="text-xl text-gray-500">
          You can start offering a subscription plan to your clients.
        </h2>
        <p className="text-sm text-gray-400 w-[28rem] text-center">
          Partners can offer a subscription plan to their clients, giving them
          access to all available assets, including exclusive content, and
          templates to improve their projects and experiences.
        </p>
        <Button
          asChild
          variant="default"
          className="w-fit flex-nowrap"
          size="xl"
        >
          <Link href="/dashboard/products/subscriptions/add">
            <Plus /> Create a Subscription plan
          </Link>
        </Button>
      </div>
    </EmptyList>
  )
}
