'use client'

import { useEffect, useState } from 'react'

import { But<PERSON> } from '@/components/ui/button'
import {
  <PERSON>alog,
  <PERSON><PERSON>Close,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

import { validateEmail } from '@/utils/validate-email'

type Props = React.PropsWithChildren<any>

export default function ConfirmWebflowEmailModal({
  clickCloseModal,
  email,
  onEmailConfirmed,
  onEmailUpdate,
  children,
  openModal,
}: Props) {
  const [currentEmail, setCurrentEmail] = useState(email)
  const [webflowEmail, setWebflowEmail] = useState('')
  const [disabled, setDisabled] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (email) {
      setCurrentEmail(email)
    }
  }, [email])

  const clickLetMeChange = () => {
    setCurrentEmail('')
    setWebflowEmail('')
  }

  const onChangeHandler = (e: any) => {
    setWebflowEmail(e.target.value)
    if (error) {
      setError('')
    }
  }

  const clickConfirm = () => {
    if (!validateEmail(currentEmail)) {
      setError('Current email is not valid.')
      return
    }

    setDisabled(true)
    onEmailConfirmed(currentEmail)
  }

  const clickUpdateWebflowEmail = async () => {
    if (!webflowEmail) {
      setError('Email is required.')
      return
    }

    if (!validateEmail(webflowEmail)) {
      setError('Entered email is not valid.')
      return
    }

    setDisabled(true)
    await onEmailUpdate(webflowEmail)
    setDisabled(false)
    setWebflowEmail('')
  }

  return (
    <Dialog open={openModal}>
      <DialogTrigger asChild>{children}</DialogTrigger>

      <DialogContent aria-describedby="change your webflow email">
        <DialogHeader>
          <DialogTitle>Confirm your Webflow email</DialogTitle>
        </DialogHeader>
        <div className="space-y-6">
          {currentEmail ? (
            <p className="text-sm leading-relaxed text-gray-500 dark:text-gray-400">
              Kindly confirm whether <strong>{currentEmail}</strong> is the
              correct email linked to your Webflow account.
            </p>
          ) : (
            <>
              <p className="text-sm leading-relaxed text-gray-500 dark:text-gray-400">
                There is no Webflow{'\u2122'} email linked to your account.
                Please add your Webflow{'\u2122'} account email in order to
                receive your asset.
              </p>
              <Label className="text-xs">
                Webflow account email
                <Input
                  className="!w-full"
                  value={webflowEmail}
                  onChange={onChangeHandler}
                  type="email"
                />
              </Label>
              <small className="text-xs text-red-700">
                {error ? error : '\u00a0'}
              </small>
            </>
          )}
        </div>

        <DialogFooter>
          {currentEmail ? (
            <>
              <DialogClose asChild>
                <Button onClick={clickConfirm} disabled={disabled}>
                  Yes, continue
                </Button>
              </DialogClose>
              <Button
                variant="secondary"
                onClick={clickLetMeChange}
                disabled={disabled}
              >
                Let me change it
              </Button>
              <DialogClose asChild>
                <Button
                  variant="ghost"
                  onClick={clickCloseModal}
                  disabled={disabled}
                  className="!mr-auto"
                >
                  Cancel
                </Button>
              </DialogClose>
            </>
          ) : (
            <Button onClick={clickUpdateWebflowEmail} disabled={disabled}>
              Update my Webflow email
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
