import Link from 'next/link'

import TemplaceLogo from '@/components/common/TemplaceLogo'

export default function Footer({ invert = false, fullLogo = false }) {
  return (
    <footer className="flex justify-between items-center p-4 h-28 border-t-[1px] border-[#1a1a1a1a]">
      <h1>
        <Link href="/">
          <TemplaceLogo
            className={`w-9 h-9 ${invert ? 'invert' : ''}`}
            full={fullLogo}
          />
        </Link>
      </h1>
      <div className="flex gap-4 items-center text-gray-500 text-sm">
        <Link href="#/about">About</Link>
        <Link href="/terms">Terms</Link>
        <Link href="#/support">Support</Link>
      </div>
    </footer>
  )
}
