'use client'

import { useState } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { z } from 'zod'

import { Product } from '@/actions/product'
import generateBuyLink from '@/actions/url/generateBuyLink'
import ClippboardClient from '@/components/common/ClippboardClient'
import CodeBox from '@/components/common/CodeBox'
import FieldText from '@/components/form/FieldText'
import FromActions from '@/components/form/FormActions'
import FormSection from '@/components/form/FormSection'
import FormSubheader from '@/components/form/FormSubheader'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import { formCreateLinkSchema } from '@/schemas'
import { getPlatform } from '@/utils/get-platform'
import { zodResolver } from '@hookform/resolvers/zod'

type FormData = z.infer<typeof formCreateLinkSchema>

type Props = {
  product?: Product
  disabled: boolean
}

export default function ProfileProductCreateLink({ product, disabled }: Props) {
  const [hide, setHide] = useState(true)
  const [link, setLink] = useState('')

  const formMethods = useForm<FormData>({
    disabled,
    defaultValues: {
      website: '',
      redirect: '',
      landing: 'confirmation',
    },
    resolver: zodResolver(formCreateLinkSchema),
  })

  const onValid = async (formData: FormData) => {
    const externalLinkData = {
      ...formData,
      productId: product?.id?.toString()?.split(':')?.pop(),
      platform: getPlatform(product?.platforms),
    }

    // console.log('externalLinkData', externalLinkData)

    const nextLink = await generateBuyLink(externalLinkData)
    setLink(nextLink)
  }

  const handleSubmit = async () => {
    // console.log('handleSubmit')
    await formMethods.handleSubmit(onValid, console.log)()
  }

  const toggleHideShowHandler = () => {
    setHide(!hide)
  }

  return (
    <div className="grid grid-cols-[1fr_auto]">
      <FormSubheader
        title="Create link"
        description="Create a link for your Product and sell it online on any website."
      />

      <FormProvider {...formMethods}>
        <form
          className={cn('w-full col-span-2', hide && 'hidden')}
          onSubmit={formMethods.handleSubmit(onValid)}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
            <FieldText
              name="website"
              placeholder="Instagram profile"
              label="Website name"
              instructions="Website that you want to generate the link for."
            />

            <FieldText
              name="redirect"
              placeholder="https://my-template.webflow.io/"
              label="Redirect URL"
              instructions="Redirect back to this page when done"
            />
          </div>

          <FormSection>
            <hgroup className="mt-2">
              <h3 className="block mb-1 text-sm">Select Landing option</h3>
              <div className="text-xs text-gray-500">
                Select if your users should be redirected straightly to the
                checkout or if they should be taken to a confirmation page
              </div>
            </hgroup>

            <RadioGroup
              defaultValue="confirmation"
              onValueChange={value =>
                formMethods.setValue('landing', value as FormData['landing'])
              }
              value={formMethods.watch('landing')}
            >
              <div className="flex items-center gap-2">
                <RadioGroupItem
                  value="confirmation"
                  id="landing-confirmation"
                />
                <Label htmlFor="landing-confirmation">
                  Pass through confirmation page first
                </Label>
              </div>
              <div className="flex items-center gap-2">
                <RadioGroupItem value="direct" id="landing-direct" />
                <Label htmlFor="landing-direct">
                  Go directly to stripe checkout
                </Label>
              </div>
            </RadioGroup>
            {formMethods.formState.errors.landing && (
              <small className="text-sm text-destructive">
                {formMethods.formState.errors.landing.message}
              </small>
            )}
            <Separator className="my-0 md:my-0" />
            {link ? (
              <>
                <div className="grid grid-cols-[auto_1fr] place-items-start gap-2">
                  <div className="col-span-2 text-sm mb-4">Link created:</div>
                  <CodeBox>{link}</CodeBox>
                  <ClippboardClient valueToCopy={link} />
                </div>

                <Separator className="my-0 md:my-0" />
              </>
            ) : null}
          </FormSection>
        </form>

        <FromActions className={hide ? 'mt-0' : 'mt-6'}>
          <Button variant="outline" size="sm" onClick={toggleHideShowHandler}>
            {hide ? 'Show' : 'Hide'}
          </Button>
          {!hide && (
            <Button size="sm" onClick={handleSubmit}>
              Generate
            </Button>
          )}
        </FromActions>
      </FormProvider>
    </div>
  )
}
