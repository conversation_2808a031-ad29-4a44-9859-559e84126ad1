'use client'

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Circle<PERSON><PERSON>,
  <PERSON>7,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from 'lucide-react'
import { startTransition } from 'react'

import { TransferSendStatus } from '@/actions/transfer'
import ConfirmWebflowEmailModal from '@/components/internal/ConfirmWebflowEmailModal'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

import { useToast } from '@/hooks/use-toast'
import useTransferById from '@/hooks/use-transfer-by-id'
import useSessionUser from '@/hooks/useSessionUser'
import useUser from '@/hooks/useUser'

interface Props {
  transferId: string
}

/**
 *
 */
export default function TransferStatusControl({ transferId }: Props) {
  const {
    data: transfer,
    isLoading,
    updateTransferStatus,
  } = useTransferById(transferId)
  const { data: userInSession } = useSessionUser()
  const { data: user, updateUserProfile } = useUser(userInSession?.id as string)
  const { toast } = useToast()

  const clickStartTransfer = () => {
    startTransition(async () => {
      await updateTransferStatus(transferId, TransferSendStatus.PENDING)
      toast({
        title: 'Transfer started',
        description:
          'The template transfer process has been started. You will receive an email when it is done.',
        variant: 'success',
      })
    })
  }

  const updateEmailHandler = async (email: string) => {
    await updateUserProfile('webflow.email', email)
  }

  return (
    <>
      <div className="w-[12rem]">
        {isLoading || !transfer ? (
          <Badge variant="grey" icon={<Clock7 className="w-3 h-3" />}>
            Loading
          </Badge>
        ) : null}

        {transfer?.status === TransferSendStatus.INITIAL ? (
          <ConfirmWebflowEmailModal
            email={user?.profile?.webflow?.email}
            onEmailConfirmed={clickStartTransfer}
            onEmailUpdate={updateEmailHandler}
          >
            <Button>Start Transfer</Button>
          </ConfirmWebflowEmailModal>
        ) : (
          <>
            {transfer?.status === TransferSendStatus.PENDING && (
              <Badge
                variant="yellow"
                icon={<TriangleAlert className="w-3 h-3" />}
              >
                Pending
              </Badge>
            )}

            {transfer?.status === TransferSendStatus.PROGRESS && (
              <Badge variant="blue" icon={<Wrench className="w-3 h-3" />}>
                In Progress
              </Badge>
            )}

            {transfer?.status === TransferSendStatus.COMPLETE && (
              <Badge variant="green" icon={<Check className="w-3 h-3" />}>
                Completed
              </Badge>
            )}

            {transfer?.status === TransferSendStatus.CANCELED && (
              <Badge variant="red" icon={<CircleSlash className="w-3 h-3" />}>
                Canceled
              </Badge>
            )}

            {transfer?.status === TransferSendStatus.UNKNOWN && (
              <Badge variant="grey" icon={<CircleHelp className="w-3 h-3" />}>
                Unknow
              </Badge>
            )}
          </>
        )}
      </div>
    </>
  )
}
