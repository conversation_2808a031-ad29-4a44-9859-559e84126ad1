'use client'
import { headers } from 'next/headers'

import { getSessionUser } from '@/actions/auth'
import { HEADER_PATHNAME } from '@/config'

import ButtonSwitcher from '@/components/common/ButtonSwitcher'
import useSessionUser from '@/hooks/useSessionUser'
import { usePathname } from 'next/navigation'

interface Props {
  children?: React.ReactNode
  pathname?: string
}

export default function ProfileTabSwitcher({ children }: Props) {
  const pathname = usePathname()
  const { data: user } = useSessionUser()

  const isMember = !!user?.hats?.find(
    item =>
      item.slug === 'member' ||
      item.slug === 'admin' ||
      item.slug === 'creator',
  )

  const buttons = [
    {
      label: 'Purchases',
      href: '/dashboard/purchases',
      // totals: '21',
    },
    ...(isMember
      ? [
          {
            label: 'Assets',
            href: '/dashboard/assets',
            // totals: '12',
            disabled: !isMember,
          },
          {
            label: 'Bundles',
            href: '/dashboard/bundles',
            // totals: '12',
            disabled: !isMember,
          },
          {
            label: 'Subscriptions',
            href: '/dashboard/subscriptions',
            // totals: '12',
            disabled: !isMember,
          },
          {
            label: 'Earnings',
            href: '/dashboard/earnings',
            // totals: '21',
          },
        ]
      : []),
  ]

  return (
    <nav className="flex flex-row justify-between">
      <ButtonSwitcher
        buttons={buttons}
        value={pathname?.split('/').slice(0, 3).join('/')}
      />
      {children}
    </nav>
  )
}
