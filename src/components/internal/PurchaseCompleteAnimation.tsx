'use client'

import dynamic from 'next/dynamic'
import { CheckCircleIcon } from '@heroicons/react/24/solid'

const Player = dynamic<any>(
  // import { Player } from "@lottiefiles/react-lottie-player";
  () => import('@lottiefiles/react-lottie-player').then(({ Player }) => Player),
  {
    ssr: false,
  },
)

export default function PurchaseCompleteAnimation() {
  return (
    <>
      <div className="flex w-full h-[14rem] justify-center">
        <Player
          src="/animations/confetti-animation.json"
          autoplay
          keepLastFrame
          style={{ width: '100%', height: '100%' }}
        />
      </div>
      <CheckCircleIcon className="w-20 h-20 text-green-500 absolute top-[6rem] left-[50%] translate-x-[-50%] cursor-pointer" />
    </>
  )
}
