import React from 'react'
import Link from 'next/link'
import Image from 'next/image'

import centsToDollars from '@/utils/cents-to-dollars'
import PlatformWidget from '@/components/products/PlatformWidget'
import { getPlatformValue } from '@/utils/get-platform'

export default function ProfileListItem({ item }: any) {
  const cols = React.useMemo(() => {
    if (item?.children?.assets?.length === 0) return ''
    if (item?.children?.assets?.length % 4 === 0) return 'grid-cols-4'
    if (item?.children?.assets?.length % 3 === 0) return 'grid-cols-3'
    if (item?.children?.assets?.length % 2 === 0) return 'grid-cols-2'
    return 'grid-cols-1'
  }, [item?.children?.assets])

  return (
    <Link
      key={item.id}
      className="flex flex-col gap-4 p-4 rounded-md hover:bg-gray-200"
      href={`/dashboard/products/bundles/${item.id.toString().split(':').pop()}`}
    >
      {item.imageSet?.images?.[0]?.url ? (
        <Image
          src={item.imageSet.images[0].url}
          alt={item.imageSet.images[0].name}
          width={426}
          height={256}
          blurDataURL="data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="
          className="rounded-md w-full h-64 object-cover ring-1 ring-gray-300"
        />
      ) : null}

      {item?.children?.assets?.length !== 0 ? (
        <div className={`w-full grid ${cols} gap-2`}>
          {item?.children?.assets?.map((asset: any) => (
            <div key={asset.id} className="overflow-hidden relative">
              {asset.imageSet?.images?.[0]?.url ? (
                <Image
                  src={asset.imageSet.images[0].url}
                  alt={asset.imageSet.images[0].name}
                  width={256}
                  height={128}
                  blurDataURL="data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="
                  className="rounded-md w-full h-[3rem] object-cover ring-1 ring-gray-300"
                />
              ) : null}
              <PlatformWidget
                className="absolute top-1 left-1"
                platform={getPlatformValue(asset.platforms)}
                justIcon
              />
            </div>
          ))}
        </div>
      ) : null}

      <hgroup className="flex flex-row justify-between text-sm">
        <h6 className="text-xs text-gray-700 font-bold">{item.name}</h6>
        <div className="text-xs text-gray-700">
          {item.price?.amount &&
            item.price?.currencyCode &&
            centsToDollars(item.price?.amount, item.price?.currencyCode)}
        </div>
      </hgroup>
    </Link>
  )
}
