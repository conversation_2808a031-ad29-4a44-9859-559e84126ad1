import { PlatformsEnum } from '@/actions/product'

interface Props {
  platform?: string
}

export default function PaymentCompleteMessage({ platform }: Props) {
  if (platform === PlatformsEnum.WEBFLOW) {
    return (
      <div className="text-gray-600 w-11/12">
        You will get an email from Webflow titled{' '}
        <strong className="font-bold">
          ”You have a site transfer request.”
        </strong>
        , among other email updates from Tem.place about your transfer status.
        Click the{' '}
        <strong className="font-bold">”Accept and transfer site”</strong> button
        to get the template to your account.
      </div>
    )
  }

  if (platform === PlatformsEnum.FRAMER) {
    return (
      <div className="text-gray-600 w-11/12">
        Go to your account in your{' '}
        <strong className="font-bold">{`"Purchases"`}</strong> list.
        <br />
        Click on selected{' '}
        <strong className="font-bold">{`"Remix Link"`}</strong> to be linked to
        your template.
      </div>
    )
  }

  if (platform) {
    return <div className="text-gray-600 w-11/12" />
  }

  return null
}
