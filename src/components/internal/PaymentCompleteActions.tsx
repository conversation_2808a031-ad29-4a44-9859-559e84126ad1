'use client'

import { startTransition, useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'

import useSessionUser from '@/hooks/useSessionUser'
import { startTransferForUser } from '@/actions/transfer'

import ConfirmWebflowEmailModal from './ConfirmWebflowEmailModal'
import { UserProfile } from '@/actions/user'
import useUser from '@/hooks/useUser'

interface Props {
  transferId?: string
  redirect?: string
  platform?: string
}

export default function PaymentCompleteActions({
  transferId,
  redirect,
  platform,
}: Props) {
  const router = useRouter()
  const { data: user } = useSessionUser()
  const { updateUserProfile } = useUser(user?.id as string)

  const [openModal, setOpenModal] = useState(false)

  const clickOpenModal = () => {
    setOpenModal(true)
  }

  const clickCloseModal = () => {
    setOpenModal(false)
  }

  const clickStartTransfer = () => {
    startTransition(async () => {
      if (!transferId) return
      await startTransferForUser(transferId, user?.id as string)
      router.push('/profile')
    })
  }

  const updateEmailHandler = async (email: string) => {
    await updateUserProfile('webflow.email', email)
  }

  return transferId ? (
    <>
      <div className="flex gap-4 pt-8 items-center justify-center">
        <Button variant="ghost">
          <Link type="button" href="/dashboard/purchases">
            Go to my Dashboard
          </Link>
        </Button>

        {redirect ? (
          <Button asChild>
            <Link type="button" href={redirect}>
              Bring me back where I came from
            </Link>
          </Button>
        ) : null}
      </div>

      <ConfirmWebflowEmailModal
        openModal={openModal}
        clickCloseModal={clickCloseModal}
        email={(user?.profile as UserProfile)?.webflow?.email}
        onEmailConfirmed={clickStartTransfer}
        onEmailUpdate={updateEmailHandler}
      />
    </>
  ) : null
}
