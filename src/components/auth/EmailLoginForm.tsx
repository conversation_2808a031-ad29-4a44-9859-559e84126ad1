'use client'

import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import {
  Form,
  FormField,
  FormItem,
  FormControl,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { sendEmailCode } from '@/actions/auth'

const emailSchema = z.object({
  email: z.string().email('Digite um e-mail válido'),
})

type EmailFormValues = z.infer<typeof emailSchema>

export default function EmailLoginForm() {
  const router = useRouter()
  const form = useForm<EmailFormValues>({
    resolver: zodResolver(emailSchema),
    defaultValues: { email: '' },
  })

  const [emailError, setEmailError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)

  async function onSubmit(data: EmailFormValues) {
    setEmailError(null)
    setIsLoading(true)

    const result = await sendEmailCode({ email: data.email })

    if (!result.success) {
      setIsLoading(false)
      setEmailError(result?.error || 'Error on send email.')
      return
    }

    setIsLoading(false)
    router.push(`/login/verify-code?email=${data.email}`)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 w-full">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input type="email" placeholder="Email" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {emailError && (
          <div className="text-red-600 text-sm text-center">{emailError}</div>
        )}
        <Button
          type="submit"
          variant="primary"
          className="w-full"
          disabled={!form.formState.isValid}
        >
          {isLoading ? 'Loading...' : 'Continue'}
        </Button>
      </form>
    </Form>
  )
}
