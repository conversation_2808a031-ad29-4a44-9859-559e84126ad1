'use client'

import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import {
  Form,
  FormField,
  FormItem,
  FormControl,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { sendEmailCode } from '@/actions/auth'
import { verifyUserToken } from '@/actions/user/verify-new-user-token'
import { signIn, useSession } from 'next-auth/react'
import { OnboardStatus, UserOnboard } from '@/actions/user'

const codeSchema = z.object({
  code: z
    .string()
    .length(5, 'The code must be exactly 5 characters long')
    .regex(/^\d+$/, 'The code must contain only numbers'),
})

type CodeFormValues = z.infer<typeof codeSchema>

type ResendButtonState = 'idle' | 'loading' | 'sent'

export default function VerifyCodeForm({ userEmail }: { userEmail: string }) {
  const { data: session, status } = useSession()
  const router = useRouter()

  const form = useForm<CodeFormValues>({
    resolver: zodResolver(codeSchema),
    defaultValues: { code: '' },
  })

  const [codeError, setCodeError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [resendState, setResendState] = useState<ResendButtonState>('idle')

  async function onSubmit(data: CodeFormValues) {
    setCodeError(null)
    setIsLoading(true)

    if (!userEmail || !data.code) return

    try {
      const isValid = await verifyUserToken({
        email: userEmail,
        code: data.code,
      })

      if (!isValid) {
        setIsLoading(false)
        setCodeError('Invalid token')
        return
      }

      signIn('credentials', {
        email: userEmail,
      })
    } catch (error) {
      setIsLoading(false)
      setCodeError('An error occurred')
    }
  }

  async function handleResendCode() {
    if (!userEmail || resendState !== 'idle') return

    setResendState('loading')
    const result = await sendEmailCode({ email: userEmail })

    if (!result.success) {
      setResendState('idle')
      return
    }

    setResendState('sent')
    setTimeout(() => {
      setResendState('idle')
    }, 5000)
  }

  const getResendButtonText = () => {
    switch (resendState) {
      case 'loading':
        return 'Resending...'
      case 'sent':
        return 'Resent'
      default:
        return 'Resend code'
    }
  }

  return (
    <div className="flex flex-col gap-8">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="space-y-4 w-full"
        >
          <FormField
            control={form.control}
            name="code"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    type="text"
                    placeholder="Enter verification code"
                    maxLength={5}
                    className="text-center"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {codeError && (
            <div className="text-red-600 text-sm text-center">{codeError}</div>
          )}
          <Button
            type="submit"
            variant="primary"
            className="w-full"
            disabled={!form.formState.isValid || isLoading}
          >
            {isLoading ? 'Verifying...' : 'Verify Code'}
          </Button>
        </form>
      </Form>
      <div className="text-center space-y-2">
        <p className="text-sm text-gray-600">{`Didn't`} receive the code?</p>
        <button
          className="text-blue-600 text-sm hover:underline disabled:opacity-50 disabled:cursor-not-allowed"
          onClick={handleResendCode}
          disabled={resendState !== 'idle'}
        >
          {getResendButtonText()}
        </button>
      </div>
    </div>
  )
}
