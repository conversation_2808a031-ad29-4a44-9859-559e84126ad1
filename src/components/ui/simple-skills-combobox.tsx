'use client'

import * as React from 'react'
import { Check, ChevronsUpDown, X, Search } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { type Skill } from '@/actions/skill'

interface SimpleSkillsComboboxContextProps {
  skills: Skill[]
  selectedSkills: string[]
  onSkillsChange: (skillIds: string[]) => void
  searchValue: string
  setSearchValue: (v: string) => void
  open: boolean
  setOpen: (v: boolean) => void
  classNames?: Partial<SimpleSkillsComboboxClassNames>
  maxSkills?: number
}

const SimpleSkillsComboboxContext = React.createContext<
  SimpleSkillsComboboxContextProps | undefined
>(undefined)

function useComboboxCtx() {
  const ctx = React.useContext(SimpleSkillsComboboxContext)
  if (!ctx)
    throw new Error(
      'SimpleSkillsCombobox.* must be used within <SimpleSkillsCombobox.Root>',
    )
  return ctx
}

export interface SimpleSkillsComboboxClassNames {
  root: string
  input: string
  popover: string
  list: string
  item: string
  badge: string
  badgeList: string
  button: string
}

interface SimpleSkillsComboboxRootProps {
  skills: Skill[]
  selectedSkills: string[]
  onSkillsChange: (skillIds: string[]) => void
  placeholder?: string
  searchPlaceholder?: string
  emptyText?: string
  disabled?: boolean
  className?: string
  classNames?: Partial<SimpleSkillsComboboxClassNames>
  children?: React.ReactNode
  maxSkills?: number
}

function Root({
  skills = [],
  selectedSkills = [],
  onSkillsChange,
  placeholder = 'Select skills...',
  searchPlaceholder = 'Search skills...',
  emptyText = 'No skills found.',
  disabled = false,
  className,
  classNames,
  children,
  maxSkills,
}: SimpleSkillsComboboxRootProps) {
  const [open, setOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState('')

  // Ensure skills and selectedSkills are arrays
  const safeSkills = Array.isArray(skills) ? skills : []
  const safeSelectedSkills = Array.isArray(selectedSkills) ? selectedSkills : []

  const ctxValue: SimpleSkillsComboboxContextProps = {
    skills: safeSkills,
    selectedSkills: safeSelectedSkills,
    onSkillsChange,
    searchValue,
    setSearchValue,
    open,
    setOpen,
    classNames,
    maxSkills,
  }

  return (
    <SimpleSkillsComboboxContext.Provider value={ctxValue}>
      <div className={cn('space-y-3', className, classNames?.root)}>
        {children ? (
          children
        ) : (
          <DefaultCombobox
            placeholder={placeholder}
            searchPlaceholder={searchPlaceholder}
            emptyText={emptyText}
            disabled={disabled}
            maxSkills={maxSkills}
          />
        )}
      </div>
    </SimpleSkillsComboboxContext.Provider>
  )
}

function DefaultCombobox({
  placeholder,
  searchPlaceholder,
  emptyText,
  disabled,
  maxSkills,
}: {
  placeholder: string
  searchPlaceholder: string
  emptyText: string
  disabled?: boolean
  maxSkills?: number
}) {
  const {
    skills,
    selectedSkills,
    onSkillsChange,
    searchValue,
    setSearchValue,
    open,
    setOpen,
    classNames,
  } = useComboboxCtx()

  const handleSkillToggle = (skillId: string) => {
    const newSelectedSkills = selectedSkills.includes(skillId)
      ? selectedSkills.filter(id => id !== skillId)
      : [...selectedSkills, skillId]
    onSkillsChange(newSelectedSkills)
  }

  const handleRemoveSkill = (skillId: string) => {
    const newSelectedSkills = selectedSkills.filter(id => id !== skillId)
    onSkillsChange(newSelectedSkills)
  }

  const selectedSkillsData = skills.filter(skill =>
    selectedSkills.includes(skill.id),
  )
  const availableSkills = skills.filter(
    skill => !selectedSkills.includes(skill.id),
  )
  const filteredSkills = searchValue
    ? availableSkills.filter(
        skill =>
          skill.name.toLowerCase().includes(searchValue.toLowerCase()) ||
          skill.description?.toLowerCase().includes(searchValue.toLowerCase()),
      )
    : availableSkills

  return (
    <>
      <BadgeList
        onRemove={handleRemoveSkill}
        selectedSkillsData={selectedSkillsData}
      />
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              'w-full justify-between h-12 bg-gray-50 border-gray-200 text-base placeholder:text-gray-400 focus-visible:ring-2 focus-visible:ring-black',
              classNames?.button,
            )}
            disabled={disabled}
          >
            <div className="flex items-center gap-2">
              {maxSkills && selectedSkills.length > 0 ? (
                <span className="text-sm text-zinc-500">
                  ({selectedSkills.length}/{maxSkills}) Selected skills
                </span>
              ) : (
                <span>{placeholder}</span>
              )}
            </div>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className={cn(
            'w-[var(--radix-popover-trigger-width)] p-0',
            classNames?.popover,
          )}
          align="start"
        >
          <div className="p-3 border-b">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={searchPlaceholder}
                value={searchValue}
                onChange={e => setSearchValue(e.target.value)}
                className={cn('pl-10', classNames?.input)}
              />
            </div>
          </div>
          <div className={cn('max-h-60 overflow-auto', classNames?.list)}>
            {filteredSkills && filteredSkills.length > 0 ? (
              <div className="p-1">
                {filteredSkills.map(skill => (
                  <div
                    key={skill.id}
                    className={cn(
                      'flex items-center gap-2 p-2 hover:bg-accent hover:text-accent-foreground rounded-sm cursor-pointer',
                      classNames?.item,
                    )}
                    onClick={() => {
                      handleSkillToggle(skill.id)
                      setSearchValue('')
                    }}
                  >
                    <Check
                      className={cn(
                        'h-4 w-4',
                        selectedSkills.includes(skill.id)
                          ? 'opacity-100'
                          : 'opacity-0',
                      )}
                    />
                    <div className="flex flex-col">
                      <span className="font-medium text-sm">{skill.name}</span>
                      {/* {skill.description && (
                        <span className="text-xs text-muted-foreground">
                          {skill.description}
                        </span>
                      )} */}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-4 text-center text-sm text-muted-foreground">
                {emptyText}
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>
    </>
  )
}

function BadgeList({
  selectedSkillsData,
  onRemove,
}: {
  selectedSkillsData: Skill[]
  onRemove: (id: string) => void
}) {
  const { classNames } = useComboboxCtx()
  if (!selectedSkillsData.length) return null
  return (
    <div className={cn('flex flex-wrap gap-2', classNames?.badgeList)}>
      {selectedSkillsData.map(skill => (
        <Badge
          key={skill.id}
          variant="secondary"
          className={cn(
            'cursor-pointer font-normal hover:bg-red-500 hover:text-destructive-foreground border-transparent rounded-full text-xs',
            classNames?.badge,
          )}
          onClick={() => onRemove(skill.id)}
        >
          {skill.name}
          <X className="ml-1 h-3 w-3" />
        </Badge>
      ))}
    </div>
  )
}

export const SimpleSkillsCombobox = Object.assign(Root, {
  BadgeList,
})
