import * as React from 'react'
import { cn } from '@/lib/utils'
import { Check, Circle } from 'lucide-react'

export type TimelineStepStatus = 'completed' | 'in-progress' | 'pending'

interface TimelineStepProps extends React.PropsWithChildren {
  status?: TimelineStepStatus
  title?: string
}

export const TimelineStep: React.FC<TimelineStepProps> = ({
  status,
  title,
  children,
}) => {
  return (
    <li>
      {status ? (
        <span className="flex items-center justify-center w-8 h-8 rounded-full -left-4 ring-4 ring-white dark:ring-gray-900">
          {status === 'completed' && (
            <Check className="w-5 h-5 text-muted-foreground" />
          )}
          {status === 'in-progress' && (
            <Circle className="w-5 h-5 text-muted-foreground" />
          )}
          {status === 'pending' && (
            <Circle className="w-5 h-5 text-muted-foreground" />
          )}
        </span>
      ) : null}
      {title ? (
        <h3 className="mb-1 text-lg font-semibold text-foreground">{title}</h3>
      ) : null}
      <div className="p-6 bg-background border-t border-muted-foreground/20">
        {children}
      </div>
    </li>
  )
}

export const Timeline: React.FC<React.PropsWithChildren> = ({
  children,
}) => {
  return (
    <ol className="relative border-l border-muted-foreground/20">{children}</ol>
  )
}
