import * as React from 'react'
import { cva, type VariantProps } from 'class-variance-authority'

import { cn } from '@/lib/utils'

const badgeVariants = cva(
  'inline-flex items-center rounded-sm border transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border border-black/10',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/80',
        secondary:
          'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        light: 'bg-neutral-50 hover:bg-neutral-100 text-gray-800 font-normal',
        destructive:
          'bg-destructive text-destructive-foreground hover:bg-destructive/80',
        red: 'bg-red-100 text-red-800 hover:bg-red-200',
        green: 'bg-green-100 text-green-800 hover:bg-green-200',
        blue: 'bg-blue-100 text-blue-800 hover:bg-blue-200',
        yellow: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',
        purple: 'bg-purple-100 text-purple-800 hover:bg-purple-200',
        grey: 'bg-gray-100 text-gray-800 hover:bg-gray-200',
      },
      size: {
        sm: 'px-1 py-[0.125em] text-[0.6rem] gap-[0.25em]',
        md: 'px-2 py-[0.25em] text-xs font-semibold gap-[0.5em]',
        lg: 'px-3 py-[0.5em] text-sm font-semibold gap-[0.75em]',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
    },
  },
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  variant?:
    | 'default'
    | 'secondary'
    | 'destructive'
    | 'red'
    | 'green'
    | 'blue'
    | 'yellow'
    | 'purple'
    | 'grey'
    | 'light'
  size?: 'sm' | 'md' | 'lg'
  icon?: React.ReactNode
  className?: string
}

function Badge({
  className,
  variant = 'default',
  size = 'md',
  icon,
  ...props
}: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant, size }), className)} {...props}>
      {icon && <span className="mr-1">{icon}</span>}
      {props.children}
    </div>
  )
}

export { Badge, badgeVariants }
