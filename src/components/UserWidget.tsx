import Image from 'next/image'

interface Props {
  name?: string
  avatar?: string
  className?: string
}

export default async function UserWidget({ className, name, avatar }: Props) {
  return (
    <div className={`flex flex-row items-center gap-1 ${className}`}>
      <span className="text-[.7rem] text-gray-500 font-semibold">{name}</span>
      {avatar ? (
        <Image
          src={avatar}
          alt="webflow logo"
          width={256}
          height={256}
          className="block w-6 h-6 rounded-full bg-gray-300 object-cover"
        />
      ) : null}
    </div>
  )
}
