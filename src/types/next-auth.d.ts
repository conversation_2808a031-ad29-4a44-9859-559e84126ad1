import { UserOnboard, UserHat } from '@/actions/user'
import NextA<PERSON>, { DefaultSession } from 'next-auth'

declare module 'next-auth' {
  interface Session {
    providerId?: string
    user: {
      id: string
      email: string
      name: string | null
      avatar?: string | null
      handler?: string
      onboardStatus?: UserOnboard
      hats?: UserHat[]
      [key: string]: any
    } & DefaultSession['user']
  }
}
