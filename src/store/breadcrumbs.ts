import { create } from 'zustand'

interface BreadcrumbItem {
  label: string
  href?: string
}

interface BreadcrumbsOverrideStore {
  overrides: Record<string, BreadcrumbItem[]>
  setOverrideForPath: (pathname: string, breadcrumbs: BreadcrumbItem[]) => void
  clearOverrideForPath: (pathname: string) => void
  clearAllOverrides: () => void
  getOverrideForPath: (pathname: string) => BreadcrumbItem[]
}

export const useBreadcrumbsOverrideStore = create<BreadcrumbsOverrideStore>(
  (set, get) => ({
    overrides: {},

    setOverrideForPath: (pathname, breadcrumbs) =>
      set(state => ({
        overrides: { ...state.overrides, [pathname]: breadcrumbs },
      })),

    clearOverrideForPath: pathname =>
      set(state => {
        const newOverrides = { ...state.overrides }
        delete newOverrides[pathname]
        return { overrides: newOverrides }
      }),

    clearAllOverrides: () => set({ overrides: {} }),

    getOverrideForPath: pathname => get().overrides[pathname] || [],
  }),
)

export function useBreadcrumbsOverride(
  pathname: string,
  breadcrumbs: BreadcrumbItem[],
) {
  const setOverrideForPath = useBreadcrumbsOverrideStore(
    state => state.setOverrideForPath,
  )

  if (breadcrumbs.length > 0) {
    setOverrideForPath(pathname, breadcrumbs)
  }
}

export function useCurrentOverride(pathname: string) {
  return useBreadcrumbsOverrideStore(state =>
    state.getOverrideForPath(pathname),
  )
}
