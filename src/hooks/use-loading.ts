import { useQuery, useQueryClient } from '@tanstack/react-query'

import { QUERY_LOADING } from '@/io/query-client'

export function useLoading() {
  const queryKey = QUERY_LOADING
  const queryClient = useQueryClient()

  const query = useQuery({
    queryKey,
    queryFn: () => Promise.resolve({ message: undefined }),
  })

  const setLoading = (message?: string) => {
    queryClient.setQueryData(queryKey, { message: message ? message : '' })
  }

  const clearLoading = () => {
    queryClient.setQueryData(queryKey, { message: undefined })
  }

  return {
    ...query,
    isLoading: query.data?.message !== undefined,
    setLoading,
    clearLoading,
  }
}
