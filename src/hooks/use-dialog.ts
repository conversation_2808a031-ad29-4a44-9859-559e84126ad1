import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

import { QUERY_DIALOG } from '@/io/query-client'

export const useDialog = () => {
  const queryClient = useQueryClient()
  const query = useQuery({
    queryKey: QUERY_DIALOG,
    queryFn: () => queryClient.getQueryData(QUERY_DIALOG) ?? '',
    initialData: '',
  })

  const mutation = useMutation({
    mutationFn: async (dialogId: string | null) => {
      queryClient.setQueryData(QUERY_DIALOG, dialogId)
      return dialogId
    },
  })

  const onOpen = (dialogId: string) => {
    mutation.mutate(dialogId)
  }

  const onClose = () => {
    mutation.mutate('')
  }

  return { ...query, onOpen, onClose }
}
