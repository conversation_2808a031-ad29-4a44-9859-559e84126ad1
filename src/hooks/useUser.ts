import { useMemo } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'

import { QUERY_USER } from '@/io/query-client'
import {
  getUserById,
  setUserOnboardStatus,
  updateUserProfileData,
  updateUserHandler,
} from '@/actions/user'

export default function useUser(userId: string) {
  const query = useQuery({
    queryKey: [...QUERY_USER, { userId }],
    queryFn: async () => {
      if (userId) {
        const user = await getUserById(userId)
        return user
      }
      return null
    },
    enabled: !!userId,
  })

  const onboard = useMutation({
    mutationFn: async ({ userId, onboardStatus }: any) =>
      setUserOnboardStatus(userId, onboardStatus),
    onSuccess: () => {
      query.refetch()
    },
  })

  const profile = useMutation({
    mutationFn: async ({ key, value }: any) =>
      updateUserProfileData(userId, key, value),
    onSuccess: () => {
      query.refetch()
    },
  })

  const handler = useMutation({
    mutationFn: async ({ newHandler }: { newHandler: string }) =>
      updateUserHandler(userId, newHandler),
    onSuccess: () => {
      query.refetch()
    },
  })

  const updateUserOnboardStatus = async (userId: string, onboardStatus: any) =>
    onboard.mutateAsync({ userId, onboardStatus })

  const updateUserProfile = async (key: string, value: any) =>
    profile.mutateAsync({ key, value })

  const updateUserHandlerAsync = async (newHandler: string): Promise<any> =>
    handler.mutateAsync({ newHandler })

  const isMember = useMemo(
    () => !!query.data?.hats?.find(item => item.slug === 'member'),
    [query.data?.hats],
  )

  const isAdmin = useMemo(
    () => !!query.data?.hats?.find(item => item.slug === 'admin'),
    [query.data?.hats],
  )

  return {
    ...query,
    isLoading: query.isLoading,
    isMember,
    isAdmin,
    updateUserOnboardStatus,
    updateUserProfile,
    updateUserHandler: updateUserHandlerAsync,
  }
}
