import { QUERY_TRANSFER_RECEIVE } from '@/io/query-client'
import { useQuery } from '@tanstack/react-query'

import { Product } from '@/actions/product'
import { createTransferReceive } from '@/actions/transfer/create-transfer-receive'

export default function useTransfer(productId: Product['id'], auto = false) {
  const query = useQuery({
    queryKey: QUERY_TRANSFER_RECEIVE,
    queryFn: async () => {
      return createTransferReceive(productId)
    },
    // refetchInterval: 5000,
    enabled: Boolean(productId) && auto,
  })

  return {
    ...query,
  }
}
