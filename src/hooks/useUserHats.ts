import { useSession } from 'next-auth/react'
import { useMemo } from 'react'
import { UserHat } from '@/actions/user'
import useUser from './useUser'

export default function useUserHats() {
  const { data: session } = useSession()
  const userId = session?.user.id ? String(session.user.id) : ''
  const { data: user, isLoading } = useUser(userId)

  const hats = useMemo(() => {
    return user?.hats || []
  }, [user?.hats])

  const hatsSession = useMemo(() => {
    return session?.user?.hats || []
  }, [session?.user?.hats])

  const isAdmin = useMemo(() => {
    return hats.some((hat: UserHat) => hat.slug === 'admin')
  }, [hats])

  const isCreator = useMemo(() => {
    return hats.some((hat: UserHat) => hat.slug === 'creator')
  }, [hats])

  const isMember = useMemo(() => {
    return hats.some((hat: UserHat) => hat.slug === 'member')
  }, [hats])

  const isAdminSession = useMemo(() => {
    return hatsSession.some((hat: UserHat) => hat.slug === 'admin')
  }, [hatsSession])

  const isCreatorSession = useMemo(() => {
    return hatsSession.some((hat: UserHat) => hat.slug === 'creator')
  }, [hatsSession])

  return {
    hats,
    isAdmin,
    isCreator,
    isMember,
    isAdminSession,
    isCreatorSession,
    isLoading,
  }
}
