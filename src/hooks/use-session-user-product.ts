import { useMutation, useQuery } from '@tanstack/react-query'

import {
  deleteProductCascade,
  getSessionUserProductById,
  saveProduct,
} from '@/actions/product'
import { QUERY_PRODUCT } from '@/io/query-client'
import { FormSaveTemplateData } from '@/schemas'
import { disableProductCascade } from '@/actions/product/disableProductCascade'

type Product = Awaited<ReturnType<typeof getSessionUserProductById>>
type LocalQueryKey = [string, { productId: string }]

export default function useSessionUserProduct(productId?: string) {
  const queryKey = [...QUERY_PRODUCT, { productId }] as LocalQueryKey

  const query = useQuery<Product>({
    queryKey,
    queryFn: async () => {
      const { productId } = queryKey[1]
      if (productId !== undefined) {
        const product = await getSessionUserProductById(productId)
        if (product === undefined) {
          throw new Error('Product not found')
        }

        return product
      }
    },
    enabled: Boolean(productId),
  })

  const updateMutation = useMutation({
    mutationFn: async (productFormData: FormSaveTemplateData) => {
      const { productId } = queryKey[1]
      if (productId !== undefined) {
        const product = await saveProduct(productFormData, productId)
        if (product === undefined) {
          throw new Error('Product cannot be updated')
        }

        return product
      }
    },
    onSettled: () => {
      query.refetch()
    },
  })

  const deleteMutation = useMutation({
    mutationFn: async (productId: string) => {
      await disableProductCascade(productId)
    },
  })

  const updateProduct = async (productFormData: FormSaveTemplateData) =>
    await updateMutation.mutateAsync(productFormData)

  const deleteProduct = async (productId: string) =>
    await deleteMutation.mutateAsync(productId)

  const isLoading =
    query.isLoading ||
    updateMutation.status === 'pending' ||
    deleteMutation.status === 'pending'

  return { ...query, isLoading, updateProduct, deleteProduct }
}
