import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

import { QUERY_SKILLS, QUERY_USER_SKILLS, QUERY_USER } from '@/io/query-client'
import { getSkills } from '@/data/skill/get-skills'
import { updateUserSkills } from '@/data/user/update-user-skills'
import { type Skill, type SkillCategory } from '@/actions/skill'
import { type User } from '@/actions/user'

export function useSkills(category?: SkillCategory, enabled?: boolean) {
  return useQuery({
    queryKey: [...QUERY_SKILLS, { category, enabled }],
    queryFn: () => getSkills(category, enabled),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
  })
}

export function useSkillsByCategory(
  category: SkillCategory,
  enabled?: boolean,
) {
  return useQuery({
    queryKey: [...QUERY_SKILLS, { category, enabled }],
    queryFn: () => getSkills(category, enabled),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useUserSkills(userId: string) {
  const queryClient = useQueryClient()

  const updateSkillsMutation = useMutation({
    mutationFn: async (skillIds: string[]) => {
      if (!userId || userId === '') throw new Error('User ID is required')
      return updateUserSkills(userId, skillIds)
    },
    onSuccess: updatedUser => {
      // Invalidate and refetch user data
      queryClient.invalidateQueries({ queryKey: [...QUERY_USER, { userId }] })

      // Update user skills cache
      if (updatedUser) {
        queryClient.setQueryData(
          [...QUERY_USER_SKILLS, { userId }],
          updatedUser.profile?.skills || [],
        )

        // Also update the main user query data to ensure consistency
        queryClient.setQueryData([...QUERY_USER, { userId }], updatedUser)
      }
    },
    onError: error => {
      console.error('Error updating user skills:', error)
    },
  })

  return {
    updateSkills: updateSkillsMutation.mutateAsync,
    updateSkillsMutation,
    isLoading: updateSkillsMutation.isPending,
    error: updateSkillsMutation.error,
  }
}

// Hook to get skills for a specific user
export function useUserSkillsData(user: User | undefined) {
  const { data: allSkills = [] } = useSkills(undefined, true)

  const userSkills = user?.profile?.skills || []

  const selectedSkillsData = allSkills.filter(skill =>
    userSkills.includes(skill.id),
  )

  return {
    selectedSkills: userSkills,
    selectedSkillsData,
    allSkills,
  }
}
