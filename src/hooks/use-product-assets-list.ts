import React from 'react'
import { INFINITE_PRODUCTS } from '@/io/query-client'
import { useInfiniteQuery } from '@tanstack/react-query'
import { useInView } from 'react-intersection-observer'
import { getProductsAssetsByUser } from '@/data/product/get-products-assets-by-user'

const PAGE_SIZE = 12
const initialData = { pages: [], pageParams: [] }

export default function useProductAssetsList(userId: string) {
  const { ref, inView } = useInView()

  const query = useInfiniteQuery({
    queryKey: [...INFINITE_PRODUCTS, { userId, hook: 'useProductAssetsList' }],
    queryFn: ({ pageParam = 0 }) =>
      getProductsAssetsByUser(userId, PAGE_SIZE, PAGE_SIZE * pageParam),
    getNextPageParam: (lastPage, pages) =>
      lastPage?.length ? pages.length : undefined,
    initialPageParam: 0,
    initialData,
  })

  React.useEffect(() => {
    if (inView && query.hasNextPage) {
      query.fetchNextPage()
    }
  }, [inView, query, query.hasNextPage, query.fetchNextPage])

  return {
    ref,
    isInitial: !query.hasNextPage && !query.hasPreviousPage && query.isFetching,
    ...query,
  }
}
