import { useMutation, useQuery } from '@tanstack/react-query'

import { QUERY_TRANSFER } from '@/io/query-client'
import {
  getTransferById,
  setTransferStatus,
  TransferSendStatus,
} from '@/actions/transfer'

export default function useTransferById(transferId: string) {
  const query = useQuery({
    queryKey: [...QUERY_TRANSFER, { transferId }],
    queryFn: async () => {
      const transfer = await getTransferById(transferId)
      return transfer
    },
    initialData: null,
  })

  const status = useMutation({
    mutationFn: async ({ transferId, transferStatus }: any) =>
      setTransferStatus(transferId, transferStatus),
    onSuccess: () => {
      query.refetch()
    },
  })

  const updateTransferStatus = (
    transferId: string,
    transferStatus: TransferSendStatus,
  ) => status.mutate({ transferId, transferStatus })

  return {
    ...query,
    updateTransferStatus,
  }
}
