import React from 'react'
import { INFINITE_PRODUCTS } from '@/io/query-client'
import { useInfiniteQuery } from '@tanstack/react-query'
import { useInView } from 'react-intersection-observer'
import { getUserProducts, ProductStatus } from '@/actions/product'

const PAGE_SIZE = 12
const initialData = { pages: [], pageParams: [] }

export default function useProductList(userId: string, flair: string = '') {
  const { ref, inView } = useInView()

  const query = useInfiniteQuery({
    queryKey: [...INFINITE_PRODUCTS, { userId, flair }],
    queryFn: ({ pageParam = 0 }) =>
      getUserProducts(
        userId,
        flair,
        undefined,
        PAGE_SIZE,
        PAGE_SIZE * pageParam,
      ),
    getNextPageParam: (lastPage, pages) =>
      lastPage?.length ? pages.length : undefined,
    initialPageParam: 0,
    initialData,
  })

  React.useEffect(() => {
    if (inView && query.hasNextPage) {
      query.fetchNextPage()
    }
  }, [inView, query, query.hasNextPage, query.fetchNextPage])

  return {
    ref,
    isInitial: !query.hasNextPage && !query.hasPreviousPage && query.isFetching,
    ...query,
  }
}
