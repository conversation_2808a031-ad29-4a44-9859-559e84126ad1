import { usePathname } from 'next/navigation'
import { useMemo } from 'react'
import { usePageTitleContext } from '@/components/structure/PageTitleProvider'

const routeTitles: Record<string, string> = {
  '/': 'Home',
  '/explore': 'Explore',
  '/explore/templates': 'Templates',
  '/explore/components': 'Components',
  '/explore/bundles': 'Bundles',
  '/explore/subscriptions': 'Subscriptions',
  '/dashboard': 'Dashboard',
  '/dashboard/products': 'My Products',
  '/dashboard/earnings': 'Earnings',
  '/dashboard/purchases': 'My Purchases',
  '/user/settings': 'Settings',
  '/community': 'Community',
  '/notifications': 'Notifications',
  '/onboarding': 'Onboarding',
}

export function usePageTitle(customTitle?: string) {
  const pathname = usePathname()

  let contextTitle: string | null = null
  try {
    const context = usePageTitleContext()
    contextTitle = context.pageTitle
  } catch (err) {
    console.error(err)
  }

  const title = useMemo(() => {
    if (contextTitle) {
      return contextTitle
    }

    if (customTitle) {
      return customTitle
    }

    return routeTitles[pathname] || 'Templace'
  }, [pathname, customTitle, contextTitle])

  return title
}
