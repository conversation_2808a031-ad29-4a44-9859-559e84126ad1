import React from 'react'
import { INFINITE_PURCHASES } from '@/io/query-client'
import { useInfiniteQuery } from '@tanstack/react-query'
import { getBuyerPurchases } from '@/actions/user/getBuyerPurchases'
import { useInView } from 'react-intersection-observer'

const PAGE_SIZE = 12
const initialData = { pages: [], pageParams: [] }

const fetchItems =
  (userId: string, flair: string) =>
  async ({ pageParam }: any) => {
    if (pageParam !== undefined) {
      return getBuyerPurchases(userId, flair, PAGE_SIZE, PAGE_SIZE * pageParam)
    }
  }

export default function usePurchaseList(userId: string, kind: string = '') {
  const { ref, inView } = useInView()

  const query = useInfiniteQuery({
    queryKey: [...INFINITE_PURCHASES, { userId, kind }],
    queryFn: fetchItems(userId, kind),
    getNextPageParam: (lastPage, pages) =>
      (lastPage?.length ?? 0) >= PAGE_SIZE ? pages.length : undefined,
    initialPageParam: 0,
    initialData,
  })

  React.useEffect(() => {
    if (inView && query.hasNextPage) {
      query.fetchNextPage()
    }
  }, [inView, query, query.hasNextPage, query.fetchNextPage])

  return {
    ref,
    ...query,
  }
}
