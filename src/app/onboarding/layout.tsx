import { redirect } from 'next/navigation'

import { getFullAuthSession } from '@/io/next-auth-config'
import { HOST } from '@/config'

import '../globals.css'
import { ReactNode } from 'react'
import TemplaceLogo from '@/components/common/TemplaceLogo'
import StepIndicator from '@/components/onboarding/StepIndicator'

export const metadata = {
  title: 'tem.place - Onboard',
}

export default async function OnboardLayout({
  children,
}: {
  children: ReactNode
}) {
  const session = await getFullAuthSession()
  return !session?.user ? (
    redirect(`${HOST}/login`)
  ) : (
    <div className="min-h-screen flex flex-col items-center justify-center bg-white px-4 py-8 sm:py-12">
      <div className="max-w-md w-full space-y-8 mb-8">
        <div className="flex justify-center">
          <TemplaceLogo className="w-16 h-16" />
        </div>
        <StepIndicator />
      </div>
      {children}
    </div>
  )
}
