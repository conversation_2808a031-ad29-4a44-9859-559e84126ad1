'use client'

import { useRef, useState } from 'react'
import Wrapper<PERSON>hat<PERSON><PERSON>YouHere, {
  WrapperWhatBringsYouHereRef,
} from '@/components/onboarding/WrapperWhatBringsYouHere'
import { Button } from '@/components/ui/button'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

export default function WhatBringsYouHerePage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const wrapperRef = useRef<WrapperWhatBringsYouHereRef>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [hasSelection, setHasSelection] = useState(false)

  useEffect(() => {
    if (status === 'loading') return

    if (!session || !session.providerId) {
      router.push('/login')
    }
  }, [session, status, router])

  // Callback to update selection state
  const onSelectionChange = (selectedId: string | null) => {
    setHasSelection(selectedId !== null)
  }

  const handleContinue = async () => {
    if (wrapperRef.current && wrapperRef.current.hasSelection) {
      setIsLoading(true)
      await wrapperRef.current.continue()
      setIsLoading(false)
    }
  }

  if (status === 'loading') {
    return <div>Loading...</div>
  }

  if (!session || !session.providerId) {
    return null
  }

  return (
    <div className="flex flex-col items-center justify-center gap-8">
      <WrapperWhatBringsYouHere
        ref={wrapperRef}
        userId={session?.user?.id}
        onSelectionChange={onSelectionChange}
      />
      <Button
        variant="primary"
        onClick={handleContinue}
        disabled={isLoading || !hasSelection}
      >
        {isLoading ? 'Loading...' : 'Continue'}
      </Button>
    </div>
  )
}
