import CreateProfileForm from '@/components/onboarding/CreateProfileForm'
import { getFullAuthSession } from '@/io/next-auth-config'
import { getUserById } from '@/actions/user/getUserById'
import { redirect } from 'next/navigation'

export default async function OnboardingPage() {
  const session = await getFullAuthSession()

  if (!session || !session.providerId) {
    return redirect('/login')
  }

  const userData = await getUserById(session.user.id)

  return (
    <div className="max-w-md w-full space-y-8">
      <h1 className="text-2xl font-semibold text-gray-900 mb-2 text-center">
        Create your profile
      </h1>
      <CreateProfileForm userId={session.user.id} userData={userData} />
    </div>
  )
}
