import { format } from 'date-fns'
import Link from 'next/link'

import { getPaymentLogById } from '@/actions/payment'
import Header from '@/components/common/Header'
import TemplaceLogo from '@/components/common/TemplaceLogo'
import TextLabel from '@/components/common/TextLabel'
import UserBox from '@/components/common/UserBox'
import Footer from '@/components/internal/Footer'
import PurchaseCompleteAnimation from '@/components/internal/PurchaseCompleteAnimation'
import PlatformWidget from '@/components/products/PlatformWidget'
import LeanWrapper from '@/components/structure/LeanWrapper'
import Main from '@/components/structure/Main'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import buildName from '@/utils/buildName'
import { getPlatform } from '@/utils/get-platform'
import { check } from '@/utils/jwt'

export default async function PaymentCompletePage({ searchParams }: any) {
  const { state: searchParamsState } = await searchParams
  const state = await check(searchParamsState)
  const paymentLog = await getPaymentLogById(state.id)

  const figma = paymentLog?.product?.fileSet?.files.find(file =>
    file.flair?.includes('figma'),
  )

  return (
    <Main className="bg-gray-50 text-gray-900">
      <Header className="items-center">
        <h1>
          <Link href="/">
            <TemplaceLogo full />
          </Link>
        </h1>
        <div className="flex items-center gap-4 justify-between">
          <UserBox />
        </div>
      </Header>

      <LeanWrapper>
        <section className="h-fit bg-background p-16 pt-2 mt-8 ring-1 ring-gray-200 rounded-md relative items-center">
          <PurchaseCompleteAnimation />
          <h2 className="text-5xl font-bold py-16 z-10 relative">
            You&apos;ve redeemed <br />
            an asset!
          </h2>

          <div className="grid lg:grid-cols-2 gap-6">
            <TextLabel title="Confirmation">{state.hashId}</TextLabel>

            <TextLabel title="Date">
              {format(paymentLog.created, 'PPPPp')}
            </TextLabel>

            {paymentLog?.buyer?.email && (
              <TextLabel title="Email">{paymentLog?.buyer?.email}</TextLabel>
            )}

            {paymentLog?.product?.name && (
              <TextLabel title="Asset">{paymentLog?.product?.name}</TextLabel>
            )}

            <TextLabel title="Total">
              <Badge variant="green" size="md">
                Free
              </Badge>
            </TextLabel>

            <TextLabel title="Platform">
              <PlatformWidget
                platform={getPlatform(paymentLog.product?.platforms)}
              />
            </TextLabel>

            <TextLabel title="Member">
              <span>
                {buildName(
                  paymentLog?.seller?.profile?.firstName,
                  paymentLog?.seller?.profile?.lastName,
                )}
              </span>
              <br />
              {paymentLog?.seller?.handler && (
                <Link href={`/@${paymentLog?.seller?.handler}`}>
                  @{paymentLog?.seller?.handler}
                </Link>
              )}
            </TextLabel>

            <TextLabel title="Figma Included">
              {figma ? (
                <Badge variant="green" size="md">
                  Included
                </Badge>
              ) : (
                <Badge variant="secondary" size="md">
                  Not Included
                </Badge>
              )}
            </TextLabel>
          </div>
          <hr className="my-8" />

          <div className="flex gap-4 pt-8 items-center justify-center">
            <Button variant="ghost">
              <Link type="button" href="/dashboard/purchases">
                Go to my Dashboard
              </Link>
            </Button>
          </div>
        </section>

        <div className="flex gap-4 mt-8 justify-center mb-16">
          {state.externalState?.redirect ? (
            <Button variant="secondary" asChild>
              <Link type="button" href={state.externalState.redirect}>
                Go back to original site
              </Link>
            </Button>
          ) : null}

          <Button variant="ghost" asChild>
            <Link type="button" href="mailto:<EMAIL>">
              Need help?
            </Link>
          </Button>
        </div>
      </LeanWrapper>
      <Footer />
    </Main>
  )
}
