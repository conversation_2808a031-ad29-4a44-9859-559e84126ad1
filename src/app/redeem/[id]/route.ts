import { redirect, RedirectType } from 'next/navigation'
import { NextRequest, NextResponse } from 'next/server'
import { sendRedeemComplete, sendRedeemSeller } from '@/actions/mailing'
import {
  checkIfUserBoughtAlready,
  continuePaymentLog,
  startPaymentLog,
} from '@/actions/payment'
import { getProductById, ProductStatus } from '@/actions/product'
import { getUserById } from '@/actions/user'
import { HOST } from '@/config'
import { getFullAuthSession } from '@/io/next-auth-config'
import { getIdFromRecord } from '@/utils'
import { check, set } from '@/utils/jwt'
import logger from '@/utils/logger'

type Params = {
  params: Promise<{ id: string }>
}

/**
 * Listens
 */
export async function GET(request: NextRequest, { params }: Params) {
  const { id } = await params
  const session = await getFullAuthSession()
  if (!session?.user?.id) {
    logger.trace(`User not logged in: <${session?.user?.id}>`)
    return redirect(`${HOST}/login?redirect=${request.url}`)
  }

  const user = await getUserById(session.user.id)
  if (!user) {
    logger.trace(`User id not in sync: <${session?.user?.id}>`)
    return redirect(`${HOST}/login?redirect=${request.url}`)
  }

  const url = new URL(request.url)
  const urlExternalState = url.searchParams.get('externalState')
  const externalState = await check(urlExternalState)

  if (id !== externalState.productId) {
    return new NextResponse('The redemption link is invalid or has expired', {
      status: 403,
    })
  }

  const product = await getProductById(
    externalState.productId,
    ProductStatus.PUBLISHED,
  )
  if (product?.price.amount !== 0) {
    return new NextResponse('This product is not currently free', {
      status: 403,
    })
  }

  const alreadyGot = await checkIfUserBoughtAlready(
    session.user.id,
    product?.id ?? '',
  )

  if (alreadyGot) {
    logger.trace(
      `User already redeemed, <${getIdFromRecord(session?.user?.id)}> userId, <${getIdFromRecord(product?.id)}> productId`,
    )

    return redirect(`${HOST}/dashboard/purchases`, RedirectType.replace)
  }
  /** Login verification ends */

  /** Starting checkout */
  const paymentLog = await startPaymentLog(
    product?.owner?.id.toString() ?? '',
    session.user.id,
    product?.id ?? '',
    {
      productId: product?.id ?? '',
      buyerUserId: session.user.id,
      linkState: externalState ?? '',
    },
  )

  const completedPaymentLog = await continuePaymentLog(
    paymentLog.id.toString(),
    {
      sessionId: session.id,
      externalState,
      custormer: session.user,
    },
  )

  externalState.id = completedPaymentLog?.id.toString()
  externalState.hashId = completedPaymentLog?.hashId ?? ''

  await sendRedeemSeller(completedPaymentLog)

  await sendRedeemComplete(completedPaymentLog)

  // redeem complete, redirecting
  const state = await set(externalState)
  const redeemCompleteUrl = `${HOST}/redeem/complete?state=${state}`
  return NextResponse.redirect(redeemCompleteUrl)
}
