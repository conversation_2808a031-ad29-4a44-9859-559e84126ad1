import { redirect } from 'next/navigation'
import { getFullAuthSession } from '@/io/next-auth-config'
import { UserOnboard } from '@/actions/user'

export default async function PostLoginRedirectPage() {
  const session = await getFullAuthSession()

  if (!session || !session.providerId) {
    return redirect('/login')
  }

  if (session?.user?.onboardStatus === UserOnboard.ONBOARDED) {
    return redirect('/')
  }

  return redirect('/onboarding/step/1')
}
