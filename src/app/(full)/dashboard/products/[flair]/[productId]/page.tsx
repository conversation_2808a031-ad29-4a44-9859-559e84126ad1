import FormSaveBundle from '@/components/form/FormSaveBundle'
import FormSaveTemplate from '@/components/form/FormSaveTemplate'

export default async function Page({ params }: any) {
  const { flair, productId } = await params

  const urlToFlair: Record<string, string> = {
    templates: 'template',
    components: 'component',
  }

  return (
    <>
      {(flair === 'templates' || flair === 'components') && (
        <FormSaveTemplate
          productId={productId}
          flair={[...(urlToFlair?.[flair] ? [urlToFlair[flair]] : [])]}
        />
      )}
      {flair === 'bundles' && (
        <FormSaveBundle productId={productId} flair={['bundle']} />
      )}
    </>
  )
}
