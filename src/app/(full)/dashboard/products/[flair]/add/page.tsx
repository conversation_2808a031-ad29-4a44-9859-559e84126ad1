import { redirect } from 'next/navigation'
import { getSessionUser } from '@/actions/auth'
import { bootstrapProduct } from '@/actions/product'
import { getUserById } from '@/actions/user/getUserById'
import { getIdFromRecord } from '@/utils'

const flairSingular: Record<string, string> = {
  templates: 'template',
  components: 'component',
  bundles: 'bundle',
  subscriptions: 'subscription',
}

export default async function Page({ params }: any) {
  const sessionUser = await getSessionUser()
  const { flair } = await params

  // Get complete user data including permissions
  const user = await getUserById(getIdFromRecord(sessionUser.id))

  if (!user) {
    redirect('/404')
  }

  // Check if user is admin or creator
  const isAdmin = user.hats?.some(hat => hat.slug === 'admin')
  const isCreator = user.hats?.some(hat => hat.slug === 'creator')

  if (!isAdmin && !isCreator) {
    redirect('/404')
  }

  // If user is creator (not admin), check canAddContent permission
  if (isCreator && !isAdmin) {
    const canAddContent = user.profile?.permissions?.canAddContent ?? false
    if (!canAddContent) {
      redirect('/dashboard/products?error=no-permission')
    }
  }

  const newProduct = await bootstrapProduct(user.id, flairSingular[flair])

  if (newProduct) {
    redirect(`/dashboard/products/${flair}/${getIdFromRecord(newProduct?.id)}`)
  } else {
    throw new Error('Error bootstraping a product')
  }

  return null
}
