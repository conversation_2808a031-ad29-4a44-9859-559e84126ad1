import Link from 'next/link'
import Image from 'next/image'

// import { cn } from '@/lib/utils'
import GoogleLoginButton from '@/components/common/GoogleLoginButton'
import TemplaceLogo from '@/components/common/TemplaceLogo'
import EmailLoginForm from '@/components/auth/EmailLoginForm'
// import OnboardWrapper from '@/components/OnboardWrapper'
// import AppleLoginButton from "@/components/AppleLoginButton";

type Props = {
  searchParams: Promise<Record<string, string>>
}

export default async function LoginPage({ searchParams }: Props) {
  const { redirect } = await searchParams
  return (
    <main className="flex w-full h-screen">
      <div className="h-full flex flex-col items-center justify-center mx-auto container max-w-sm gap-y-8">
        <TemplaceLogo className="w-16 h-16" />

        <h1 className="text-2xl font-semibold text-center leading-[1.3] text-gray-950">
          Log in or sign up
        </h1>

        <div className="flex flex-col gap-2 w-full">
          <GoogleLoginButton
            className="bg-white text-black text-sm gap-4 border border-neutral-300 w-full hover:bg-white hover:brightness-95"
            redirect={redirect ?? '/'}
          />
        </div>

        <span className="text-gray-500 text-sm">or</span>

        <div className="w-full">
          <EmailLoginForm />
        </div>

        {redirect ? (
          <span className="w-1/2 text-center text-gray-600 text-sm">
            You will be redirected after login.
          </span>
        ) : null}

        <small className="text-center text-gray-400">
          By signing up, you are creating an account and agree to our{' '}
          <Link
            href="/terms"
            className="text-center text-gray-500 underline hover:text-gray-600"
          >
            Terms and Privacy Policy
          </Link>
          .
        </small>
      </div>
    </main>
  )
}
