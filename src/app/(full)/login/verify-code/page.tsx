'use client'

import TemplaceLogo from '@/components/common/TemplaceLogo'
import Link from 'next/link'
import VerifyCodeForm from '@/components/auth/VerifyCodeForm'
import { useSearchParams } from 'next/navigation'

export default function VerifyCodePage() {
  const searchParams = useSearchParams()
  const userEmail = searchParams.get('email') || ''

  if (!userEmail) {
    return (
      <main className="flex w-full h-screen">
        <div className="h-full flex flex-col items-center justify-center mx-auto container max-w-sm gap-y-8">
          <TemplaceLogo className="w-16 h-16" />
          <h1 className="text-2xl font-semibold text-center leading-[1.3] text-gray-950">
            Email not provided
          </h1>
          <Link href="/login" className="text-blue-600 hover:underline">
            Back to login
          </Link>
        </div>
      </main>
    )
  }

  return (
    <main className="flex w-full h-screen">
      <div className="h-full flex flex-col items-center justify-center mx-auto container max-w-sm gap-y-8">
        <TemplaceLogo className="w-16 h-16" />

        <h1 className="text-2xl font-semibold text-center leading-[1.3] text-gray-950">
          Log in or sign up
        </h1>

        <div className="flex flex-col gap-2 justify-center text-center">
          <span className="text-gray-500 text-sm">
            We sent a code to: <strong>{userEmail}</strong>
          </span>

          <Link className="w-fit mx-auto text-sm text-blue-600" href="/login">
            Change
          </Link>
        </div>

        <div className="w-full">
          <VerifyCodeForm userEmail={userEmail} />
        </div>

        <small className="text-center text-gray-400">
          By signing up, you are creating an account and agree to our{' '}
          <Link
            href="/terms"
            className="text-center text-gray-500 underline hover:text-gray-600"
          >
            Terms and Privacy Policy
          </Link>
          .
        </small>
      </div>
    </main>
  )
}
