import Link from 'next/link'

import { But<PERSON> } from '@/components/ui/button'
import Main from '@/components/structure/Main'
import TemplaceLogo from '@/components/common/TemplaceLogo'

export default async function LoginError() {
  return (
    <Main>
      <div className="flex flex-col items-center justify-center gap-4">
        <TemplaceLogo className="w-10 h-10" />
        <h1 className="text-[2.6rem] font-semibold text-center leading-[1.3] text-gray-950">
          Login Error
        </h1>
      </div>
      <Button variant="ghost" asChild>
        <Link href="/">Back to Homepage</Link>
      </Button>
    </Main>
  )
}
