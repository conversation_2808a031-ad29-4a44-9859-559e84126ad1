import { Button } from '@/components/ui/button'
import { format } from 'date-fns'
import Link from 'next/link'

import { getPaymentLogById } from '@/actions/payment'
import { PaymentPrice } from '@/actions/price'
import Header from '@/components/common/Header'
import LeanWrapper from '@/components/structure/LeanWrapper'
import TemplaceLogo from '@/components/common/TemplaceLogo'
import TextLabel from '@/components/common/TextLabel'
import UserBox from '@/components/common/UserBox'
import Footer from '@/components/internal/Footer'
import PaymentCompleteMessage from '@/components/internal/PaymentCompleteMessage'
import PurchaseCompleteAnimation from '@/components/internal/PurchaseCompleteAnimation'
import PlatformWidget from '@/components/products/PlatformWidget'
import Main from '@/components/structure/Main'
import { getPlatform } from '@/utils/get-platform'
import { check } from '@/utils/jwt'

export default async function PaymentCompletePage({ searchParams }: any) {
  const { state: searchParamsState } = await searchParams
  const state = await check(searchParamsState)
  const paymentLog = await getPaymentLogById(state.id)

  return (
    <Main>
      <Header className="items-center">
        <h1>
          <Link href="/">
            <TemplaceLogo full />
          </Link>
        </h1>
        <div className="flex items-center gap-4 justify-between">
          <UserBox />
        </div>
      </Header>

      <LeanWrapper>
        <section className="h-fit bg-background p-16 pt-2 mt-8 ring-1 ring-gray-200 rounded-md relative items-center">
          <PurchaseCompleteAnimation />
          <h2 className="text-5xl font-bold py-16 z-10 relative">
            Thanks for your purchase
          </h2>

          <div className="grid lg:grid-cols-2">
            <div className="flex flex-col gap-6">
              <TextLabel title="Confirmation">{state.hashId}</TextLabel>
              <TextLabel title="Email">{paymentLog?.buyer?.email}</TextLabel>
              <TextLabel title="Total">
                {new Intl.NumberFormat('en-US', {
                  style: 'currency',
                  currency: (paymentLog.product.price as PaymentPrice)
                    .currencyCode,
                }).format(
                  Number((paymentLog.product.price as PaymentPrice).amount) /
                    100,
                )}
              </TextLabel>
              <TextLabel title="Member">
                <span>{paymentLog?.seller?.name}</span>
                <br />
                <Link href={`/@${paymentLog.seller.handler}`}>
                  @{paymentLog.seller.handler}
                </Link>
              </TextLabel>
            </div>
            <div className="flex flex-col gap-6">
              <TextLabel title="Date">
                {format(paymentLog.created, 'PPPPp')}
              </TextLabel>
              <TextLabel title="Asset">{paymentLog.product.name}</TextLabel>
              <TextLabel title="Platform">
                <PlatformWidget
                  platform={getPlatform(paymentLog.product?.platforms)}
                />
              </TextLabel>
              <TextLabel title="Figma Included">
                <em>Coming Soon</em>
              </TextLabel>
            </div>
          </div>
          <hr className="my-8" />

          <PaymentCompleteMessage
            platform={getPlatform(paymentLog.product?.platforms)}
          />

          {/* {console.log('============', paymentLog) ?? null} */}

          {/* {paymentLog?.transfers?.length > 0
            ? paymentLog?.transfers.map((transfer: any) => (
                <PaymentCompleteActions
                  key={transfer.id}
                  transferId={transfer.id}
                  platform={getPlatform(paymentLog.product?.platforms)}
                  redirect={transfer.redirect}
                />
              ))
            : null} */}

          <div className="flex gap-4 pt-8 items-center justify-center">
            <Button variant="outline" asChild>
              <Link type="button" href="/dashboard/purchases">
                Go to my Dashboard
              </Link>
            </Button>
          </div>
        </section>

        <div className="flex gap-4 mt-8 justify-center mb-16">
          {state.externalState?.redirect ? (
            <Button variant="secondary" asChild>
              <Link type="button" href={state.externalState.redirect}>
                Go back to original site
              </Link>
            </Button>
          ) : null}

          <Button variant="ghost" asChild>
            <Link type="button" href="mailto:<EMAIL>">
              Need help?
            </Link>
          </Button>
        </div>
      </LeanWrapper>
      <Footer />
    </Main>
  )
}
