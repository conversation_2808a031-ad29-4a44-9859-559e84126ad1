import { Metadata } from 'next'
import { ProductStatus } from '@/actions/product'
import { getProductByIdOrSlug } from '@/actions/product'
import { HOST } from '@/config'

type GenerateMetadataProps = {
  params: Promise<{ productIdorSlug: string }>
}

export async function generateMetadata({
  params,
}: GenerateMetadataProps): Promise<Metadata> {
  const { productIdorSlug } = await params
  const product = await getProductByIdOrSlug(
    productIdorSlug,
    ProductStatus.PUBLISHED,
  )
  const productName = product?.name ?? 'Product Name'
  const title = `${productName} | Templace`
  const description = product?.description ?? 'Product Description'
  const images = product?.imageSet?.images?.map(item => ({
    url: item?.url ?? `/thumb.jpeg`,
    width: 800,
    height: 600,
    alt: `${productName} Image, ${item.name}`,
  }))

  return {
    title,
    description,
    metadataBase: new URL(HOST!),
    alternates: {
      canonical: `${HOST}/product/${productIdorSlug}`,
      languages: {
        'en-US': `${HOST}/product/${productIdorSlug}`,
      },
    },
    openGraph: {
      title,
      description,
      url: `${HOST}/product/${productIdorSlug}`,
      siteName: title,
      images,
      locale: 'en_US',
      type: 'article',
    },
    icons: {
      icon: '/favicon.ico',
    },
  }
}

type Props = React.PropsWithChildren

export default async function ProductLayout({ children }: Props) {
  return <>{children}</>
}
