import {
  PlatformsEnum,
  ProductPlatformsKeys,
  getProductByIdOrSlug,
} from '@/actions/product'
import ProductGallery from '@/components/common/ProductGallery'
import ProductSidebar from '@/components/common/ProductSidebar'
import BundleAndSubscriptionGrid from '@/components/products/BundleAndSubscriptionGrid'
import ProductPreview from '@/components/products/ProductPreview'
import ProductHeader from '@/components/products/ProductPublicHeader'
import { getPlatform } from '@/utils/get-platform'

export default async function ProductPage({ params }: any) {
  const { productIdorSlug } = await params
  const product = await getProductByIdOrSlug(productIdorSlug)
  if (!product) {
    return (
      <div className="text-center p-4">
        <p className="text-lg font-semibold">Product not found</p>
      </div>
    )
  }

  const platform = getPlatform(product.platforms) as ProductPlatformsKeys

  const hasEmbedPreview =
    platform &&
    (platform === 'framer' ||
      platform === 'vzero' ||
      platform === 'loveable' ||
      platform === 'bolt') &&
    product.platforms?.[platform]?.embedPreview !== undefined

  return (
    <>
      <ProductHeader user={product.owner} />

      <div className="flex flex-col md:flex-row w-full">
        <div className="w-full md:w-3/4 p-4">
          {hasEmbedPreview && <ProductPreview product={product} />}
          {platform === PlatformsEnum.BUNDLE ||
          platform === PlatformsEnum.SUBSCRIPTION ? (
            <BundleAndSubscriptionGrid bundleOrSubscription={product} />
          ) : (
            <ProductGallery
              imageSet={product.imageSet}
              fileSet={product.fileSet}
            />
          )}
        </div>

        <div className="w-full md:w-1/4 p-4 border-l">
          <ProductSidebar product={product} />
        </div>
      </div>
    </>
  )
}
