'use client'

import { useEffect } from 'react'

type Props = {
  error: Error & { digest?: string }
  reset: () => void
}

export default function Error({ error, reset }: Props) {
  useEffect(() => {
    // Log the error to an error reporting service
    // console.error(error)
  }, [error])

  return (
    <section>
      <div className="flex justify-center items-center min-h-[66vh] text-gray-300 text-7xl font-mono">
        404 - Not Found
      </div>
    </section>
  )
}
