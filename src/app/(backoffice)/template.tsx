import { headers } from 'next/headers'
import Loading from '@/components/common/Loading'
import Modals from '@/components/common/Modals'
import BackofficeNav from '@/components/backoffice/BackofficeNav'
import HybridBackofficeBreadcrumbs from '@/components/backoffice/HybridBackofficeBreadcrumbs'
import { getBreadcrumbsFromPath } from '@/utils/breadcrumbs'
import { HEADER_PATHNAME } from '@/config'
import Wrapper from '@/components/structure/Wrapper'
import Main from '@/components/structure/Main'

export default async function Template({ children }: React.PropsWithChildren) {
  const headersList = await headers()
  const pathname = headersList.get(HEADER_PATHNAME) || '/backoffice'

  const serverBreadcrumbs = getBreadcrumbsFromPath(pathname)

  return (
    <Wrapper>
      <Main className="container mx-auto md:pl-[4rem]">
        <BackofficeNav>
          <HybridBackofficeBreadcrumbs serverBreadcrumbs={serverBreadcrumbs} />
        </BackofficeNav>

        {children}
      </Main>
      <Modals />
      <Loading />
    </Wrapper>
  )
}
