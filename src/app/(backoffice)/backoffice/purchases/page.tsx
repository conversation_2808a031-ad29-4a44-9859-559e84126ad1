import BackofficeBreadcrumbs from '@/components/backoffice/BackofficeBreadcrumbs'
import BackofficeHero from '@/components/backoffice/BackofficeHero'
import BackofficeNav from '@/components/backoffice/BackofficeNav'
import Container from '@/components/backoffice/Container'
import ColSeventy from '@/components/backoffice/ColSeventy'
import ColThirty from '@/components/backoffice/ColThirty'
import Full from '@/components/structure/Full'

export default async function PurchasesBackofficePage() {
  return (
    <Full>
      <Container>
        <ColThirty>
          <BackofficeHero
            title="Purchases"
            description="These are all the purchases made in the platform."
          />
        </ColThirty>

        <ColSeventy>
          <></>
        </ColSeventy>
      </Container>

      <Container>
        <ColThirty>
          <></>
        </ColThirty>

        <ColSeventy>
          <div className="w-full h-full grid place-items-center">
            <span className="text-1xl text-muted-foreground py-4">
              Purchases component coming soon.
            </span>
          </div>
        </ColSeventy>
      </Container>
    </Full>
  )
}
