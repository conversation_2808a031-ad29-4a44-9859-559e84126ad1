import BackofficeHero from '@/components/backoffice/BackofficeHero'
import BackofficePage from '@/components/backoffice/BackofficePage'
import ColThirty from '@/components/backoffice/ColThirty'
import Container from '@/components/backoffice/Container'
import BackofficeTransactionLogs from '@/components/backoffice/BackofficeTransactionLogs'
import { getTransactionStats } from '@/actions/payment/get-transaction-stats'
import centsToDollars from '@/utils/cents-to-dollars'

type Props = {
  searchParams: Promise<{
    page?: string
    sortBy?: string
    sortOrder?: string
  }>
}

export default async function BackofficeLogsPage({ searchParams }: Props) {
  const params = await searchParams
  const page = Number(params.page) || 1
  const sortBy = params.sortBy || 'created'
  const sortOrder = (params.sortOrder || 'desc') as 'asc' | 'desc'

  const stats = await getTransactionStats()

  return (
    <BackofficePage>
      <Container>
        <ColThirty>
          <BackofficeHero
            title="Transaction Logs"
            description="View all purchase and sale logs from the marketplace"
          />
        </ColThirty>
      </Container>
      <div className="w-full px-4">
        {/* Stats or filters could go here */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="bg-muted/50 rounded-lg p-4">
            <h3 className="text-sm font-medium text-muted-foreground">
              Total Transactions
            </h3>
            <p className="text-2xl font-bold">
              {stats.totalTransactions.toLocaleString()}
            </p>
          </div>
          <div className="bg-muted/50 rounded-lg p-4">
            <h3 className="text-sm font-medium text-muted-foreground">
              Today&apos;s Volume
            </h3>
            <p className="text-2xl font-bold">
              {centsToDollars(stats.todayVolume, stats.currency)}
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              {stats.todayTransactions} transaction
              {stats.todayTransactions !== 1 ? 's' : ''} today
            </p>
          </div>
        </div>
      </div>
      <Container>
        <BackofficeTransactionLogs
          page={page}
          sortBy={sortBy}
          sortOrder={sortOrder}
        />
      </Container>
    </BackofficePage>
  )
}
