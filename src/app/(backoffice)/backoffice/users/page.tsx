import BackofficeHero from '@/components/backoffice/BackofficeHero'
import BackofficePage from '@/components/backoffice/BackofficePage'
import ColThirty from '@/components/backoffice/ColThirty'
import Container from '@/components/backoffice/Container'
import ListUsers from '@/components/backoffice/ListUsers'
import UsersBlocks from '@/components/backoffice/UsersBlocks'

interface UsersBackofficePageProps {
  searchParams: Promise<{
    page?: string
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  }>
}

export default async function UsersBackofficePage({
  searchParams,
}: UsersBackofficePageProps) {
  const searchParamsObj = await searchParams
  const page = Number(searchParamsObj.page) || 1
  const sortBy = searchParamsObj.sortBy || 'created'
  const sortOrder = searchParamsObj.sortOrder || 'desc'

  return (
    <BackofficePage>
      <Container>
        <ColThirty>
          <BackofficeHero
            title="Users"
            description="Manage all users, access profiles"
          />
        </ColThirty>
      </Container>
      <div className=" p-4 w-full">
        <UsersBlocks className="bg-muted/50 rounded-lg flex flex-col items-start justify-center p-4" />
      </div>

      <Container>
        <ListUsers page={page} sortBy={sortBy} sortOrder={sortOrder} />
      </Container>
    </BackofficePage>
  )
}
