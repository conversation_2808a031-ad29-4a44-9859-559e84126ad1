import BackofficePage from '@/components/backoffice/BackofficePage'
import Container from '@/components/backoffice/Container'
import ColThirty from '@/components/backoffice/ColThirty'
import ColSeventy from '@/components/backoffice/ColSeventy'
import BackofficeHero from '@/components/backoffice/BackofficeHero'
import ClippboardClient from '@/components/common/ClippboardClient'
import ShowReceive from '@/components/backoffice/ShowReceive'

interface Props {
  params: Promise<{ id: string }>
}

export default async function BackofficeReceivePage({ params }: Props) {
  const { id } = await params

  return (
    <BackofficePage>
      <Container>
        <ColThirty>
          <BackofficeHero
            title="Receive"
            description="Detailed information about this TransferReceive"
          />
          <ClippboardClient valueToCopy={id} className="font-mono text-lg">
            <small className="font-mono text-lg">{id}</small>
          </ClippboardClient>
        </ColThirty>

        <ColSeventy>
          <ShowReceive id={id} />
        </ColSeventy>
      </Container>
    </BackofficePage>
  )
}
