import BackofficeHero from '@/components/backoffice/BackofficeHero'
import BackofficePage from '@/components/backoffice/BackofficePage'
import ColSeventy from '@/components/backoffice/ColSeventy'
import ColThirty from '@/components/backoffice/ColThirty'
import Container from '@/components/backoffice/Container'
import BackofficeProducts from '@/components/backoffice/BackofficeProducts'

interface ProductsBackofficePageProps {
  searchParams: Promise<{
    page?: string
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  }>
}

export default async function ProductsBackofficePage({
  searchParams,
}: ProductsBackofficePageProps) {
  const searchParamsObj = await searchParams
  const page = Number(searchParamsObj.page) || 1
  const sortBy = searchParamsObj.sortBy || 'created'
  const sortOrder = searchParamsObj.sortOrder || 'desc'

  return (
    <BackofficePage>
      <Container>
        <ColThirty>
          <BackofficeHero
            title="Products"
            description="These are all the products offered in the platform"
          />
        </ColThirty>

        <ColSeventy>
          {/* We can add statistics blocks here in the future */}
        </ColSeventy>
      </Container>

      <Container>
        <BackofficeProducts page={page} sortBy={sortBy} sortOrder={sortOrder} />
      </Container>
    </BackofficePage>
  )
}
