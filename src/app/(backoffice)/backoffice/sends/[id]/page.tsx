import BackofficePage from '@/components/backoffice/BackofficePage'
import Container from '@/components/backoffice/Container'
import ColThirty from '@/components/backoffice/ColThirty'
import ColSeventy from '@/components/backoffice/ColSeventy'
import BackofficeHero from '@/components/backoffice/BackofficeHero'
import ClippboardClient from '@/components/common/ClippboardClient'
import ShowSend from '@/components/backoffice/ShowSend'

interface Props {
  params: Promise<{ id: string }>
}

export default async function BackofficeSendPage({ params }: Props) {
  const { id } = await params

  return (
    <BackofficePage>
      <Container>
        <ColThirty>
          <BackofficeHero
            title="Send"
            description="Detailed information about this TransferSend"
          />
          <ClippboardClient valueToCopy={id} className="font-mono text-lg">
            <small className="font-mono text-lg">{id}</small>
          </ClippboardClient>
        </ColThirty>

        <ColSeventy>
          <ShowSend id={id} />
        </ColSeventy>
      </Container>
    </BackofficePage>
  )
}
