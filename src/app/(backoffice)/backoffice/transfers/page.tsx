import BackofficeCompleteTransfers from '@/components/backoffice/BackofficeCompleteTransfers'
import BackofficeHero from '@/components/backoffice/BackofficeHero'
import BackofficeOpenTransfers from '@/components/backoffice/BackofficeOpenTransfers'
import BackofficePage from '@/components/backoffice/BackofficePage'
import ColSeventy from '@/components/backoffice/ColSeventy'
import ColThirty from '@/components/backoffice/ColThirty'
import Container from '@/components/backoffice/Container'
import TransfersBlocks from '@/components/backoffice/TransfersBlocks'
import { cn } from '@/lib/utils'

export default async function TransfersBackofficePage() {
  return (
    <BackofficePage>
      <Container>
        <ColThirty>
          <BackofficeHero
            title="Transfers"
            description="Manage transfers and their status"
          />
        </ColThirty>

        <ColSeventy>
          <TransfersBlocks />
        </ColSeventy>
      </Container>

      <Container>
        <BackofficeOpenTransfers />
      </Container>

      <Container>
        <BackofficeCompleteTransfers />
      </Container>
    </BackofficePage>
  )
}
