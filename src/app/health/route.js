import { NextResponse } from 'next/server'
import { Surrealized } from '@/io/surreal'
import logger from '@/utils/logger'
import redis from '@/io/ioredis'

export const dynamic = 'force-dynamic'

export async function GET() {
  const surreal = await Surrealized()
  const surrealStatus = await surreal.query(`
    INFO FOR DB;
    INFO FOR NS;
  `)
  logger.trace(`surreal connection: ${!!surrealStatus}`)
  logger.trace(`surreal status: ${JSON.stringify(surrealStatus, null, 2)}`)

  const redisStatus = await redis.info()
  logger.trace(`redis connection: ${!!redisStatus}`)
  logger.trace(`redis status: ${JSON.stringify(redisStatus)}`)

  return new NextResponse(
    JSON.stringify({
      surreal: !!surrealStatus,
      redis: !!redisStatus,
    }),
    {
      status: 200,
    },
  )
}
