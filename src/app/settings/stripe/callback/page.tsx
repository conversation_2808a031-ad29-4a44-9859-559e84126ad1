'use client'

import { useEffect } from 'react'
import { saveStripeAccountId } from '@/actions/stripe/connect'
import { useToast } from '@/hooks/use-toast'
import { useRouter, useSearchParams } from 'next/navigation'

export default function StripeCallbackPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { toast } = useToast()

  useEffect(() => {
    const accountId = searchParams.get('account_id')

    async function handleSave() {
      if (accountId) {
        const result = await saveStripeAccountId(accountId)
        if (result.success) {
          toast({
            title: 'Success',
            description: 'Stripe account connected successfully.',
          })
        } else {
          toast({
            title: 'Error',
            description: result.error,
            variant: 'destructive',
          })
        }
      } else {
        toast({
          title: 'Error',
          description: 'No account ID found.',
          variant: 'destructive',
        })
      }

      router.push('/user/settings')
    }

    handleSave()
  }, [searchParams, toast, router])

  return (
    <div className="flex h-screen items-center justify-center">
      <p>Connecting your Stripe account, please wait...</p>
    </div>
  )
}