import { Metadata } from 'next'
import { HOST } from '@/config'
import { getUser<PERSON>yHandler } from '@/actions/user'

type GenerateMetadataProps = {
  params: Promise<{ handler: string }>
}

export async function generateMetadata({
  params,
}: GenerateMetadataProps): Promise<Metadata> {
  const { handler } = await params
  const user = await getUserByHandler(handler)
  const fullName = `${user?.profile?.firstName} ${user?.profile?.lastName}`
  const title = `${fullName} | Templace`
  const description = user?.profile?.bio ?? ''
  const images = [
    {
      url: user?.avatar ?? `/thumb.jpeg`,
      width: 800,
      height: 600,
      alt: `${fullName}'s Avatar`,
    },
  ]

  return {
    title,
    description,
    metadataBase: new URL(HOST!),
    alternates: {
      canonical: `${HOST}/user/${handler}`,
      languages: {
        'en-US': `${HOST}/user/${handler}`,
      },
    },
    openGraph: {
      title,
      description,
      url: `${HOST}/user/${handler}`,
      siteName: title,
      images,
      locale: 'en_US',
      type: 'profile',
    },
    icons: {
      icon: '/favicon.ico',
    },
  }
}

type Props = React.PropsWithChildren

export default async function ProductLayout({ children }: Props) {
  return <div className="-my-2">{children}</div>
}
