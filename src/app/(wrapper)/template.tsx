import Modals from '@/components/common/Modals'
import Main from '@/components/structure/Main'
import Loading from '@/components/common/Loading'
import Content from '@/components/structure/Content'
import Wrapper from '@/components/structure/Wrapper'

type Props = React.PropsWithChildren

export default async function Template({ children }: Props) {
  return (
    <Wrapper>
      <Main>
        <Content>{children}</Content>
      </Main>
      <Modals />
      <Loading />
    </Wrapper>
  )
}
