import { getProductsByFlair, PlatformsEnum } from '@/actions/product'
import ExploreContent from '@/components/explore/ExploreContent'

async function getProductsByFlairLocal() {
  const productsAll = await getProductsByFlair(
    PlatformsEnum.SUBSCRIPTION,
    12,
    0,
  )
  const productsWebflow = await getProductsByFlair(
    `${PlatformsEnum.SUBSCRIPTION}|${PlatformsEnum.WEBFLOW}`,
    12,
    0,
  )
  const productsFramer = await getProductsByFlair(
    `${PlatformsEnum.SUBSCRIPTION}|${PlatformsEnum.FRAMER}`,
    12,
    0,
  )

  return {
    productsAll,
    productsWebflow,
    productsFramer,
  }
}

/**
 *
 */
export default async function ExploreSubscriptionsPage() {
  const { productsAll, productsWebflow, productsFramer } =
    await getProductsByFlairLocal()

  const tabs = [
    {
      key: 'all',
      label: 'All',
      flair: PlatformsEnum.SUBSCRIPTION,
      content: productsAll,
    },
    {
      key: PlatformsEnum.WEBFLOW,
      label: 'Webflow',
      flair: `${PlatformsEnum.SUBSCRIPTION}|${PlatformsEnum.WEBFLOW}`,
      content: productsWebflow,
    },
    {
      key: PlatformsEnum.FRAMER,
      label: 'Framer',
      flair: `${PlatformsEnum.SUBSCRIPTION}|${PlatformsEnum.FRAMER}`,
      content: productsFramer,
    },
  ]

  return (
    <div className="container py-8 mx-auto">
      <ExploreContent
        title="Subscriptions"
        subtitle="Access premium creator content with monthly subscriptions"
        tabs={tabs}
        defaultTab="all"
      />
    </div>
  )
}
