import Link from 'next/link'
import { redirect } from 'next/navigation'
import { getServerSession } from 'next-auth'
import TrendingTemplates from '@/components/common/TrendingTemplates'
import { Button } from '@/components/ui/button'
import HomeStructure from '@/components/common/HomeStructure'

export default async function Home() {
  const session = await getServerSession()

  if (!session) {
    redirect('/login')
  }

  return (
    <>
      <HomeStructure />
      <div className="flex flex-col mt-6 gap-6">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold">Trending Templates</h2>

          <Link href="/explore">
            <Button
              variant="secondary"
              className="gap-2 bg-gray-100 text-gray-700 hover:bg-gray-200 text-xs h-10 px-4 py-2"
            >
              Browse All
              <span className="sr-only">Explore all templates</span>
            </Button>
          </Link>
        </div>

        <TrendingTemplates total={6} />
      </div>
    </>
  )
}
