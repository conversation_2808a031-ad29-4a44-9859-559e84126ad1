import { getProductsByFlair, PlatformsEnum } from '@/actions/product'
import ProductGrid from '@/components/common/ProductGrid'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import ExploreContent from '@/components/explore/ExploreContent'

const getProductsByFlairLocal = async (flair: string) => {
  const productsAll = await getProductsByFlair(flair, 12, 0)
  const productsWebflow = await getProductsByFlair(
    `${PlatformsEnum.WEBFLOW}|${flair}`,
    12,
    0,
  )
  const productsFramer = await getProductsByFlair(
    `${PlatformsEnum.FRAMER}|${flair}`,
    12,
    0,
  )

  return {
    productsAll,
    productsWebflow,
    productsFramer,
  }
}

/**
 *
 */
export default async function ExploreTemplatesPage({ params }: any) {
  const flairId = 'template'
  const { productsAll, productsWebflow, productsFramer } =
    await getProductsByFlairLocal(flairId)

  const tabs = [
    {
      key: 'all',
      label: 'All',
      flair: flairId,
      content: productsAll,
    },
    {
      key: PlatformsEnum.WEBFLOW,
      label: 'Webflow',
      flair: `${PlatformsEnum.WEBFLOW}|${flairId}`,
      content: productsWebflow,
    },
    {
      key: PlatformsEnum.FRAMER,
      label: 'Framer',
      flair: `${PlatformsEnum.FRAMER}|${flairId}`,
      content: productsFramer,
    },
  ]

  return (
    <div className="container py-8 mx-auto">
      <ExploreContent
        title="Templates"
        subtitle="Discover ready-to-use templates for your next project"
        tabs={tabs}
        defaultTab="all"
      />
    </div>
  )
}
