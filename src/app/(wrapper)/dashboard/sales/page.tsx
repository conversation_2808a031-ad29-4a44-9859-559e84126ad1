import Link from 'next/link'
import { formatRelative, isThisWeek, isThisMonth } from 'date-fns'
import lodash from 'lodash'

import { getPaymentLogBySeller } from '@/actions/payment'
import { PaymentPrice } from '@/actions/price'
import { getSessionUser } from '@/actions/auth'
import centsToDollars from '@/utils/cents-to-dollars'
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from '@/components/ui/table'
import { TemplateCell } from '@/components/common/TemplateCell'
import { ImageSet } from '@/actions/image'
import { redirect } from 'next/navigation'

export default async function ProfileEarningsPage() {
  const user = await getSessionUser()
  const earnings = await getPaymentLogBySeller(user?.id?.toString())

  if (user?.hats?.find(hat => hat.slug === 'member')) {
    return redirect('/404')
  }

  const amount =
    earnings?.reduce(
      (acc, item) => acc + ((item.product?.price as PaymentPrice)?.amount ?? 0),
      0,
    ) ?? 0

  const topProducts = lodash(earnings)
    .groupBy('product.id')
    .map((items: any, id: any) => ({
      id,
      name: items?.[0].product?.name ?? 'Unknown',
      total: (items?.[0].product?.price?.amount ?? 0) * items.length,
    }))
    .orderBy('total', 'desc')
    .take(5)
    .value()

  const lastSaleThisWeek = earnings.filter(item => {
    const date = new Date(item.created)
    const today = new Date()
    return isThisWeek(date)
  })

  const positiveFeedback =
    earnings.filter(item => {
      const date = new Date(item.created)
      return isThisMonth(date)
    }).length >= (lastSaleThisWeek?.length || 0)

  return (
    <div className="container max-w-4xl mx-auto">
      <div className="grid grid-cols-3 gap-4 mb-8 mt-16">
        <div className="p-4 bg-background rounded-lg ring-1">
          <h3 className="text-md font-medium mb-2">Total Items Sold</h3>
          <p className="text-6xl font-bold">{earnings?.length || 0}</p>
        </div>
        <div className="p-4 bg-background rounded-lg ring-1">
          <h3 className="text-lg font-medium mb-2">Top 5 Items</h3>
          <ul className="space-y-2">
            {topProducts.map(item => (
              <li
                key={item.id.toString()}
                className="flex justify-between text-xs"
              >
                <span>{item.name}</span>
                <span>{centsToDollars(item.total)}</span>
              </li>
            ))}
          </ul>
        </div>
        <div className="p-4 bg-background rounded-lg ring-1">
          <h3 className="text-lg font-medium mb-2">Total Balance</h3>
          <div className="flex items-baseline">
            <span className="text-3xl font-bold">{centsToDollars(amount)}</span>
          </div>
          {positiveFeedback && lastSaleThisWeek?.length ? (
            <span className="ml-2 text-green-500 text-xs">
              nice! +{lastSaleThisWeek?.length} sales this week
            </span>
          ) : null}
        </div>
      </div>

      <Table>
        <TableHeader>
          <TableRow className="text-xs">
            <TableHead>Id</TableHead>
            <TableHead>Product</TableHead>
            <TableHead>Customer</TableHead>
            <TableHead>Date</TableHead>
            <TableHead className="text-right">Amount</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody className="text-xs">
          {earnings.map(payment => (
            <TableRow
              key={payment.id.toString()}
              className="transition-colors duration-200 cursor-pointer hover:bg-gray-50"
            >
              <TableCell className="py-3 cursor-pointer hover:bg-gray-50/50">
                {payment.id.toString().split(':').pop()}
              </TableCell>
              <TableCell>
                <TemplateCell
                  name={payment.product?.name}
                  thumbnail={
                    (payment.product?.imageSet as ImageSet)?.images?.[0]?.url ?? ''
                  }
                  isBundle={false}
                  showBundleDetails={false}
                  isExpanded={false}
                  templatesCount={0}
                  isSelected={false}
                  isTemplateSelection={false}
                />
              </TableCell>
              <TableCell>
                <Link
                  href={`/user/${payment.buyer?.handler}`}
                  className="hover:text-primary"
                >
                  @{payment.buyer?.handler}
                </Link>
              </TableCell>
              <TableCell>
                {formatRelative(new Date(payment.created), new Date())}
              </TableCell>
              <TableCell className="font-bold">
                {centsToDollars(
                  (payment.product?.price as PaymentPrice)?.amount ?? 0,
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
