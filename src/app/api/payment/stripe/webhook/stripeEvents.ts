import Stripe from 'stripe'

import { saveStripePaymentEvent } from '@/actions/stripe/saveStripePaymentEvent'
import logger from '@/utils/logger'

export async function stripeEvents(event: Stripe.Event) {
  let status = 200
  let response = {}

  logger.trace(`stripeEvents: stripe.${event.type}`)

  try {
    response = {
      status: 'success',
      url: event.type,
    }

    await saveStripePaymentEvent({
      type: `stripe.${event.type}`,
      objectId: 'id' in event.data.object ? event.data.object.id : null,
      eventId: event.id,
      data: JSON.stringify(event.data),
    })

    return { response, status }
  } catch (err) {
    status = 500
    response = { status: 'error', error: err }
    return { response, status }
  }
}
