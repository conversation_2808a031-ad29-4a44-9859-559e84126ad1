import Stripe from 'stripe'
import logger from '@/utils/logger'
import { NextResponse } from 'next/server'
import { STRIPE_SECRET_KEY, STRIPE_WEBHOOK_SECRET_KEY } from '@/config'
import { stripeEvents } from './stripeEvents'

const stripe = new Stripe(STRIPE_SECRET_KEY)

export const dynamic = 'force-dynamic'
export const fetchCache = 'default-no-store'

export async function POST(req) {
  const text = await req.text()
  const payload = JSON.parse(text)

  const signature = req.headers.get('stripe-signature')
  const created = new Date(
    parseInt(payload?.created, 10) * 1000,
  ).toLocaleDateString()

  let event

  try {
    event = stripe.webhooks.constructEvent(
      text,
      signature,
      STRIPE_WEBHOOK_SECRET_KEY,
    )
  } catch (err) {
    logger.error(`Webhook signature verification failed:`)
    logger.error(`${err.toString()}`)
    return new NextResponse({ status: 'error', error: err }, { status: 400 })
  }

  const { response, status } = await stripeEvents(event, created)

  if (response) {
    return new NextResponse(response, { status })
  }

  return new NextResponse(
    { status: 'error', error: new Error('Unexpected error') },
    { status: 500 },
  )
}
