import { sendPurchaseComplete } from '@/actions/mailing'
import { continuePaymentLog } from '@/actions/payment/createNewPaymentLog'
import { getEventFromCache } from '@/actions/stripe'
import { getLogDataFromSession } from '@/actions/stripe/get-log-data-from-session'
import { saveStripePaymentEvent } from '@/actions/stripe/saveStripePaymentEvent'
import { set } from '@/utils/jwt'
import logger from '@/utils/logger'
import { serverTrack } from '@/utils/track'
import { redirect, RedirectType } from 'next/navigation'
import { NextRequest } from 'next/server'

export const dynamic = 'force-dynamic'
export const fetchCache = 'default-no-store'

export async function GET(req: NextRequest) {
  const url = new URL(req.url)
  const sessionId = url.searchParams.get('session_id')
  logger.trace(`session_id: ${sessionId}`)

  if (!sessionId) {
    throw new Error('Missing session_id')
  }

  let tries = 3
  while (tries > 0) {
    try {
      logger.info(
        `Checking for completed event, try ${3 - tries}/3 ${sessionId}`,
      )
      const completedEvent = await getEventFromCache([
        'stripe.checkout.session.completed',
        sessionId,
      ])
      if (completedEvent) {
        break
      }
    } catch (err) {
      tries--
      if (tries > -1) {
        logger.error(err)
        await new Promise(resolve => setTimeout(resolve, 3000))
      } else {
        throw err
      }
    }
  }

  await saveStripePaymentEvent({
    type: 'stripe.dsm.checkout.finished',
    eventId: sessionId,
    sessionId,
  })

  const { customer, invoice, paymentIntent, invoicePdf, invoiceUrl, metadata } =
    await getLogDataFromSession(sessionId!)

  const {
    productId,
    buyerUserId,
    sellerUserId,
    externalState,
    paymentPriceId,
    paymentLogId,
  } = metadata

  const logData = await continuePaymentLog(paymentLogId, {
    sessionId,
    paymentPriceId,
    externalState,
    customer,
    invoice,
    paymentIntent,
    invoicePdf,
    invoiceUrl,
  })

  // vercel analytics
  serverTrack('payment_complete', {
    sellerUserId,
    buyerUserId,
    productId,
    paymentIntent,
  })

  await sendPurchaseComplete(logData)

  const urlState = await set({
    id: logData?.id,
    hashId: logData?.hashId,
    productId,
    buyerUserId,
    paymentPriceId,
    externalState,
  })

  return redirect(`/payment/complete?state=${urlState}`, RedirectType.replace)
}
