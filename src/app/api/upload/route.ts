import { NextRequest, NextResponse } from 'next/server'
import { getUploadToken, getUploadUrl } from '@/actions/file'
import { getSessionUser } from '@/actions/auth'
import { getUserById } from '@/actions/user'

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': 'ORIGIN',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers':
        'Content-Type, Authorization, X-Requested-With',
      'Access-Control-Allow-Credentials': 'true',
    },
  })
}

export async function POST(request: NextRequest) {
  try {
    const sessionUser = await getSessionUser()
    const user = await getUserById(sessionUser?.id?.toString())
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const folder = (formData.get('folder') as string) || 'files'
    const meta = JSON.parse(formData.get('meta') as string)

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    const token = await getUploadToken(meta)
    const { url: signedUrl, fileCreated } = await getUploadUrl(
      folder,
      file.name,
      file.type,
      token,
      meta,
    )

    return NextResponse.json(
      {
        signedUrl,
        fileCreated,
      },
      {
        headers: {
          'Access-Control-Allow-Origin': 'ORIGIN',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers':
            'Content-Type, Authorization, X-Requested-With',
          'Access-Control-Allow-Credentials': 'true',
        },
      },
    )
  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { error: 'Upload failed' },
      {
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': 'ORIGIN',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers':
            'Content-Type, Authorization, X-Requested-With',
          'Access-Control-Allow-Credentials': 'true',
        },
      },
    )
  }
}
