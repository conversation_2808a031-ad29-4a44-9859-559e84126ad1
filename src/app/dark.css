@media (prefers-color-scheme: dark) {
  :root {
    /* <PERSON> */
    --gray-dark: 240 10% 3.9%; /* #0A0A0A */
    --gray-medium: 240 3.7% 15.9%; /* #27272A */
    --gray-muted: 240 5% 64.9%; /* #A1A1AA */
    --gray-super-light: 240 4.8% 95.9%; /* #F4F4F5 */
    --off-white: 0 0% 98%; /* #FAFAFA */
    --white: 0 0% 100%; /* #FFFFFF */

    --semantic-blue: 220 70% 50%; /* #3B82F6 */
    --semantic-green: 160 60% 45%; /* #059669 */
    --semantic-orange: 30 80% 55%; /* #F97316 */
    --semantic-purple: 280 65% 60%; /* #9333EA */
    --semantic-pink: 340 75% 55%; /* #EC4899 */
    --semantic-red: 0 62.8% 30.6%; /* #991B1B */

    /* Semantic Colors */
    --background: var(--gray-dark);
    --foreground: var(--white);
    --card: var(--gray-dark);
    --card-foreground: var(--white);
    --popover: var(--gray-dark);
    --popover-foreground: var(--white);
    --primary: var(--gray-dark);
    --primary-foreground: var(--off-white);
    --secondary: var(--gray-medium);
    --secondary-foreground: var(--off-white);
    --muted: var(--gray-medium);
    --muted-foreground: var(--gray-muted);
    --accent: var(--gray-medium);
    --accent-foreground: var(--off-white);
    --destructive: var(--semantic-red);
    --destructive-foreground: var(--off-white);
    --border: var(--gray-medium);
    --input: var(--gray-medium);
    --ring: var(--gray-medium);
    --chart-1: var(--semantic-blue);
    --chart-2: var(--semantic-green);
    --chart-3: var(--semantic-orange);
    --chart-4: var(--semantic-purple);
    --chart-5: var(--semantic-pink);
    --sidebar-background: 240 5.9% 10%; /* #18181B */
    --sidebar-foreground: var(--gray-super-light);
    --sidebar-primary: var(--gray-medium);
    --sidebar-primary-foreground: var(--white);
    --sidebar-accent: var(--gray-dark);
    --sidebar-accent-foreground: var(--gray-super-light);
    --sidebar-border: var(--gray-medium);
    --sidebar-ring: var(--gray-dark);

    /* Radius */
    --radius: 0.5rem;
  }
}