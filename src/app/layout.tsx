import { GoogleAnalytics, GoogleTagManager } from '@next/third-parties/google'
import { Analytics } from '@vercel/analytics/react'
import { Inter_Tight } from 'next/font/google'
import Script from 'next/script'

import SessionProvider from '@/components/SessionProvider'
import { Toaster } from '@/components/ui/toaster'
import { PageTitleProvider } from '@/components/structure/PageTitleProvider'
import {
  GOOGLE_ANALYTICS,
  GOOGLE_TAG_MANAGER,
  HOST,
  VERCEL_ENV,
} from '@/config'
import { getFullAuthSession } from '@/io/next-auth-config'
import ReactQueryProvider from '@/providers/ReactQueryProvider'

import './globals.css'

import UserGlobalFetcher from '@/components/UserGlobalFetcher'

const interTight = Inter_Tight({ subsets: ['latin'] })

const title =
  'Templace | Buy and Sell Premium Webflow & Framer Templates and Components'
const description =
  'The ultimate marketplace for Webflow and Framer templates. Buy, sell, and collaborate with top creators in our exclusive, high-quality community.'

export const metadata = {
  title,
  description,
  metadataBase: new URL(HOST!),
  alternates: {
    canonical: '/',
    languages: {
      'en-US': '/en-US',
    },
  },
  openGraph: {
    title,
    description,
    url: HOST,
    siteName: title,
    images: [
      {
        url: `/thumb.jpeg`,
        width: 800,
        height: 600,
        alt: 'tem.place',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  icons: {
    icon: '/favicon.ico',
  },
}

export const viewport = {
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: 'white' },
    { media: '(prefers-color-scheme: dark)', color: 'black' },
  ],
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: 'cover',
}

export default async function RootLayout({
  children,
}: React.PropsWithChildren) {
  const session = await getFullAuthSession()

  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <Script id="remove-extension-attributes" strategy="beforeInteractive">
          {`
            // Remove extension attributes that cause hydration mismatches
            document.addEventListener('DOMContentLoaded', () => {
              document.body.removeAttribute('data-new-gr-c-s-check-loaded');
              document.body.removeAttribute('data-gr-ext-installed');
            });
          `}
        </Script>
      </head>
      <body className={interTight.className} suppressHydrationWarning>
        <SessionProvider session={session}>
          <ReactQueryProvider>
            <PageTitleProvider>
              <UserGlobalFetcher />
              {children}
            </PageTitleProvider>
          </ReactQueryProvider>
          <Toaster />
        </SessionProvider>
        {GOOGLE_ANALYTICS && <GoogleAnalytics gaId={GOOGLE_ANALYTICS} />}
        {GOOGLE_TAG_MANAGER && <GoogleTagManager gtmId={GOOGLE_TAG_MANAGER} />}
        {VERCEL_ENV === 'production' && <Analytics />}
      </body>
    </html>
  )
}
