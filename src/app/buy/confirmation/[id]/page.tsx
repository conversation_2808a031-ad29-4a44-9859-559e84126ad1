import type { Metadata, ResolvingMetadata } from 'next'
import { Coffee } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'

import { checkIfUserBoughtAlready } from '@/actions/payment'
import { getProductById, ProductStatus } from '@/actions/product'
import GoogleLoginButton from '@/components/common/GoogleLoginButton'
import LeanWrapper from '@/components/structure/LeanWrapper'
import TemplaceLogo from '@/components/common/TemplaceLogo'
import PlatformWidget from '@/components/products/PlatformWidget'
import Main from '@/components/structure/Main'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card'
import UserWidget from '@/components/UserWidget'
import { getFullAuthSession } from '@/io/next-auth-config'
import centsToDollars from '@/utils/cents-to-dollars'

import { HOST } from '@/config'

import { FileSet } from '@/actions/file'
import MediaBox from '@/components/common/MediaBox'
import { getPlatform } from '@/utils/get-platform'

export async function generateMetadata(
  { params }: any,
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const { id } = await params
  const product = await getProductById(id, ProductStatus.PUBLISHED)
  const parentMetadata = await parent
  const parentImages = parentMetadata?.openGraph?.images || []

  const mediaFiles: string[] =
    product?.fileSet?.files
      ?.filter(
        file =>
          file.url &&
          (file.mimeType?.startsWith('video/') ||
            file.mimeType?.startsWith('image/')),
      )
      .map(item => item.url!) ?? []

  return {
    title: `${product?.name} | Transfer made by Templace, Buy and Sell Premium Curated Templates and Components`,
    openGraph: {
      images: [...mediaFiles, ...parentImages],
    },
  }
}

export default async function Confirmation({ params, searchParams }: any) {
  const { id } = await params
  const { externalState } = await searchParams
  const product = await getProductById(id, ProductStatus.PUBLISHED)
  const session = await getFullAuthSession()

  let alreadyBought = false
  if (session?.user?.id) {
    alreadyBought = await checkIfUserBoughtAlready(session.user.id, id)
  }

  const media =
    (product?.fileSet as FileSet)?.files?.find(
      file =>
        file.mimeType?.startsWith('video/') ||
        file.mimeType?.startsWith('image/'),
    ) ?? {}

  return (
    <Main>
      <LeanWrapper>
        <div className="flex flex-col gap-8 justify-center items-center h-screen">
          <TemplaceLogo className="w-16 h-16 object-contain" />

          <h1 className="text-3xl md:text-4xl font-bold w-[26rem] text-center text-foreground/40">
            This transfer is made by{' '}
            <span className="text-foreground">Templace</span>
          </h1>

          <p className="w-1/2 text-foreground/40 text-center text-xs">
            This asset will be transferred to your platform account within
            approximately 24 hours, with not needing to send emails,
            complications, or hassles involved.
          </p>

          <Card
            className="w-[32rem] h-46"
            image={
              <div className="w-full h-full">
                <MediaBox
                  media={media}
                  ratio={1 / 1}
                  width={170}
                  height={170}
                />
              </div>
            }
          >
            <CardHeader>
              {getPlatform(product?.platforms) ? (
                <PlatformWidget
                  className="w-22 h-auto object-contain"
                  platform={getPlatform(product?.platforms)}
                />
              ) : null}
            </CardHeader>

            <CardContent>
              {getPlatform(product?.platforms) ? (
                <div className="w-full flex flex-row gap-1 justify-between">
                  <span className="text-md text-foreground font-semibold">
                    {product?.name}
                    <br />
                    {product?.price.amount
                      ? centsToDollars(product.price.amount)
                      : null}
                  </span>
                </div>
              ) : null}
            </CardContent>

            <CardFooter>
              {product?.owner?.avatar && product?.owner?.handler ? (
                <div className="w-full flex flex-row gap-1 justify-between">
                  <Badge variant="grey">
                    <Coffee width={12} height={12} />
                    Waiting
                  </Badge>
                  <UserWidget
                    className="w-22 h-auto object-contain"
                    avatar={product?.owner?.avatar}
                    name={`@${product?.owner?.handler}`}
                  />
                </div>
              ) : null}
            </CardFooter>
          </Card>

          {alreadyBought ? (
            <Button asChild variant="outline" size="lg">
              <Link
                type="button"
                href={`${HOST}/dashboard/purchases/#product/${id}`}
              >
                You already purchased this asset
              </Link>
            </Button>
          ) : (
            <>
              {session?.user?.id ? (
                <Button size="lg">
                  {/* <a> tag is necessary for the link to work, due to stripe redirect */}
                  <a
                    type="button"
                    href={`${HOST}/buy/${id}?externalState=${externalState}`}
                  >
                    Go to checkout
                  </a>
                </Button>
              ) : (
                <GoogleLoginButton
                  redirect={`${HOST}/buy/${id}?externalState=${externalState}`}
                />
              )}
            </>
          )}

          <Image
            src="/assets/art-powered-stripe.svg"
            alt="Powered by Stripe"
            width={154}
            height={42}
          />
        </div>
      </LeanWrapper>
    </Main>
  )
}
