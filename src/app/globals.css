@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* Light Theme */
  :root {
    --gray-dark: 240 10% 3.9%; /* #0A0A0A */
    --gray-medium: 240 3.7% 15.9%; /* #27272A */
    --gray-muted: 240 5% 64.9%; /* #A1A1AA */
    --gray-super-light: 240 4.8% 95.9%; /* #F4F4F5 */
    --off-white: 0 0% 98%; /* #FAFAFA */
    --white: 0 0% 100%; /* #FFFFFF */

    --semantic-blue: 220 70% 50%; /* #3B82F6 */
    --semantic-green: 160 60% 45%; /* #059669 */
    --semantic-orange: 30 80% 55%; /* #F97316 */
    --semantic-purple: 280 65% 60%; /* #9333EA */
    --semantic-pink: 340 75% 55%; /* #EC4899 */
    --semantic-red: 0 62.8% 30.6%; /* #991B1B */

    /* Base Colors */
    --background: var(--white);
    --foreground: var(--gray-dark);

    /* Muted Colors */
    --muted: var(--gray-super-light);
    --muted-foreground: var(--gray-muted);

    /* Card Colors */
    --card: var(--white);
    --card-foreground: var(--gray-dark);

    /* Popover Colors */
    --popover: var(--white);
    --popover-foreground: var(--gray-dark);

    /* Primary Colors */
    --primary: var(--gray-dark);
    --primary-foreground: var(--off-white);

    /* Secondary Colors */
    --secondary: var(--gray-super-light);
    --secondary-foreground: var(--gray-dark);

    /* Accent Colors */
    --accent: var(--gray-super-light);
    --accent-foreground: var(--gray-dark);

    /* Destructive Colors */
    --destructive: var(--semantic-red);
    --destructive-foreground: var(--off-white);

    /* Border Colors */
    --border: var(--gray-super-light); /* #E4E4E7 */
    --input: var(--gray-super-light); /* #E4E4E7 */
    --ring: var(--gray-super-light); /* #E4E4E7 */

    /* Chart Colors */
    --chart-1: var(--semantic-orange);
    --chart-2: var(--semantic-green);
    --chart-3: var(--semantic-blue);
    --chart-4: var(--semantic-purple);
    --chart-5: var(--semantic-pink);

    /* Sidebar Colors */
    --sidebar-background: var(--white);
    --sidebar-foreground: var(--gray-dark);
    --sidebar-primary: var(--gray-dark);
    --sidebar-primary-foreground: var(--off-white);
    --sidebar-accent: var(--gray-super-light);
    --sidebar-accent-foreground: var(--gray-dark);
    --sidebar-border: var(--gray-super-light);
    --sidebar-ring: var(--gray-dark);

    /* Radius */
    --radius: 0.5rem;
  }
}

* {
  color: inherit;
}

html {
  scroll-behavior: smooth;
}

/* body {
  @apply text-foreground;
} */

/* a:not([type='button']) {
  @apply text-gray-600 hover:underline hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100;
} */

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  .horizontal-lock {
    @apply overflow-x-hidden;
  }

  /* Prevent body overflow from being changed by Radix UI components */
  html body[data-scroll-locked] {
    overflow: visible !important;
    margin-right: 0 !important;
  }
}
